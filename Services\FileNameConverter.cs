using System;
using System.Globalization;
using System.IO;
using System.Windows.Data;

namespace DatalogDrawing.Views
{
    public class FileNameConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return "未知文件";
            
            string path = value.ToString();
            if (string.IsNullOrEmpty(path)) return "未知文件";
            
            try
            {
                return Path.GetFileName(path);
            }
            catch
            {
                return path;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 