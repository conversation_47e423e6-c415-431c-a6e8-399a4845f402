using System.Windows.Controls;
using DatalogDrawing.ViewModels;

namespace DatalogDrawing.Views
{
    public partial class RecipeView : UserControl
    {
        private RecipeViewModel _viewModel;

        public RecipeView()
        {
            InitializeComponent();
            // 如果没有外部设置DataContext，则创建默认的ViewModel
            if (DataContext == null)
            {
                _viewModel = new RecipeViewModel();
                DataContext = _viewModel;
            }
            else if (DataContext is RecipeViewModel vm)
            {
                _viewModel = vm;
            }

            // 设置动态列
            this.Loaded += RecipeView_Loaded;

            // 订阅动态列生成事件
            if (_viewModel != null)
            {
                _viewModel.GenerateDataGridColumns += OnGenerateDataGridColumns;
            }
        }

        private void RecipeView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            // 在界面加载完成后设置动态列
            if (_viewModel != null)
            {
                _viewModel.SetupDataGridColumns(RecipeStepsDataGrid);
            }
        }

        private void OnGenerateDataGridColumns(DataGrid dataGrid)
        {
            // 重新生成DataGrid列
            if (_viewModel != null)
            {
                _viewModel.SetupDataGridColumns(RecipeStepsDataGrid);
            }
        }
    }
}
