using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using DatalogDrawing.Models;
using Newtonsoft.Json;
using System.IO;

namespace DatalogDrawing.Views
{
    public partial class PADataView : UserControl
    {
        private ObservableCollection<ParticleSizeData> _particleData;
        private List<ParticleColorMapping> _colorMappings;
        private bool _isPreviewMode = false;

        public PADataView()
        {
            InitializeComponent();
            _particleData = new ObservableCollection<ParticleSizeData>();
            ParticleDataGrid.ItemsSource = _particleData;
            
            // 加载颜色配置
            LoadColorConfig();
            
            // 监听数据变化
            this.DataContextChanged += PADataView_DataContextChanged;
        }

        private void LoadColorConfig()
        {
            try
            {
                var configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PAconfig.json");
                if (File.Exists(configPath))
                {
                    var jsonContent = File.ReadAllText(configPath);
                    var config = JsonConvert.DeserializeObject<PAConfig>(jsonContent);

                    // 转换PAconfig格式到标准格式
                    if (config?.DisplayConfig?.ParticleColorMapping != null)
                    {
                        _colorMappings = config.DisplayConfig.ParticleColorMapping.Select(m => new ParticleColorMapping
                        {
                            MinSize = m.MinSize,
                            MaxSize = m.MaxSize,
                            Color = m.Color,
                            Name = m.Name
                        }).ToList();
                    }
                    else
                    {
                        _colorMappings = GetDefaultColorMappings();
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"成功加载颜色配置，共{_colorMappings.Count}个颜色映射");
                    foreach (var mapping in _colorMappings)
                    {
                        System.Diagnostics.Debug.WriteLine($"  {mapping.Name}: {mapping.MinSize}-{mapping.MaxSize} -> {mapping.Color}");
                    }
                }
                else
                {
                    _colorMappings = GetDefaultColorMappings();
                    System.Diagnostics.Debug.WriteLine("配置文件不存在，使用默认颜色配置");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载颜色配置失败: {ex.Message}");
                _colorMappings = GetDefaultColorMappings();
            }
        }

        public void ReloadColorConfig()
        {
            LoadColorConfig();
            // 如果有数据，重新应用样式
            if (_particleData.Count > 0)
            {
                UpdateDataGridStyle();
            }
        }

        private List<ParticleColorMapping> GetDefaultColorMappings()
        {
            return new List<ParticleColorMapping>
            {
                new ParticleColorMapping { MinSize = 0.0, MaxSize = 0.06, Color = "#FFFFFF", Name = "White" },
                new ParticleColorMapping { MinSize = 0.06, MaxSize = 0.09, Color = "#ADD8E6", Name = "LightBlue" },
                new ParticleColorMapping { MinSize = 0.09, MaxSize = 0.12, Color = "#00008B", Name = "DarkBlue" },
                new ParticleColorMapping { MinSize = 0.12, MaxSize = 0.16, Color = "#FFFF00", Name = "Yellow" },
                new ParticleColorMapping { MinSize = 0.16, MaxSize = 0.3, Color = "#90EE90", Name = "LightGreen" },
                new ParticleColorMapping { MinSize = 0.3, MaxSize = 0.4, Color = "#00FFFF", Name = "Cyan" },
                new ParticleColorMapping { MinSize = 0.4, MaxSize = 0.5, Color = "#800080", Name = "Purple" },
                new ParticleColorMapping { MinSize = 0.5, MaxSize = 999.0, Color = "#FF0000", Name = "Red" }
            };
        }

        private void PADataView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 取消订阅旧的ViewModel
            if (e.OldValue is ViewModels.PADataViewModel oldViewModel)
            {
                oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }

            // 订阅新的ViewModel
            if (e.NewValue is ViewModels.PADataViewModel newViewModel)
            {
                newViewModel.PropertyChanged += ViewModel_PropertyChanged;

                // 立即检查是否有数据需要显示
                if (newViewModel.PreData?.Count > 0 || newViewModel.AfterData?.Count > 0 || newViewModel.AddData?.Count > 0)
                {
                    UpdateParticleVisualization(newViewModel);
                }
            }
        }

        private void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (sender is ViewModels.PADataViewModel viewModel)
            {
                switch (e.PropertyName)
                {
                    case nameof(ViewModels.PADataViewModel.PreData):
                    case nameof(ViewModels.PADataViewModel.AfterData):
                    case nameof(ViewModels.PADataViewModel.AddData):
                        UpdateParticleVisualization(viewModel);
                        break;
                }
            }
        }

        private void UpdateParticleVisualization(ViewModels.PADataViewModel viewModel)
        {
            try
            {
                var preData = viewModel.PreData ?? new List<PAData>();
                var afterData = viewModel.AfterData ?? new List<PAData>();
                var addData = viewModel.AddData ?? new List<PAData>();

                UpdateParticleData(preData, afterData, addData);

                // Add数据就是新增的颗粒，直接使用，不进行距离过滤
                var addedParticles = addData.Where(p => p.DeltaDSIZE > 0).ToList();

                DrawParticleCharts(preData, afterData, addedParticles);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新颗粒可视化时出错: {ex.Message}");
            }
        }

        private List<PAData> FilterDuplicateParticles(List<PAData> particles, double distanceThreshold)
        {
            if (particles == null || particles.Count <= 1)
                return particles;

            var filteredParticles = new List<PAData>();
            var usedIndices = new HashSet<int>();

            for (int i = 0; i < particles.Count; i++)
            {
                if (usedIndices.Contains(i))
                    continue;

                var currentParticle = particles[i];
                filteredParticles.Add(currentParticle);
                usedIndices.Add(i);

                // 检查其他点是否与当前点距离太近
                for (int j = i + 1; j < particles.Count; j++)
                {
                    if (usedIndices.Contains(j))
                        continue;

                    var otherParticle = particles[j];
                    var distance = CalculateDistance(currentParticle, otherParticle);

                    if (distance <= distanceThreshold)
                    {
                        usedIndices.Add(j);
                        System.Diagnostics.Debug.WriteLine($"过滤重复点: 点{i}和点{j}距离{distance:F2}，小于阈值{distanceThreshold}");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"过滤前颗粒数: {particles.Count}, 过滤后颗粒数: {filteredParticles.Count}");
            return filteredParticles;
        }

        private double CalculateDistance(PAData particle1, PAData particle2)
        {
            var dx = particle1.XREL - particle2.XREL;
            var dy = particle1.YREL - particle2.YREL;
            return Math.Sqrt(dx * dx + dy * dy);
        }

        private void UpdateParticleData(List<PAData> preData, List<PAData> afterData, List<PAData> addData)
        {
            System.Diagnostics.Debug.WriteLine("开始更新颗粒数据");
            _particleData.Clear();

            // Add数据已经通过算法计算得出，直接使用，不进行距离过滤
            // 只过滤DeltaDSIZE > 0的数据（确保是有效的新增颗粒）
            var addedParticles = addData.Where(p => p.DeltaDSIZE > 0).ToList();

            // 从配置文件获取尺寸范围
            var sizeRanges = GetSizeRangesFromConfig();
            System.Diagnostics.Debug.WriteLine($"获取到 {sizeRanges.Count} 个尺寸范围");

            foreach (var range in sizeRanges)
            {
                var beforeCount = preData.Count(p => p.DSIZE >= range.Min && p.DSIZE < range.Max);
                var afterCount = afterData.Count(p => p.DSIZE >= range.Min && p.DSIZE < range.Max);
                var delta = afterCount - beforeCount;

                // 计算Add数据中各尺寸范围的颗粒数量
                var addCount = addedParticles.Count(p => p.DSIZE >= range.Min && p.DSIZE < range.Max);

                var particleData = new ParticleSizeData
                {
                    SizeRange = range.Name,
                    Before = beforeCount,
                    After = afterCount,
                    Delta = delta,
                    Add = addCount
                };

                System.Diagnostics.Debug.WriteLine($"添加数据行: {range.Name} - Before:{beforeCount}, After:{afterCount}, Delta:{delta}, Add:{addCount}");
                _particleData.Add(particleData);
            }

            // 添加总计行
            _particleData.Add(new ParticleSizeData
            {
                SizeRange = "Total",
                Before = preData.Count,
                After = afterData.Count,
                Delta = afterData.Count - preData.Count,
                Add = addedParticles.Count
            });

            System.Diagnostics.Debug.WriteLine($"总共添加了 {_particleData.Count} 行数据");
            
            // 动态更新表格样式
            UpdateDataGridStyle();
        }

        private void UpdateDataGridStyle()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始更新DataGrid样式");
                var style = new Style(typeof(DataGridRow));

                if (_colorMappings != null)
                {
                    System.Diagnostics.Debug.WriteLine($"颜色映射数量: {_colorMappings.Count}");
                    foreach (var mapping in _colorMappings)
                    {
                        var rangeName = GetRangeName(mapping);
                        System.Diagnostics.Debug.WriteLine($"创建样式触发器: {rangeName} -> {mapping.Color}");
                        
                        var trigger = new DataTrigger
                        {
                            Binding = new System.Windows.Data.Binding("SizeRange"),
                            Value = rangeName
                        };

                        // 设置背景色
                        trigger.Setters.Add(new Setter(DataGridRow.BackgroundProperty, 
                            new SolidColorBrush((Color)ColorConverter.ConvertFromString(mapping.Color))));

                        // 根据颜色亮度设置前景色
                        var foregroundColor = GetContrastColor(mapping.Color);
                        trigger.Setters.Add(new Setter(DataGridRow.ForegroundProperty, 
                            new SolidColorBrush(foregroundColor)));

                        style.Triggers.Add(trigger);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("颜色映射为空，使用默认配置");
                }

                // 添加总计行的特殊样式
                var totalTrigger = new DataTrigger
                {
                    Binding = new System.Windows.Data.Binding("SizeRange"),
                    Value = "Total"
                };
                totalTrigger.Setters.Add(new Setter(DataGridRow.BackgroundProperty, 
                    new SolidColorBrush(Color.FromRgb(64, 64, 64))));
                totalTrigger.Setters.Add(new Setter(DataGridRow.ForegroundProperty, 
                    Brushes.White));
                totalTrigger.Setters.Add(new Setter(DataGridRow.FontWeightProperty, 
                    FontWeights.Bold));
                style.Triggers.Add(totalTrigger);

                ParticleDataGrid.RowStyle = style;
                System.Diagnostics.Debug.WriteLine("DataGrid样式更新完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新表格样式失败: {ex.Message}");
            }
        }

        private string GetRangeName(ParticleColorMapping mapping)
        {
            if (mapping.MinSize == 0.0 && mapping.MaxSize == 0.06)
                return "00.00-00.06";
            if (mapping.MinSize == 0.5 && mapping.MaxSize == 999.0)
                return "Saturated";
            
            // 使用与GetSizeRangesFromConfig相同的格式化方式
            return $"{mapping.MinSize:00.00}-{mapping.MaxSize:00.00}";
        }

        private Color GetContrastColor(string hexColor)
        {
            try
            {
                var color = (Color)ColorConverter.ConvertFromString(hexColor);
                var brightness = (color.R * 299 + color.G * 587 + color.B * 114) / 1000;
                return brightness > 128 ? Colors.Black : Colors.White;
            }
            catch
            {
                return Colors.Black;
            }
        }

        private List<dynamic> GetSizeRangesFromConfig()
        {
            var ranges = new List<dynamic>();
            
            if (_colorMappings != null)
            {
                foreach (var mapping in _colorMappings)
                {
                    var rangeName = GetRangeName(mapping);
                    ranges.Add(new { Min = mapping.MinSize, Max = mapping.MaxSize, Name = rangeName });
                }
            }
            else
            {
                // 默认尺寸范围 - 确保与GetRangeName方法生成的名称一致
                ranges = new List<dynamic>
                {
                    new { Min = 0.0, Max = 0.06, Name = "00.00-00.06" },
                    new { Min = 0.06, Max = 0.09, Name = "00.06-00.09" },
                    new { Min = 0.09, Max = 0.12, Name = "00.09-00.12" },
                    new { Min = 0.12, Max = 0.16, Name = "00.12-00.16" },
                    new { Min = 0.16, Max = 0.30, Name = "00.16-00.30" },
                    new { Min = 0.30, Max = 0.40, Name = "00.30-00.40" },
                    new { Min = 0.40, Max = 0.50, Name = "00.40-00.50" },
                    new { Min = 0.50, Max = double.MaxValue, Name = "Saturated" }
                };
            }
            
            return ranges;
        }

        private void DrawParticleCharts(List<PAData> preData, List<PAData> afterData, List<PAData> addData)
        {
            DrawInitialChart(preData);
            DrawAfterChart(afterData);
            DrawAddChart(addData);
        }

        // 设置预览模式，禁用操作按钮
        public void SetPreviewMode(bool isPreviewMode)
        {
            _isPreviewMode = isPreviewMode;

            if (isPreviewMode)
            {
                // 延迟执行以确保控件已加载
                this.Loaded += (s, e) => {
                    DisableOperationButtons();
                    // 手动触发可视化更新
                    RefreshVisualization();
                };

                // 如果已经加载，立即执行
                if (this.IsLoaded)
                {
                    DisableOperationButtons();
                    // 手动触发可视化更新
                    RefreshVisualization();
                }
            }
        }

        // 手动刷新可视化
        private void RefreshVisualization()
        {
            if (DataContext is ViewModels.PADataViewModel viewModel)
            {
                System.Diagnostics.Debug.WriteLine($"RefreshVisualization: Pre={viewModel.PreData?.Count ?? 0}, After={viewModel.AfterData?.Count ?? 0}, Add={viewModel.AddData?.Count ?? 0}");
                UpdateParticleVisualization(viewModel);
            }
        }

        private void DisableOperationButtons()
        {
            System.Diagnostics.Debug.WriteLine("DisableOperationButtons: 开始禁用操作按钮");

            // 查找操作按钮区域并禁用所有按钮
            var operationBorder = FindName("OperationButtonsBorder") as Border;
            if (operationBorder != null)
            {
                System.Diagnostics.Debug.WriteLine("找到OperationButtonsBorder，禁用其中的按钮");
                DisableButtonsInContainer(operationBorder);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("未找到OperationButtonsBorder，遍历整个控件树");
                // 如果没有找到命名的Border，遍历整个控件树查找按钮
                DisableButtonsInContainer(this);
            }
        }

        private void DisableButtonsInContainer(DependencyObject container)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(container); i++)
            {
                var child = VisualTreeHelper.GetChild(container, i);

                if (child is Button button)
                {
                    // 检查是否是操作按钮（通过内容或命令判断）
                    var content = button.Content?.ToString();
                    System.Diagnostics.Debug.WriteLine($"找到按钮: {content}, IsEnabled={button.IsEnabled}");

                    if (content != null && (content.Contains("解析") || content.Contains("导出") ||
                                          content.Contains("刷新") || content.Contains("清除") ||
                                          content.Contains("浏览") || content.Contains("🚀") ||
                                          content.Contains("💾") || content.Contains("🔄") ||
                                          content.Contains("🗑️") || content.Contains("📁")))
                    {
                        button.IsEnabled = false;
                        button.ToolTip = "预览模式下不可操作";
                        System.Diagnostics.Debug.WriteLine($"禁用按钮: {content}");
                    }
                }
                else
                {
                    DisableButtonsInContainer(child);
                }
            }
        }

        private void DrawInitialChart(List<PAData> data)
        {
            InitialCanvas.Children.Clear();
            Services.ParticleDrawingService.DrawParticlesOnCanvas(InitialCanvas, data);
        }

        private void DrawAfterChart(List<PAData> data)
        {
            AfterCanvas.Children.Clear();
            Services.ParticleDrawingService.DrawParticlesOnCanvas(AfterCanvas, data);
        }

        private void DrawAddChart(List<PAData> data)
        {
            AddCanvas.Children.Clear();
            Services.ParticleDrawingService.DrawParticlesOnCanvas(AddCanvas, data);
        }



        private void RefreshConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReloadColorConfig();
                MessageBox.Show("颜色配置已重新加载！", "配置刷新", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"刷新配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class ParticleSizeData
    {
        public string SizeRange { get; set; }
        public int Before { get; set; }
        public int After { get; set; }
        public int Delta { get; set; }
        public int Add { get; set; }
    }
}
