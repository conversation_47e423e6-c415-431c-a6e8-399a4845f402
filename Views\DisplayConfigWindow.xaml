<Window x:Class="DatalogDrawing.Views.DisplayConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DatalogDrawing.Views"
        mc:Ignorable="d"
        Title="显示配置确认" Height="550" Width="700" WindowStartupLocation="CenterOwner" SizeToContent="Height">
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 测量设备名称 -->
        <GroupBox Header="测量设备名称" Grid.Row="0" Margin="0,0,0,10">
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBox x:Name="EquipmentNameTextBox" Text="KLASFX200(@ 49Point)" Margin="0,5"/>
            </Grid>
        </GroupBox>
        
        <!-- 数值格式设置 -->
        <GroupBox Header="数值格式设置" Grid.Row="1" Margin="0,0,0,10">
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Text="小数位数:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox x:Name="DecimalPlacesComboBox" Grid.Row="0" Grid.Column="1" SelectedIndex="2" Margin="0,5">
                    <ComboBoxItem Content="0"/>
                    <ComboBoxItem Content="1"/>
                    <ComboBoxItem Content="2"/>
                    <ComboBoxItem Content="3"/>
                    <ComboBoxItem Content="4"/>
                    <ComboBoxItem Content="5"/>
                </ComboBox>
                
                <TextBlock Text="用户输入值:" Grid.Row="0" Grid.Column="2" VerticalAlignment="Center" Margin="20,0,10,0"/>
                <TextBox x:Name="UserInputValueTextBox" Grid.Row="0" Grid.Column="3" Text="1.0" Margin="0,5"/>
                
                <TextBlock Text="注: 用户输入值将用于一些计算，比如均匀性百分比等" Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="5" Foreground="Gray" FontStyle="Italic" Margin="0,5"/>
            </Grid>
        </GroupBox>
        
        <!-- Slot显示名称设置 -->
        <GroupBox Header="Slot显示名称设置" Grid.Row="2" Margin="0,0,0,10">
            <StackPanel Margin="5">
                <TextBlock Text="您可以自定义每个Slot的显示名称" Margin="0,5"/>
                <CheckBox x:Name="UseDefaultNamesCheckBox" Content="使用默认名称 (使用实际Slot ID)" Margin="0,5" Checked="UseDefaultNamesCheckBox_Checked"/>
            </StackPanel>
        </GroupBox>
        
        <!-- Slot名称列表 -->
        <DataGrid x:Name="SlotNamesDataGrid" Grid.Row="3" AutoGenerateColumns="False" 
                  CanUserAddRows="False" CanUserDeleteRows="False" Margin="0,0,0,15"
                  MinHeight="150" MaxHeight="250" VerticalScrollBarVisibility="Auto">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Slot编号" Binding="{Binding SlotNumber}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="晶圆ID" Binding="{Binding WaferId}" Width="150" IsReadOnly="True"/>
                <DataGridTextColumn Header="显示名称" Binding="{Binding DisplayName}" Width="*"/>
            </DataGrid.Columns>
        </DataGrid>
                
        <!-- 按钮区 -->
        <Border Grid.Row="4" BorderBrush="#DDDDDD" BorderThickness="0,1,0,0" Padding="0,10,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="确定" Width="80" Height="30" Click="Confirm_Click" Margin="0,0,10,0" Background="#0078D7" Foreground="White"/>
                <Button Content="取消" Width="80" Height="30" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 