using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace DatalogDrawing.Models
{
    /// <summary>
    /// Recipe类型枚举
    /// </summary>
    public enum RecipeType
    {
        MainRecipe,
        SubRecipe
    }

    /// <summary>
    /// Recipe数据模型
    /// </summary>
    public class RecipeData
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public RecipeType Type { get; set; }
        public DateTime ImportTime { get; set; } = DateTime.Now;
        public ObservableCollection<RecipeStep> Steps { get; set; } = new ObservableCollection<RecipeStep>();
        
        /// <summary>
        /// 总执行时间（计算得出）
        /// </summary>
        public TimeSpan TotalTime
        {
            get
            {
                var total = TimeSpan.Zero;
                foreach (var step in Steps)
                {
                    total = total.Add(step.StepTime);
                }
                return total;
            }
        }

        /// <summary>
        /// 步骤数量
        /// </summary>
        public int StepCount => Steps.Count;
    }

    /// <summary>
    /// Recipe步骤数据模型
    /// </summary>
    public class RecipeStep
    {
        public int StepNo { get; set; }
        public string StepName { get; set; }
        public TimeSpan StepTime { get; set; }
        public string StepUpCondition { get; set; }
        public double StepUpConditionTargetValue { get; set; }
        public string CommandCode { get; set; }
        
        // 升降机相关
        public int ElevatorCommand { get; set; }
        public int ElevatorSpeed { get; set; }
        
        // 舟转动相关
        public int BoatRotateCommand { get; set; }
        public int BoatRotateSpeed { get; set; }
        
        // 温度设置（8个区域）
        public double TempZone1Set { get; set; }
        public double TempZone2Set { get; set; }
        public double TempZone3Set { get; set; }
        public double TempZone4Set { get; set; }
        public double TempZone5Set { get; set; }
        public double TempZone6Set { get; set; }
        public double TempZone7Set { get; set; }
        public double TempZone8Set { get; set; }
        
        // 温度斜率设置（8个区域）
        public double TempZone1RampSet { get; set; }
        public double TempZone2RampSet { get; set; }
        public double TempZone3RampSet { get; set; }
        public double TempZone4RampSet { get; set; }
        public double TempZone5RampSet { get; set; }
        public double TempZone6RampSet { get; set; }
        public double TempZone7RampSet { get; set; }
        public double TempZone8RampSet { get; set; }
        
        public string TempControlMode { get; set; }
        public int LoopCount { get; set; }
        
        // APC2压力控制
        public string APC2ControlMode { get; set; }
        public double APC2Set { get; set; }
        public double APC2RampSet { get; set; }
        
        // APC3压力控制
        public string APC3ControlMode { get; set; }
        public double APC3Set { get; set; }
        public double APC3RampSet { get; set; }
        
        // 子配方相关
        public string SubRecipeName { get; set; }
        public int CallCount { get; set; }

        /// <summary>
        /// 获取显示用的升降机命令文本
        /// </summary>
        public string ElevatorCommandDisplay
        {
            get
            {
                if (ElevatorCommand == 0) return "NONE";
                return ElevatorCommand == 9 ? string.Format("PP({0})", ElevatorSpeed) : string.Format("CMD{0}({1})", ElevatorCommand, ElevatorSpeed);
            }
        }

        /// <summary>
        /// 获取显示用的舟转动命令文本
        /// </summary>
        public string BoatRotateCommandDisplay
        {
            get
            {
                if (BoatRotateCommand == 0) return "NONE";
                return BoatRotateCommand == 4 ? string.Format("R On({0})", BoatRotateSpeed) : string.Format("CMD{0}({1})", BoatRotateCommand, BoatRotateSpeed);
            }
        }

        /// <summary>
        /// 获取显示用的升温速率文本（取Zone1为代表）
        /// </summary>
        public string RampRateDisplay { get { return TempZone1RampSet + "℃/min"; } }

        /// <summary>
        /// 获取显示用的APC2设置文本（选择器字段）
        /// </summary>
        public string APC2SettingDisplay
        {
            get
            {
                if (string.IsNullOrEmpty(APC2ControlMode)) return "";
                
                // 根据配置文件的选择器逻辑
                if (APC2Set != 0)
                    return string.Format("{0}({1})", APC2ControlMode, APC2Set);
                if (APC2RampSet != 0)
                    return string.Format("{0}({1})", APC2ControlMode, APC2RampSet);
                
                return APC2ControlMode;
            }
        }

        /// <summary>
        /// 获取显示用的APC3设置文本（选择器字段）
        /// </summary>
        public string APC3SettingDisplay
        {
            get
            {
                if (string.IsNullOrEmpty(APC3ControlMode)) return "";
                
                // 根据配置文件的选择器逻辑
                if (APC3Set != 0)
                    return string.Format("{0}({1})", APC3ControlMode, APC3Set);
                if (APC3RampSet != 0)
                    return string.Format("{0}({1})", APC3ControlMode, APC3RampSet);
                
                return APC3ControlMode;
            }
        }

        /// <summary>
        /// 获取显示用的子配方名称文本
        /// </summary>
        public string SubRecipeNameDisplay
        {
            get
            {
                if (string.IsNullOrEmpty(SubRecipeName) || SubRecipeName == "0") return "0";
                return CallCount > 0 ? string.Format("{0}({1})", SubRecipeName, CallCount) : SubRecipeName;
            }
        }

        /// <summary>
        /// 获取显示用的条件文本（复合字段）
        /// </summary>
        public string ConditionDisplay
        {
            get
            {
                if (string.IsNullOrEmpty(StepUpCondition)) return "";
                return string.Format("{0}({1})", StepUpCondition, StepUpConditionTargetValue);
            }
        }

        /// <summary>
        /// 获取显示用的命令文本
        /// </summary>
        public string CommandDisplay
        {
            get
            {
                return CommandCode ?? "";
            }
        }

        /// <summary>
        /// 获取显示用的温度设置文本
        /// </summary>
        public string TempSetDisplay
        {
            get
            {
                return TempZone1Set.ToString("F1") + "°C";
            }
        }

        /// <summary>
        /// 获取显示用的温度速率文本
        /// </summary>
        public string TempRateDisplay
        {
            get
            {
                return TempZone1RampSet.ToString("F1") + "°C/min";
            }
        }

        /// <summary>
        /// 获取显示用的温度模式文本
        /// </summary>
        public string TempModeDisplay
        {
            get
            {
                return TempControlMode ?? "";
            }
        }

        /// <summary>
        /// 获取显示用的循环次数文本
        /// </summary>
        public string LoopDisplay
        {
            get
            {
                return LoopCount.ToString();
            }
        }


    }
}
