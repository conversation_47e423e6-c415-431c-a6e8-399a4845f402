using System.Collections.Generic;
using Newtonsoft.Json;

namespace DatalogDrawing.Models
{
    // 标准的ParticleColorMapping类（Pascal命名）
    public class ParticleColorMapping
    {
        public double MinSize { get; set; }
        public double MaxSize { get; set; }
        public string Color { get; set; }
        public string Name { get; set; }
    }

    // PAconfig.json中的颗粒颜色映射类（下划线命名）
    public class PAConfigParticleColorMapping
    {
        [JsonProperty("min_size")]
        public double MinSize { get; set; }

        [JsonProperty("max_size")]
        public double MaxSize { get; set; }

        [JsonProperty("color")]
        public string Color { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }
    }

    // PAconfig.json中的显示配置
    public class PAConfigDisplayConfig
    {
        [JsonProperty("particle_spacing_distance")]
        public double ParticleSpacingDistance { get; set; } = 130.0;

        [JsonProperty("particle_color_mapping")]
        public List<PAConfigParticleColorMapping> ParticleColorMapping { get; set; }
    }

    // PAconfig.json的根配置
    public class PAConfig
    {
        [JsonProperty("csv_parser_config")]
        public object CsvParserConfig { get; set; }

        [JsonProperty("display_config")]
        public PAConfigDisplayConfig DisplayConfig { get; set; }
    }

    // 兼容性类（保持向后兼容）
    public class DisplayConfig
    {
        public List<ParticleColorMapping> ParticleColorMapping { get; set; }
    }
}