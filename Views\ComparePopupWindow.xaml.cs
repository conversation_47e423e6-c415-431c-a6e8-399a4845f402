using DatalogDrawing.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Data;
using System.Windows.Media;

namespace DatalogDrawing.Views
{
    public partial class ComparePopupWindow : Window
    {
        public ComparePopupWindow()
        {
            InitializeComponent();
            this.Closed += ComparePopupWindow_Closed;
        }

        private void ComparePopupWindow_Closed(object sender, EventArgs e)
        {
            if (HeatMapView1 != null) HeatMapView1.Model = null;
            if (HeatMapView2 != null) HeatMapView2.Model = null;
        }
    }
} 