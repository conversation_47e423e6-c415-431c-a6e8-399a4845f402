using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Globalization;
using DatalogDrawing.Interfaces;
using DatalogDrawing.Models;
using Newtonsoft.Json;

namespace DatalogDrawing.Services
{
    public class FilmThicknessParser : IDataParser<FilmThicknessData>
    {
        public Dictionary<string, object> Parse(string filePath, AppConfig config)
        {
            return ParseAsync(filePath, config).GetAwaiter().GetResult();
        }

        public FilmThicknessData ParseData(string filePath, AppConfig config)
        {
            var result = ParseAsync(filePath, config).GetAwaiter().GetResult();
            var filmThicknessData = new FilmThicknessData();

            foreach (var kvp in result)
            {
                filmThicknessData[kvp.Key] = kvp.Value;
            }

            return filmThicknessData;
        }

        public async Task<Dictionary<string, object>> ParseAsync(string filePath, AppConfig config)
        {
            return await ParseCsvFileAsync(filePath, config);
        }

        public async Task<Dictionary<string, object>> ParseCsvFileAsync(string filePath, AppConfig config)
        {
            var csvConfig = config.CsvParserConfig;
            var encoding = GetEncoding(csvConfig.Encoding);
            var allLines = new List<string>();
            using (var reader = new StreamReader(filePath, encoding))
            {
                string line;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        allLines.Add(line);
                    }
                }
            }
            // 先获得所有SlotInfo
            var slotInfos = ParseMultiSlotFile(allLines, csvConfig);
            // 合并所有SlotInfo中的SlotTotalInfo和SlotDetailInfo为一个List<Dictionary<string, object>>
            var results = new Dictionary<string, object>();
            foreach (var slot in slotInfos)
            {
                results.Add(slot.SlotNumber.ToString(), slot);
            }
            return results;
        }

        private List<SlotInfo> ParseMultiSlotFile(List<string> allLines, CsvParserConfig config)
        {
            var slotGroups = SplitIntoSlotGroups(allLines);
            var results = new SlotInfo[slotGroups.Count];

            Parallel.For(0, slotGroups.Count, i =>
            {
                results[i] = ParseSlotGroup(slotGroups[i], config);
            });

            return results.ToList();
        }

        private List<List<string>> SplitIntoSlotGroups(List<string> allLines)
        {
            var slotGroups = new List<List<string>>();
            List<string> currentGroup = null;

            foreach (var line in allLines)
            {
                // 以WAFER ID,为新slot的开始（更健壮，兼容多slot文件）
                if (line.Trim().StartsWith("WAFER ID,", StringComparison.OrdinalIgnoreCase))
                {
                    if (currentGroup != null && currentGroup.Count > 0)
                    {
                        slotGroups.Add(currentGroup);
                    }
                    currentGroup = new List<string>();
                }
                if (currentGroup != null)
                {
                    currentGroup.Add(line);
                }
            }
            if (currentGroup != null && currentGroup.Count > 0)
            {
                slotGroups.Add(currentGroup);
            }
            return slotGroups;
        }

        private SlotInfo ParseSlotGroup(List<string> slotLines, CsvParserConfig config)
        {
            var slotInfo = ExtractSlotInfo(slotLines);
            // 先生成ColumnMappings
            var columnStatisiticsMappings = new List<string>();
            var columnDetailMappings = new List<string>();
            foreach (var filter in config.RowFilters.Where(f => f.Enabled && !string.IsNullOrEmpty(f.StartValue)))
            {
                if (filter.DataType == "Statistics")
                {
                    int startIndex = slotLines.FindIndex(l => CheckLineCondition(l, filter.StartCondition, filter.StartValue));
                    if (startIndex == -1) continue;
                    // 已经找到了RESULT TYPE行，直接使用
                    var resultTypeLine = slotLines[startIndex];
                    var resultTypeValues = ParseCsvLine(resultTypeLine, config.Delimiter);
                    for (int i = 0; i < resultTypeValues.Count; i++)
                    {
                        if (resultTypeValues[i].Contains("."))
                        {
                            resultTypeValues[i] = resultTypeValues[i].Replace(".", "_");
                        }
                    }

                    columnStatisiticsMappings = resultTypeValues;
                }
                else // detail
                {
                    int startIndex = slotLines.FindIndex(l => CheckLineCondition(l, filter.StartCondition, filter.StartValue));
                    if (startIndex == -1) continue;

                    var resultTypeLine = slotLines[startIndex];
                    var resultTypeValues = ParseCsvLine(resultTypeLine, config.Delimiter);
                    for (int i = 0; i < resultTypeValues.Count; i++)
                    {
                        if (resultTypeValues[i].Contains("."))
                        {
                            resultTypeValues[i] = resultTypeValues[i].Replace(".", "_");
                        }
                    }

                    columnDetailMappings = resultTypeValues;
                }
            }

            if (columnStatisiticsMappings.Count == 0)
            {
                throw new Exception("没有找到Statistics数据列");
            }
            if (columnDetailMappings.Count < columnStatisiticsMappings.Count)
            {
                throw new Exception("Detail数据列少于Statistics数据");
            }
            // 用columnStatisiticsMappings填充columnDetailMappings
            for (int i = 0; i < columnStatisiticsMappings.Count; i++)
            {
                columnDetailMappings[i] = columnStatisiticsMappings[i];
            }

            slotInfo.columnStaticNames = columnStatisiticsMappings;
            slotInfo.columnDetailNames = columnDetailMappings;

            foreach (var filter in config.RowFilters.Where(f => f.Enabled && !string.IsNullOrEmpty(f.StartValue)))
            {
                int startIndex = slotLines.FindIndex(l => CheckLineCondition(l, filter.StartCondition, filter.StartValue));
                if (startIndex == -1) continue;
                startIndex++;

                int endIndex = slotLines.Count;
                if (!string.IsNullOrEmpty(filter.EndValue))
                {
                    endIndex = slotLines.FindIndex(startIndex, l => CheckLineCondition(l, filter.EndCondition, filter.EndValue));
                    if (endIndex == -1) endIndex = slotLines.Count;
                }

                for (int i = startIndex; i < endIndex; i++)
                {
                    var line = slotLines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;
                    var values = ParseCsvLine(line, config.Delimiter);


                    var rowData = new Dictionary<string, object>
                    {
                        ["SlotNumber"] = slotInfo.SlotNumber.ToString(),
                        ["WaferId"] = slotInfo.WaferId,
                        ["DataType"] = filter.DataType
                    };

                    // 分类填充
                    if (filter.DataType == "Statistics")
                    {
                        if (values.Count > slotInfo.columnStaticNames.Count)
                        {
                            throw new Exception($"数据列数大于列名数 (行:{i + 1})");
                        }
                        for (int j = 0; j < values.Count; j++)
                        {
                            rowData[slotInfo.columnStaticNames[j]] = values[j];
                        }
                        slotInfo.SlotTotalInfo.Add(rowData);
                    }
                    else
                    {
                        if (values.Count > slotInfo.columnDetailNames.Count)
                        {
                            throw new Exception("数据列数大于列名数");
                        }
                        for (int j = 0; j < values.Count; j++)
                        {
                            rowData[slotInfo.columnDetailNames[j]] = values[j];
                        }
                        slotInfo.SlotDetailInfo.Add(rowData);
                    }

                }
            }
            return slotInfo;
        }

        private bool CheckLineCondition(string line, string condition, string value)
        {
            if (string.IsNullOrEmpty(condition) || value == null) return false;
            var trimmedLine = line.Trim();
            switch (condition.ToLower())
            {
                case "equals":
                    return trimmedLine.Equals(value, StringComparison.OrdinalIgnoreCase);
                case "contains":
                    return trimmedLine.IndexOf(value, StringComparison.OrdinalIgnoreCase) >= 0;
                case "startswith":
                    return trimmedLine.StartsWith(value, StringComparison.OrdinalIgnoreCase);
                case "endswith":
                    // 使用 startsWith确定结束行
                    return trimmedLine.StartsWith(value, StringComparison.OrdinalIgnoreCase);
                default:
                    return false;
            }
        }

        private Encoding GetEncoding(string encodingName)
        {
            return encodingName?.ToUpper() switch
            {
                "UTF-8" => Encoding.UTF8,
                "GBK" => Encoding.GetEncoding("GBK"),
                "ASCII" => Encoding.ASCII,
                _ => Encoding.UTF8
            };
        }

        private SlotInfo ExtractSlotInfo(List<string> slotLines)
        {
            var slotInfo = new SlotInfo();
            foreach (var line in slotLines)
            {
                if (line.Trim().StartsWith("SLOT,", StringComparison.OrdinalIgnoreCase))
                {
                    var parts = line.Split(',');
                    if (parts.Length > 1 && int.TryParse(parts[1].Trim(), out int slotNumber))
                        slotInfo.SlotNumber = slotNumber;
                }
                else if (line.Trim().StartsWith("WAFER ID,", StringComparison.OrdinalIgnoreCase))
                {
                    var parts = line.Split(',');
                    if (parts.Length > 1)
                        slotInfo.WaferId = parts[1].Trim();
                }
            }
            return slotInfo;
        }

        private List<string> ParseCsvLine(string line, string delimiter)
        {
            if (string.IsNullOrEmpty(line))
                return new List<string>();

            var parts = new List<string>();
            int start = 0, end;
            while ((end = line.IndexOf(delimiter, start)) != -1)
            {
                parts.Add(line.Substring(start, end - start).Trim());
                start = end + delimiter.Length;
            }
            parts.Add(line.Substring(start).Trim());

            // 仅当最后一列为空时才移除
            if (parts.Count > 0 && string.IsNullOrEmpty(parts.Last()))
                parts.RemoveAt(parts.Count - 1);

            return parts;
        }
    }
}