using DatalogDrawing.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Data;
using System.Windows.Threading;
using System.Collections.Generic;
using System.Linq;
using MathNet.Numerics.Interpolation;
using System.Windows.Input;
using MIConvexHull;

namespace DatalogDrawing.Views
{
    /// <summary>
    /// Interaction logic for FilmThicknessView.xaml
    /// </summary>
    public partial class FilmThicknessView : UserControl
    {
        public FilmThicknessView()
        {
            InitializeComponent();
            this.DataContextChanged += FilmThicknessView_DataContextChanged;
        }

        private void FilmThicknessView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            var vm = this.DataContext as FilmThicknessViewModel;
            if (vm != null)
            {
                // 订阅 PropertyChanged 事件
                vm.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(vm.ResultTable) || args.PropertyName == nameof(vm.ResultTable.DefaultView))
                    {
                        // 使用新方法更新Grid
                        UpdateResultGrid(vm.ResultTable);
                    }
                    if (args.PropertyName == nameof(vm.StatisticsTable) || args.PropertyName == nameof(vm.ResultTable.DefaultView))
                    {
                        // 强制刷新
                        StatisticsDataGrid.ItemsSource = null;
                        StatisticsDataGrid.ItemsSource = vm.StatisticsTable.DefaultView;
                    }
                    if (args.PropertyName == nameof(vm.DetailTable) || args.PropertyName == nameof(vm.DetailTable.DefaultView))
                    {
                        // 强制刷新
                        DetailDataGrid.ItemsSource = null;
                        DetailDataGrid.ItemsSource = vm.DetailTable.DefaultView;
                    }
                };
            }
        }

        private void UpdateResultGrid(DataTable dataTable)
        {
            // 获取ViewModel
            var vm = this.DataContext as FilmThicknessViewModel;
            if (vm == null)
                return;

            // 清空Grid
            ResultGrid.Children.Clear();
            ResultGrid.RowDefinitions.Clear();
            ResultGrid.ColumnDefinitions.Clear();

            // 如果没有数据，直接返回
            if (dataTable == null || dataTable.Rows.Count == 0)
                return;

            // 定义样式
            var borderBrush = new SolidColorBrush(Colors.LightGray);
            var textBlockStyle = new Style(typeof(TextBlock));
            textBlockStyle.Setters.Add(new Setter(TextBlock.HorizontalAlignmentProperty, HorizontalAlignment.Center));
            textBlockStyle.Setters.Add(new Setter(TextBlock.VerticalAlignmentProperty, VerticalAlignment.Center));
            textBlockStyle.Setters.Add(new Setter(TextBlock.MarginProperty, new Thickness(5)));
            textBlockStyle.Setters.Add(new Setter(TextBlock.TextAlignmentProperty, TextAlignment.Center));

            // 创建行定义
            for (int i = 0; i < dataTable.Rows.Count; i++)
            {
                // 最后一行用于热力图，设置足够的高度
                if (i == dataTable.Rows.Count - 1)
                    ResultGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(210) });
                else
                    ResultGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            // 创建列定义
            ResultGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });  // 第一列固定宽度
            ResultGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });  // 第二列固定宽度

            // 根据数据动态添加列
            int slotCount = dataTable.Columns.Count - 2; // 减去前两列
            for (int i = 0; i < slotCount; i++)
            {
                // 使用固定宽度，确保热力图大小一致
                ResultGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220, GridUnitType.Pixel) });
            }

            // 处理第一行的合并单元格
            // 第一行第一列跨2列
            var firstRowFirstCell = CreateBorderWithText(dataTable.Rows[0][0]?.ToString(), textBlockStyle);
            Grid.SetRow(firstRowFirstCell, 0);
            Grid.SetColumn(firstRowFirstCell, 0);
            Grid.SetColumnSpan(firstRowFirstCell, 2);
            ResultGrid.Children.Add(firstRowFirstCell);

            // 第一行第三列跨到最后
            var firstRowThirdCell = CreateBorderWithText(dataTable.Rows[0][2]?.ToString(), textBlockStyle);
            Grid.SetRow(firstRowThirdCell, 0);
            Grid.SetColumn(firstRowThirdCell, 2);
            Grid.SetColumnSpan(firstRowThirdCell, slotCount); // 确保跨越足够的列
            ResultGrid.Children.Add(firstRowThirdCell);

            // 处理第二行
            // 第二行第一列跨2列
            var secondRowFirstCell = CreateBorderWithText(dataTable.Rows[1][0]?.ToString(), textBlockStyle);
            Grid.SetRow(secondRowFirstCell, 1);
            Grid.SetColumn(secondRowFirstCell, 0);
            Grid.SetColumnSpan(secondRowFirstCell, 2);
            ResultGrid.Children.Add(secondRowFirstCell);

            // 第二行其他列 - 这些是slot名称
            List<string> slotNames = new List<string>();
            for (int col = 2; col < dataTable.Columns.Count; col++)
            {
                var content = dataTable.Rows[1][col]?.ToString();
                slotNames.Add(content); // 保存slot名称，用于后面的热力图

                var border = CreateBorderWithText(content, textBlockStyle);
                Grid.SetRow(border, 1);
                Grid.SetColumn(border, col);
                ResultGrid.Children.Add(border);
            }

            // 处理第三行到倒数第二行
            // 创建第三行到倒数第二行第一列的合并单元格
            if (dataTable.Rows.Count > 3) // 确保至少有3行
            {
                var thicknessCell = CreateBorderWithText("Thickness", textBlockStyle);
                Grid.SetRow(thicknessCell, 2);
                Grid.SetColumn(thicknessCell, 0);
                Grid.SetRowSpan(thicknessCell, dataTable.Rows.Count - 3); // 从第三行到倒数第二行
                ResultGrid.Children.Add(thicknessCell);

                // 处理从第三行到倒数第二行的其它单元格
                for (int row = 2; row < dataTable.Rows.Count - 1; row++)
                {
                    // 第二列及以后
                    for (int col = 1; col < dataTable.Columns.Count; col++)
                    {
                        var content = dataTable.Rows[row][col]?.ToString();
                        var border = CreateBorderWithText(content, textBlockStyle);
                        Grid.SetRow(border, row);
                        Grid.SetColumn(border, col);
                        ResultGrid.Children.Add(border);
                    }
                }
            }

            // 处理最后一行
            // 最后一行第一列跨2列
            int lastRowIndex = dataTable.Rows.Count - 1;
            var lastRowFirstCell = CreateBorderWithText(dataTable.Rows[lastRowIndex][0]?.ToString(), textBlockStyle);
            Grid.SetRow(lastRowFirstCell, lastRowIndex);
            Grid.SetColumn(lastRowFirstCell, 0);
            Grid.SetColumnSpan(lastRowFirstCell, 2);
            ResultGrid.Children.Add(lastRowFirstCell);

            // 最后一行其它列放置热力图
            for (int col = 2; col < dataTable.Columns.Count; col++)
            {
                try
                {
                    // 获取对应slot的名称和热力图数据
                    string slotName = col - 2 < slotNames.Count ? slotNames[col - 2] : "";
                    var heatMapPoints = vm.GetHeatMapPointsForSlot(slotName);

                    // 记录日志，帮助调试大小问题
                    System.Diagnostics.Debug.WriteLine($"热力图绘制：槽位 {slotName}, 列索引 {col}, 列宽 {ResultGrid.ColumnDefinitions[col].Width}");

                    // 创建热力图
                    var plotModel = CreateHeatMapModel(heatMapPoints, slotName);
                    var plotView = new OxyPlot.Wpf.PlotView
                    {
                        Model = plotModel,
                        Height = 180,
                        Width = 200, // 调整固定宽度，留出足够空间显示边框和内容
                        Margin = new Thickness(2), // 减小边距
                        Background = new SolidColorBrush(Colors.White),
                        PanCursor = Cursors.Hand,
                        ZoomHorizontalCursor = Cursors.SizeWE,
                        ZoomVerticalCursor = Cursors.SizeNS,
                        HorizontalAlignment = HorizontalAlignment.Center // 在父容器中居中
                    };

                    var border = new Border
                    {
                        BorderBrush = borderBrush,
                        BorderThickness = new Thickness(1),
                        Child = plotView,
                        Background = new SolidColorBrush(Colors.White)
                    };

                    Grid.SetRow(border, lastRowIndex);
                    Grid.SetColumn(border, col);
                    ResultGrid.Children.Add(border);

                    System.Diagnostics.Debug.WriteLine($"Added heat map for slot {slotName} with {heatMapPoints.Count} points");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error creating heat map at column {col}: {ex.Message}");

                    // 添加错误信息显示
                    var errorTextBlock = new TextBlock
                    {
                        Text = "Error creating heat map",
                        Style = textBlockStyle,
                        Foreground = new SolidColorBrush(Colors.Red)
                    };

                    var border = new Border
                    {
                        BorderBrush = borderBrush,
                        BorderThickness = new Thickness(1),
                        Child = errorTextBlock
                    };

                    Grid.SetRow(border, lastRowIndex);
                    Grid.SetColumn(border, col);
                    ResultGrid.Children.Add(border);
                }
            }
        }

        private Border CreateBorderWithText(string text, Style textBlockStyle)
        {
            var textBlock = new TextBlock
            {
                Text = text,
                Style = textBlockStyle
            };

            var border = new Border
            {
                BorderBrush = new SolidColorBrush(Colors.LightGray),
                BorderThickness = new Thickness(1),
                Child = textBlock
            };

            return border;
        }

        private OxyPlot.PlotModel CreateHeatMapModel(List<FilmThicknessViewModel.HeatMapPoint> points, string title)
        {
            var plotModel = new OxyPlot.PlotModel { Title = title + " THK MAP", PlotAreaBorderThickness = new OxyPlot.OxyThickness(0) };
            if (points == null || points.Count < 4)
            {
                plotModel.Subtitle = "No data available";
                return plotModel;
            }

            // 1. 计算数据点的中心和半径
            double centerX = points.Average(p => p.X);
            double centerY = points.Average(p => p.Y);
            double radius = points.Max(p => Math.Sqrt(Math.Pow(p.X - centerX, 2) + Math.Pow(p.Y - centerY, 2))) * 1.05;

            // 2. 生成规则网格
            int gridSize = 150;
            double minX = centerX - radius, maxX = centerX + radius;
            double minY = centerY - radius, maxY = centerY + radius;
            double[] xGrid = Enumerable.Range(0, gridSize).Select(i => minX + i * (maxX - minX) / (gridSize - 1)).ToArray();
            double[] yGrid = Enumerable.Range(0, gridSize).Select(i => minY + i * (maxY - minY) / (gridSize - 1)).ToArray();
            double[,] zGrid = new double[gridSize, gridSize];

            // 3. 线性插值（Delaunay + MIConvexHull）
            var delaunayPoints = points.Select(p => new Vertex2D(p.X, p.Y, p.Value)).ToList();
            var triangulation = MIConvexHull.DelaunayTriangulation<Vertex2D, TriFace>.Create(delaunayPoints, 1e-10);

            for (int i = 0; i < gridSize; i++)
            {
                for (int j = 0; j < gridSize; j++)
                {
                    double xi = xGrid[i], yj = yGrid[j];
                    double distToCenter = Math.Sqrt(Math.Pow(xi - centerX, 2) + Math.Pow(yj - centerY, 2));
                    if (distToCenter > radius)
                    {
                        zGrid[i, j] = double.NaN;
                        continue;
                    }
                    zGrid[i, j] = InterpolateLinear(triangulation, xi, yj);
                }
            }

            // 4. 圆形遮罩（确保边界外完全透明）
            for (int i = 0; i < gridSize; i++)
            {
                for (int j = 0; j < gridSize; j++)
                {
                    double xi = xGrid[i], yj = yGrid[j];
                    double distToCenter = Math.Sqrt(Math.Pow(xi - centerX, 2) + Math.Pow(yj - centerY, 2));
                    if (distToCenter > radius)
                        zGrid[i, j] = double.NaN;
                }
            }

            // 4.5. 应用中位数滤波来减少噪声并改善视觉效果
            zGrid = MedianFilter2D(zGrid, gridSize);

            // 5. 统计有效数据的 min/max（只统计非 NaN 数据）
            double minValue = double.MaxValue, maxValue = double.MinValue;
            int validCount = 0;
            for (int i = 0; i < gridSize; i++)
            {
                for (int j = 0; j < gridSize; j++)
                {
                    if (!double.IsNaN(zGrid[i, j]))
                    {
                        minValue = Math.Min(minValue, zGrid[i, j]);
                        maxValue = Math.Max(maxValue, zGrid[i, j]);
                        validCount++;
                    }
                }
            }

            // 如果有效数据太少，使用原始数据点的范围
            if (validCount < 10)
            {
                minValue = points.Min(p => p.Value);
                maxValue = points.Max(p => p.Value);
            }

            // 确保 min/max 不同，避免颜色轴异常
            if (Math.Abs(maxValue - minValue) < 1e-6)
            {
                double range = Math.Abs(minValue) * 0.1;
                if (range < 1e-6) range = 1.0;
                minValue -= range;
                maxValue += range;
            }

            // 添加颜色轴（turbo调色板，Key=ColorAxis）
            plotModel.Axes.Add(new OxyPlot.Axes.LinearColorAxis
            {
                Position = OxyPlot.Axes.AxisPosition.Right,
                Palette = CreateTurboPalette(500),
                Title = "Value",
                Key = "ColorAxis",
                Minimum = minValue,
                Maximum = maxValue,
                AxisDistance = 20,
                StringFormat = "0.00",
                TicklineColor = OxyPlot.OxyColors.Black,
                TextColor = OxyPlot.OxyColors.Black,
                MajorGridlineStyle = OxyPlot.LineStyle.Solid
            });

            // 6. OxyPlot 可视化
            var heatmapSeries = new OxyPlot.Series.HeatMapSeries
            {
                X0 = minX,
                X1 = maxX,
                Y0 = minY,
                Y1 = maxY,
                Data = zGrid,
                Interpolate = true,
                RenderMethod = OxyPlot.Series.HeatMapRenderMethod.Bitmap,
                ColorAxisKey = "ColorAxis"
            };
            plotModel.Series.Add(heatmapSeries);

            // 7. 添加等高线
            var contourSeries = new OxyPlot.Series.ContourSeries
            {
                ColumnCoordinates = xGrid,
                RowCoordinates = yGrid,
                Data = zGrid,
                ContourLevels = GenerateContourLevels(minValue, maxValue, 12),
                LabelBackground = OxyPlot.OxyColors.Transparent,
                StrokeThickness = 0.5,
                FontSize = 0,
                Color = OxyPlot.OxyColors.Black
            };
            plotModel.Series.Add(contourSeries);

            // 8. 添加原始采样点
            var scatterSeries = new OxyPlot.Series.ScatterSeries
            {
                MarkerType = OxyPlot.MarkerType.Circle,
                MarkerSize = 1.5,
                MarkerFill = OxyPlot.OxyColors.Black
            };
            foreach (var p in points)
            {
                scatterSeries.Points.Add(new OxyPlot.Series.ScatterPoint(p.X, p.Y, 1.5, p.Value));
            }
            plotModel.Series.Add(scatterSeries);

            return plotModel;
        }

        // 创建MATLAB风格的Turbo调色板
        private OxyPlot.OxyPalette CreateTurboPalette(int steps)
        {
            // MATLAB turbo 调色板数据（256个颜色点，RGB值范围0-1）
            float[,] turbo = new float[256, 3]
            {
                {0.18995f,0.07176f,0.23217f},{0.19483f,0.08339f,0.26149f},{0.19956f,0.09498f,0.29024f},{0.20415f,0.10652f,0.31844f},{0.20860f,0.11802f,0.34607f},{0.21291f,0.12947f,0.37314f},{0.21708f,0.14087f,0.39964f},{0.22111f,0.15223f,0.42558f},{0.22500f,0.16354f,0.45096f},{0.22875f,0.17481f,0.47578f},
                {0.23236f,0.18603f,0.50004f},{0.23582f,0.19720f,0.52373f},{0.23915f,0.20833f,0.54686f},{0.24234f,0.21941f,0.56942f},{0.24539f,0.23044f,0.59142f},{0.24830f,0.24143f,0.61286f},{0.25107f,0.25237f,0.63374f},{0.25369f,0.26327f,0.65406f},{0.25618f,0.27412f,0.67381f},{0.25853f,0.28492f,0.69300f},
                {0.26074f,0.29568f,0.71162f},{0.26280f,0.30639f,0.72968f},{0.26473f,0.31706f,0.74718f},{0.26652f,0.32768f,0.76412f},{0.26816f,0.33825f,0.78050f},{0.26967f,0.34878f,0.79631f},{0.27103f,0.35926f,0.81156f},{0.27226f,0.36970f,0.82624f},{0.27334f,0.38008f,0.84037f},{0.27429f,0.39043f,0.85393f},
                {0.27509f,0.40072f,0.86692f},{0.27576f,0.41097f,0.87936f},{0.27628f,0.42118f,0.89123f},{0.27667f,0.43134f,0.90254f},{0.27691f,0.44145f,0.91328f},{0.27701f,0.45152f,0.92347f},{0.27698f,0.46153f,0.93309f},{0.27680f,0.47151f,0.94214f},{0.27648f,0.48144f,0.95064f},{0.27603f,0.49132f,0.95857f},
                {0.27543f,0.50115f,0.96594f},{0.27469f,0.51094f,0.97275f},{0.27381f,0.52069f,0.97899f},{0.27273f,0.53040f,0.98461f},{0.27106f,0.54015f,0.98930f},{0.26878f,0.54995f,0.99303f},{0.26592f,0.55979f,0.99583f},{0.26252f,0.56967f,0.99773f},{0.25862f,0.57958f,0.99876f},{0.25425f,0.58950f,0.99896f},
                {0.24946f,0.59943f,0.99835f},{0.24427f,0.60937f,0.99697f},{0.23874f,0.61931f,0.99485f},{0.23288f,0.62923f,0.99202f},{0.22676f,0.63913f,0.98851f},{0.22039f,0.64901f,0.98436f},{0.21382f,0.65886f,0.97959f},{0.20708f,0.66866f,0.97423f},{0.20021f,0.67842f,0.96833f},{0.19326f,0.68812f,0.96190f},
                {0.18625f,0.69775f,0.95498f},{0.17923f,0.70732f,0.94761f},{0.17223f,0.71680f,0.93981f},{0.16529f,0.72620f,0.93161f},{0.15844f,0.73551f,0.92305f},{0.15173f,0.74472f,0.91416f},{0.14519f,0.75381f,0.90496f},{0.13886f,0.76279f,0.89550f},{0.13278f,0.77165f,0.88580f},{0.12698f,0.78037f,0.87590f},
                {0.12151f,0.78896f,0.86581f},{0.11639f,0.79740f,0.85559f},{0.11167f,0.80569f,0.84525f},{0.10738f,0.81381f,0.83484f},{0.10357f,0.82177f,0.82437f},{0.10026f,0.82955f,0.81389f},{0.09750f,0.83714f,0.80342f},{0.09532f,0.84455f,0.79299f},{0.09377f,0.85175f,0.78264f},{0.09287f,0.85875f,0.77240f},
                {0.09267f,0.86554f,0.76230f},{0.09320f,0.87211f,0.75237f},{0.09451f,0.87844f,0.74265f},{0.09662f,0.88454f,0.73316f},{0.09958f,0.89040f,0.72393f},{0.10342f,0.89600f,0.71500f},{0.10815f,0.90142f,0.70599f},{0.11374f,0.90673f,0.69651f},{0.12014f,0.91193f,0.68660f},{0.12733f,0.91701f,0.67627f},
                {0.13526f,0.92197f,0.66556f},{0.14391f,0.92680f,0.65448f},{0.15323f,0.93151f,0.64308f},{0.16319f,0.93609f,0.63137f},{0.17377f,0.94053f,0.61938f},{0.18491f,0.94484f,0.60713f},{0.19659f,0.94901f,0.59466f},{0.20877f,0.95304f,0.58199f},{0.22142f,0.95692f,0.56914f},{0.23449f,0.96065f,0.55614f},
                {0.24797f,0.96423f,0.54303f},{0.26180f,0.96765f,0.52981f},{0.27597f,0.97092f,0.51653f},{0.29042f,0.97403f,0.50321f},{0.30513f,0.97697f,0.48987f},{0.32006f,0.97974f,0.47654f},{0.33517f,0.98234f,0.46325f},{0.35043f,0.98477f,0.45002f},{0.36581f,0.98702f,0.43688f},{0.38127f,0.98909f,0.42386f},
                {0.39678f,0.99098f,0.41098f},{0.41229f,0.99268f,0.39826f},{0.42778f,0.99419f,0.38575f},{0.44321f,0.99551f,0.37345f},{0.45854f,0.99663f,0.36140f},{0.47375f,0.99755f,0.34963f},{0.48879f,0.99828f,0.33816f},{0.50362f,0.99879f,0.32701f},{0.51822f,0.99910f,0.31622f},{0.53255f,0.99919f,0.30581f},
                {0.54658f,0.99907f,0.29581f},{0.56026f,0.99873f,0.28623f},{0.57357f,0.99817f,0.27712f},{0.58646f,0.99739f,0.26849f},{0.59891f,0.99638f,0.26038f},{0.61088f,0.99514f,0.25280f},{0.62233f,0.99366f,0.24579f},{0.63323f,0.99195f,0.23937f},{0.64362f,0.98999f,0.23356f},{0.65394f,0.98775f,0.22835f},
                {0.66428f,0.98524f,0.22370f},{0.67462f,0.98246f,0.21960f},{0.68494f,0.97941f,0.21602f},{0.69525f,0.97610f,0.21294f},{0.70553f,0.97255f,0.21032f},{0.71577f,0.96875f,0.20815f},{0.72596f,0.96470f,0.20640f},{0.73610f,0.96043f,0.20504f},{0.74617f,0.95593f,0.20406f},{0.75617f,0.95121f,0.20343f},
                {0.76608f,0.94627f,0.20311f},{0.77591f,0.94113f,0.20310f},{0.78563f,0.93579f,0.20336f},{0.79524f,0.93025f,0.20386f},{0.80473f,0.92452f,0.20459f},{0.81410f,0.91861f,0.20552f},{0.82333f,0.91253f,0.20663f},{0.83241f,0.90627f,0.20788f},{0.84133f,0.89986f,0.20926f},{0.85010f,0.89328f,0.21074f},
                {0.85868f,0.88655f,0.21230f},{0.86709f,0.87968f,0.21391f},{0.87530f,0.87267f,0.21555f},{0.88331f,0.86553f,0.21719f},{0.89112f,0.85826f,0.21880f},{0.89870f,0.85087f,0.22038f},{0.90605f,0.84337f,0.22188f},{0.91317f,0.83576f,0.22328f},{0.92004f,0.82806f,0.22456f},{0.92666f,0.82025f,0.22570f},
                {0.93301f,0.81236f,0.22667f},{0.93909f,0.80439f,0.22744f},{0.94489f,0.79634f,0.22800f},{0.95039f,0.78823f,0.22831f},{0.95560f,0.78005f,0.22836f},{0.96049f,0.77181f,0.22811f},{0.96507f,0.76352f,0.22754f},{0.96931f,0.75519f,0.22663f},{0.97323f,0.74682f,0.22536f},{0.97679f,0.73842f,0.22369f},
                {0.98000f,0.73000f,0.22161f},{0.98289f,0.72140f,0.21918f},{0.98549f,0.71250f,0.21650f},{0.98781f,0.70330f,0.21358f},{0.98986f,0.69382f,0.21043f},{0.99163f,0.68408f,0.20706f},{0.99314f,0.67408f,0.20348f},{0.99438f,0.66386f,0.19971f},{0.99535f,0.65341f,0.19577f},{0.99607f,0.64277f,0.19165f},
                {0.99654f,0.63193f,0.18738f},{0.99675f,0.62093f,0.18297f},{0.99672f,0.60977f,0.17842f},{0.99644f,0.59846f,0.17376f},{0.99593f,0.58703f,0.16899f},{0.99517f,0.57549f,0.16412f},{0.99419f,0.56386f,0.15918f},{0.99297f,0.55214f,0.15417f},{0.99153f,0.54036f,0.14910f},{0.98987f,0.52854f,0.14398f},
                {0.98799f,0.51667f,0.13883f},{0.98590f,0.50479f,0.13367f},{0.98360f,0.49291f,0.12849f},{0.98108f,0.48104f,0.12332f},{0.97837f,0.46920f,0.11817f},{0.97545f,0.45740f,0.11305f},{0.97234f,0.44565f,0.10797f},{0.96904f,0.43399f,0.10294f},{0.96555f,0.42241f,0.09798f},{0.96187f,0.41093f,0.09310f},
                {0.95801f,0.39958f,0.08831f},{0.95398f,0.38836f,0.08362f},{0.94977f,0.37729f,0.07905f},{0.94538f,0.36638f,0.07461f},{0.94084f,0.35566f,0.07031f},{0.93612f,0.34513f,0.06616f},{0.93125f,0.33482f,0.06218f},{0.92623f,0.32473f,0.05837f},{0.92105f,0.31489f,0.05475f},{0.91572f,0.30530f,0.05134f},
                {0.91024f,0.29599f,0.04814f},{0.90463f,0.28696f,0.04516f},{0.89888f,0.27824f,0.04243f},{0.89298f,0.26981f,0.03993f},{0.88691f,0.26152f,0.03753f},{0.88066f,0.25334f,0.03521f},{0.87422f,0.24526f,0.03297f},{0.86760f,0.23730f,0.03082f},{0.86079f,0.22945f,0.02875f},{0.85380f,0.22170f,0.02677f},
                {0.84662f,0.21407f,0.02487f},{0.83926f,0.20654f,0.02305f},{0.83172f,0.19912f,0.02131f},{0.82399f,0.19182f,0.01966f},{0.81608f,0.18462f,0.01809f},{0.80799f,0.17753f,0.01660f},{0.79971f,0.17055f,0.01520f},{0.79125f,0.16368f,0.01387f},{0.78260f,0.15693f,0.01264f},{0.77377f,0.15028f,0.01148f},
                {0.76476f,0.14374f,0.01041f},{0.75556f,0.13731f,0.00942f},{0.74617f,0.13098f,0.00851f},{0.73661f,0.12477f,0.00769f},{0.72686f,0.11867f,0.00695f},{0.71692f,0.11268f,0.00629f},{0.70680f,0.10680f,0.00571f},{0.69650f,0.10102f,0.00522f},{0.68602f,0.09536f,0.00481f},{0.67535f,0.08980f,0.00449f},
                {0.66449f,0.08436f,0.00424f},{0.65345f,0.07902f,0.00408f},{0.64223f,0.07380f,0.00401f},{0.63082f,0.06868f,0.00401f},{0.61923f,0.06367f,0.00410f},{0.60746f,0.05878f,0.00427f},{0.59550f,0.05399f,0.00453f},{0.58336f,0.04931f,0.00486f},{0.57103f,0.04474f,0.00529f},{0.55852f,0.04028f,0.00579f},
                {0.54583f,0.03593f,0.00638f},{0.53295f,0.03169f,0.00705f},{0.51989f,0.02756f,0.00780f},{0.50664f,0.02354f,0.00863f},{0.49321f,0.01963f,0.00955f},{0.47960f,0.01583f,0.01055f}
            };

            var colors = new List<OxyPlot.OxyColor>();
            for (int i = 0; i < steps; i++)
            {
                double pos = (double)i / (steps - 1) * 255;
                int idx0 = (int)Math.Floor(pos);
                int idx1 = Math.Min(idx0 + 1, 255);
                double t = pos - idx0;

                // 线性插值并转为byte
                byte r = (byte)((turbo[idx0, 0] * (1 - t) + turbo[idx1, 0] * t) * 255.0);
                byte g = (byte)((turbo[idx0, 1] * (1 - t) + turbo[idx1, 1] * t) * 255.0);
                byte b = (byte)((turbo[idx0, 2] * (1 - t) + turbo[idx1, 2] * t) * 255.0);
                colors.Add(OxyPlot.OxyColor.FromRgb(r, g, b));
            }
            return new OxyPlot.OxyPalette(colors);
        }

        // 添加平滑处理函数
        private double[,] ApplySmoothing(double[,] grid, int size)
        {
            double[,] result = new double[size, size];

            // 复制边界值
            for (int i = 0; i < size; i++)
            {
                for (int j = 0; j < size; j++)
                {
                    result[i, j] = grid[i, j];
                }
            }

            // 应用简单的高斯平滑
            for (int i = 1; i < size - 1; i++)
            {
                for (int j = 1; j < size - 1; j++)
                {
                    if (double.IsNaN(grid[i, j]))
                        continue;

                    double sum = 0;
                    double weightSum = 0;

                    for (int di = -1; di <= 1; di++)
                    {
                        for (int dj = -1; dj <= 1; dj++)
                        {
                            if (!double.IsNaN(grid[i + di, j + dj]))
                            {
                                double weight = Math.Exp(-(di * di + dj * dj) / 2.0);
                                sum += grid[i + di, j + dj] * weight;
                                weightSum += weight;
                            }
                        }
                    }

                    if (weightSum > 0)
                    {
                        result[i, j] = sum / weightSum;
                    }
                }
            }

            return result;
        }

        // 生成等高线级别
        private double[] GenerateContourLevels(double min, double max, int count)
        {
            double[] levels = new double[count];
            double step = (max - min) / (count - 1);

            for (int i = 0; i < count; i++)
            {
                levels[i] = min + i * step;
            }

            return levels;
        }

        // Vertex2D 和 TriFace 用于 Delaunay 三角剖分
        public class Vertex2D : MIConvexHull.IVertex
        {
            public double[] Position { get; }
            public double Value { get; }
            public Vertex2D(double x, double y, double value)
            {
                Position = new[] { x, y };
                Value = value;
            }
            public double X => Position[0];
            public double Y => Position[1];
        }
        // TriFace 泛型继承修正
        public class TriFace : MIConvexHull.TriangulationCell<Vertex2D, TriFace> { }

        // 线性插值函数
        private double InterpolateLinear(MIConvexHull.DelaunayTriangulation<Vertex2D, TriFace> triangulation, double x, double y)
        {
            foreach (var tri in triangulation.Cells)
            {
                var v0 = tri.Vertices[0];
                var v1 = tri.Vertices[1];
                var v2 = tri.Vertices[2];
                if (PointInTriangle(x, y, v0, v1, v2))
                {
                    double l0, l1, l2;
                    Barycentric(x, y, v0, v1, v2, out l0, out l1, out l2);
                    return l0 * v0.Value + l1 * v1.Value + l2 * v2.Value;
                }
            }
            return double.NaN;
        }
        private bool PointInTriangle(double x, double y, Vertex2D v0, Vertex2D v1, Vertex2D v2)
        {
            double dX = x - v2.X;
            double dY = y - v2.Y;
            double dX21 = v2.X - v1.X;
            double dY12 = v1.Y - v2.Y;
            double D = dY12 * (v0.X - v2.X) + dX21 * (v0.Y - v2.Y);
            double s = dY12 * dX + dX21 * dY;
            double t = (v2.Y - v0.Y) * dX + (v0.X - v2.X) * dY;
            if (D < 0) return (s <= 0) && (t <= 0) && (s + t >= D);
            return (s >= 0) && (t >= 0) && (s + t <= D);
        }
        private void Barycentric(double x, double y, Vertex2D v0, Vertex2D v1, Vertex2D v2, out double l0, out double l1, out double l2)
        {
            double detT = (v1.Y - v2.Y) * (v0.X - v2.X) + (v2.X - v1.X) * (v0.Y - v2.Y);
            l0 = ((v1.Y - v2.Y) * (x - v2.X) + (v2.X - v1.X) * (y - v2.Y)) / detT;
            l1 = ((v2.Y - v0.Y) * (x - v2.X) + (v0.X - v2.X) * (y - v2.Y)) / detT;
            l2 = 1.0 - l0 - l1;
        }

        // 3x3 中位数滤波
        private double[,] MedianFilter2D(double[,] data, int size)
        {
            double[,] result = new double[size, size];
            
            for (int i = 0; i < size; i++)
            {
                for (int j = 0; j < size; j++)
                {
                    if (double.IsNaN(data[i, j]))
                    {
                        result[i, j] = double.NaN;
                        continue;
                    }
                    
                    var window = new List<double>();
                    
                    // 收集3x3窗口内的有效数据点
                    for (int di = -1; di <= 1; di++)
                    {
                        for (int dj = -1; dj <= 1; dj++)
                        {
                            int ni = i + di, nj = j + dj;
                            if (ni >= 0 && ni < size && nj >= 0 && nj < size && !double.IsNaN(data[ni, nj]))
                            {
                                window.Add(data[ni, nj]);
                            }
                        }
                    }
                    
                    if (window.Count == 0)
                    {
                        result[i, j] = data[i, j];
                    }
                    else
                    {
                        window.Sort();
                        result[i, j] = window[window.Count / 2];
                    }
                }
            }
            
            return result;
        }

        // 二维三次样条插值（行列两次，近似）
        private double[,] BicubicSplineInterpolate(double[,] data, double[] xGrid, double[] yGrid, int size)
        {
            var result = new double[size, size];
            // 先对每一行做一维样条
            var rowSplines = new MathNet.Numerics.Interpolation.CubicSpline[size];
            for (int i = 0; i < size; i++)
            {
                var valid = new List<Tuple<double, double>>();
                for (int j = 0; j < size; j++)
                    if (!double.IsNaN(data[i, j])) valid.Add(new Tuple<double, double>(yGrid[j], data[i, j]));
                if (valid.Count >= 4)
                    rowSplines[i] = MathNet.Numerics.Interpolation.CubicSpline.InterpolateNatural(valid.Select(v => v.Item1).ToArray(), valid.Select(v => v.Item2).ToArray());
                else
                    rowSplines[i] = null;
            }
            // 再对每一列做一维样条
            for (int j = 0; j < size; j++)
            {
                var colVals = new double[size];
                var validRows = new List<int>();
                for (int i = 0; i < size; i++)
                {
                    if (rowSplines[i] != null)
                    {
                        colVals[i] = rowSplines[i].Interpolate(yGrid[j]);
                        validRows.Add(i);
                    }
                    else
                        colVals[i] = double.NaN;
                }
                if (validRows.Count >= 4)
                {
                    var validX = validRows.Select(idx => xGrid[idx]).ToArray();
                    var validY = validRows.Select(idx => colVals[idx]).ToArray();
                    var spline = MathNet.Numerics.Interpolation.CubicSpline.InterpolateNatural(validX, validY);
                    for (int i = 0; i < size; i++)
                        result[i, j] = !double.IsNaN(colVals[i]) ? spline.Interpolate(xGrid[i]) : double.NaN;
                }
                else
                {
                    for (int i = 0; i < size; i++)
                        result[i, j] = double.NaN;
                }
            }
            return result;
        }
    }
}
