#!/usr/bin/env python3
"""
C#应用程序集成接口
用于从C#应用程序调用Python热力图生成功能
"""

import sys
import json
import argparse
from pathlib import Path
from film_thickness_heatmap import FilmThicknessHeatmap, load_data_from_json, load_data_from_csv

def main():
    """主函数，处理命令行参数并生成热力图"""
    parser = argparse.ArgumentParser(description='生成膜厚热力图')
    parser.add_argument('--data', required=True, help='数据文件路径（JSON或CSV）')
    parser.add_argument('--output', required=True, help='输出图像路径')
    parser.add_argument('--title', default='Film Thickness Heatmap', help='图表标题')
    parser.add_argument('--type', choices=['2d', '3d', 'both'], default='2d', help='图表类型')
    parser.add_argument('--grid-size', type=int, default=150, help='网格密度')
    parser.add_argument('--contour-levels', type=int, default=12, help='等高线数量')
    parser.add_argument('--width', type=int, default=10, help='图像宽度（英寸）')
    parser.add_argument('--height', type=int, default=8, help='图像高度（英寸）')
    parser.add_argument('--dpi', type=int, default=100, help='图像分辨率')
    parser.add_argument('--x-col', default='X', help='CSV文件中X坐标列名')
    parser.add_argument('--y-col', default='Y', help='CSV文件中Y坐标列名')
    parser.add_argument('--value-col', default='Value', help='CSV文件中数值列名')
    parser.add_argument('--no-show', action='store_true', help='不显示图表，仅保存')
    
    args = parser.parse_args()
    
    try:
        # 加载数据
        data_path = Path(args.data)
        if not data_path.exists():
            raise FileNotFoundError(f"数据文件不存在: {args.data}")
        
        if data_path.suffix.lower() == '.json':
            points = load_data_from_json(args.data)
        elif data_path.suffix.lower() == '.csv':
            points = load_data_from_csv(args.data, args.x_col, args.y_col, args.value_col)
        else:
            raise ValueError("不支持的文件格式，请使用JSON或CSV文件")
        
        print(f"成功加载 {len(points)} 个数据点")
        
        # 创建热力图生成器
        heatmap_generator = FilmThicknessHeatmap(
            grid_size=args.grid_size,
            contour_levels=args.contour_levels
        )
        
        # 生成图表
        output_path = Path(args.output)
        figsize = (args.width, args.height)
        show_plot = not args.no_show
        
        if args.type == '2d' or args.type == 'both':
            # 生成2D热力图
            output_2d = output_path.with_suffix('.2d.png') if args.type == 'both' else output_path
            fig_2d = heatmap_generator.create_heatmap(
                points=points,
                title=args.title,
                save_path=str(output_2d),
                show_plot=show_plot and args.type == '2d',
                figsize=figsize,
                dpi=args.dpi
            )
            print(f"2D热力图已保存到: {output_2d}")
        
        if args.type == '3d' or args.type == 'both':
            # 生成3D表面图
            output_3d = output_path.with_suffix('.3d.png') if args.type == 'both' else output_path
            fig_3d = heatmap_generator.create_3d_surface(
                points=points,
                title=args.title,
                save_path=str(output_3d),
                show_plot=show_plot and args.type == '3d',
                figsize=figsize,
                dpi=args.dpi
            )
            print(f"3D表面图已保存到: {output_3d}")
        
        print("热力图生成完成！")
        return 0
        
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        return 1

def export_data_from_csharp(slot_name: str, points_data: str, output_dir: str = ".") -> dict:
    """
    从C#应用程序导出的数据生成热力图
    
    Args:
        slot_name: 槽位名称
        points_data: JSON格式的点数据字符串
        output_dir: 输出目录
        
    Returns:
        包含生成结果的字典
    """
    try:
        # 解析JSON数据
        points = json.loads(points_data)
        
        if not points:
            return {"success": False, "error": "没有数据点"}
        
        # 创建热力图生成器
        heatmap_generator = FilmThicknessHeatmap(grid_size=150, contour_levels=12)
        
        # 生成文件路径
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        safe_slot_name = "".join(c for c in slot_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        output_2d = output_dir / f"{safe_slot_name}_heatmap_2d.png"
        output_3d = output_dir / f"{safe_slot_name}_heatmap_3d.png"
        
        # 生成2D热力图
        fig_2d = heatmap_generator.create_heatmap(
            points=points,
            title=f"{slot_name} Thickness Map",
            save_path=str(output_2d),
            show_plot=False,
            figsize=(8, 6),
            dpi=150
        )
        
        # 生成3D表面图
        fig_3d = heatmap_generator.create_3d_surface(
            points=points,
            title=f"{slot_name} Thickness 3D",
            save_path=str(output_3d),
            show_plot=False,
            figsize=(10, 8),
            dpi=150
        )
        
        return {
            "success": True,
            "slot_name": slot_name,
            "points_count": len(points),
            "output_2d": str(output_2d),
            "output_3d": str(output_3d)
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    # 检查是否是从C#调用的特殊模式
    if len(sys.argv) > 1 and sys.argv[1] == "--csharp-mode":
        # C#集成模式
        if len(sys.argv) != 5:
            print("用法: python csharp_integration.py --csharp-mode <slot_name> <points_json> <output_dir>")
            sys.exit(1)
        
        slot_name = sys.argv[2]
        points_json = sys.argv[3]
        output_dir = sys.argv[4]
        
        result = export_data_from_csharp(slot_name, points_json, output_dir)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        # 命令行模式
        sys.exit(main())
