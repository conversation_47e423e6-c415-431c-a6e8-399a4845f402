using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;

namespace DatalogDrawing
{
    public partial class ConfigEditorWindow : Window, INotifyPropertyChanged
    {
        private ConfigService _configService;
        private AppConfig _originalConfig;
        private AppConfig _config;

        public AppConfig Config 
        { 
            get => _config;
            set => SetField(ref _config, value);
        }
        
        public ICommand SaveConfigCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ExportConfigCommand { get; }
        public ICommand ImportConfigCommand { get; }
        public ICommand RemoveColumnCommand { get; }
        public ICommand AddColumnCommand { get; }
        public ICommand AddRowFilterCommand { get; }
        public ICommand RemoveRowFilterCommand { get; }

        public ConfigEditorWindow(ConfigService configService)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            
            // 创建配置的深拷贝以支持取消操作
            _originalConfig = CloneConfig(configService.Config);
            Config = CloneConfig(_originalConfig);

            // 初始化命令
            SaveConfigCommand = new RelayCommand<object>(param => SaveConfig());
            CancelCommand = new RelayCommand<object>(param => Cancel());
            ExportConfigCommand = new RelayCommand<object>(param => ExportConfig());
            ImportConfigCommand = new RelayCommand<object>(param => ImportConfig());
            RemoveColumnCommand = new RelayCommand<string>(RemoveColumn);
            AddColumnCommand = new RelayCommand<object>(param => AddColumn());
            AddRowFilterCommand = new RelayCommand<object>(param => AddRowFilter());
            RemoveRowFilterCommand = new RelayCommand<RowFilter>(RemoveRowFilter);

            // 在命令初始化后再设置DataContext
            DataContext = this;
            
            InitializeComponent();
        }

        private AppConfig CloneConfig(AppConfig source)
        {
            // 使用 Newtonsoft.Json 替代 System.Text.Json 实现深拷贝
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(source);
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
        }

        private void SaveConfig()
        {
            _configService.Config = CloneConfig(Config);
            if (_configService.SaveConfig())
            {
                DialogResult = true;
                Close();
            }
        }

        private void Cancel()
        {
            Config = CloneConfig(_originalConfig);
            Close();
        }

        private void ExportConfig()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "JSON配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                DefaultExt = "json",
                FileName = "csv_config_export.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                _configService.ExportConfig(saveFileDialog.FileName);
            }
        }

        private void ImportConfig()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "JSON配置文件 (*.json)|*.json|所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _configService.ImportConfig(openFileDialog.FileName);
                    Config = CloneConfig(_configService.Config);
                    _originalConfig = CloneConfig(Config);
                    MessageBox.Show("配置导入成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"配置导入失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void RemoveColumn(string column)
        {
            if (Config?.DisplayConfig?.TableColumns != null && Config.DisplayConfig.TableColumns.Contains(column))
            {
                Config.DisplayConfig.TableColumns.Remove(column);
                OnPropertyChanged(nameof(Config));
            }
        }

        private void AddColumn()
        {
            if (Config?.DisplayConfig?.TableColumns != null)
            {
                Config.DisplayConfig.TableColumns.Add("新列");
                OnPropertyChanged(nameof(Config));
            }
        }

        private void AddRowFilter()
        {
            if (Config?.CsvParserConfig?.RowFilters != null)
            {
                Config.CsvParserConfig.RowFilters.Add(new RowFilter
                {
                    Name = $"过滤器{Config.CsvParserConfig.RowFilters.Count + 1}",
                    Enabled = true,
                    FilterType = "RowValue"
                });
                OnPropertyChanged(nameof(Config));
            }
        }

        private void RemoveRowFilter(RowFilter filter)
        {
            if (Config?.CsvParserConfig?.RowFilters != null && filter != null)
            {
                Config.CsvParserConfig.RowFilters.Remove(filter);
                OnPropertyChanged(nameof(Config));
            }
        }

        // INotifyPropertyChanged 实现
        public event PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetField<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}