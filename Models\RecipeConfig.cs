using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace DatalogDrawing.Models
{
    /// <summary>
    /// Recipe配置根类
    /// </summary>
    public class RecipeConfig
    {
        [JsonProperty("csv_parser_config")]
        public RecipeCsvParserConfig CsvParserConfig { get; set; } = new RecipeCsvParserConfig();

        [JsonProperty("display_config")]
        public RecipeDisplayConfig DisplayConfig { get; set; } = new RecipeDisplayConfig();
    }

    /// <summary>
    /// Recipe CSV解析配置
    /// </summary>
    public class RecipeCsvParserConfig
    {
        [JsonProperty("delimiter")]
        public string Delimiter { get; set; } = ",";

        [JsonProperty("encoding")]
        public string Encoding { get; set; } = "UTF-8";

        [JsonProperty("has_header")]
        public bool HasHeader { get; set; } = true;

        [JsonProperty("skip_empty_lines")]
        public bool SkipEmptyLines { get; set; } = true;

        [JsonProperty("row_filters")]
        public List<RecipeRowFilter> RowFilters { get; set; } = new List<RecipeRowFilter>();

        [JsonProperty("column_mapping")]
        public Dictionary<string, string> ColumnMapping { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// Recipe行过滤器
    /// </summary>
    public class RecipeRowFilter
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("enabled")]
        public bool Enabled { get; set; } = true;

        [JsonProperty("filter_type")]
        public string FilterType { get; set; }

        [JsonProperty("startCondition")]
        public string StartCondition { get; set; }

        [JsonProperty("startValue")]
        public string StartValue { get; set; }

        [JsonProperty("endCondition")]
        public string EndCondition { get; set; }

        [JsonProperty("endValue")]
        public string EndValue { get; set; }

        [JsonProperty("data_type")]
        public string DataType { get; set; }
    }

    /// <summary>
    /// Recipe显示配置
    /// </summary>
    public class RecipeDisplayConfig
    {
        [JsonProperty("columns")]
        public List<RecipeColumnConfig> Columns { get; set; } = new List<RecipeColumnConfig>();
    }

    /// <summary>
    /// Recipe列配置
    /// </summary>
    public class RecipeColumnConfig
    {
        [JsonProperty("field")]
        public string Field { get; set; }

        [JsonProperty("header")]
        public string Header { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("source")]
        public string Source { get; set; }

        [JsonProperty("sources")]
        public List<string> Sources { get; set; }

        [JsonProperty("format")]
        public string Format { get; set; }

        [JsonProperty("conditions")]
        public List<RecipeColumnCondition> Conditions { get; set; }

        [JsonProperty("default")]
        public RecipeColumnDefault Default { get; set; }

        [JsonProperty("selectors")]
        public List<RecipeColumnSelector> Selectors { get; set; }

        [JsonProperty("fallback")]
        public string Fallback { get; set; }

        [JsonProperty("fallback_sources")]
        public List<string> FallbackSources { get; set; }
    }

    /// <summary>
    /// Recipe列条件配置
    /// </summary>
    public class RecipeColumnCondition
    {
        [JsonProperty("check")]
        public string Check { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonProperty("result")]
        public string Result { get; set; }

        [JsonProperty("format")]
        public string Format { get; set; }

        [JsonProperty("sources")]
        public List<string> Sources { get; set; }
    }

    /// <summary>
    /// Recipe列默认配置
    /// </summary>
    public class RecipeColumnDefault
    {
        [JsonProperty("format")]
        public string Format { get; set; }

        [JsonProperty("sources")]
        public List<string> Sources { get; set; }
    }

    /// <summary>
    /// Recipe列选择器配置
    /// </summary>
    public class RecipeColumnSelector
    {
        [JsonProperty("check_field")]
        public string CheckField { get; set; }

        [JsonProperty("check")]
        public string Check { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonProperty("format")]
        public string Format { get; set; }

        [JsonProperty("sources")]
        public List<string> Sources { get; set; }
    }
}