using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace DatalogDrawing.Models
{
    /// <summary>
    /// Recipe配置根类
    /// </summary>
    public class RecipeConfig
    {
        [JsonProperty("csv_parser_config")]
        public RecipeCsvParserConfig CsvParserConfig { get; set; } = new RecipeCsvParserConfig();

        [JsonProperty("display_config")]
        public RecipeDisplayConfig DisplayConfig { get; set; } = new RecipeDisplayConfig();
    }

    /// <summary>
    /// Recipe CSV解析配置
    /// </summary>
    public class RecipeCsvParserConfig
    {
        [JsonProperty("delimiter")]
        public string Delimiter { get; set; } = ",";

        [JsonProperty("encoding")]
        public string Encoding { get; set; } = "UTF-8";

        [JsonProperty("has_header")]
        public bool HasHeader { get; set; } = true;

        [JsonProperty("skip_empty_lines")]
        public bool SkipEmptyLines { get; set; } = true;

        [JsonProperty("row_filters")]
        public List<RecipeRowFilter> RowFilters { get; set; } = new List<RecipeRowFilter>();

        [JsonProperty("column_mapping")]
        public Dictionary<string, string> ColumnMapping { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// Recipe行过滤器
    /// </summary>
    public class RecipeRowFilter
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("enabled")]
        public bool Enabled { get; set; } = true;

        [JsonProperty("filter_type")]
        public string FilterType { get; set; }

        [JsonProperty("startCondition")]
        public string StartCondition { get; set; }

        [JsonProperty("startValue")]
        public string StartValue { get; set; }

        [JsonProperty("endCondition")]
        public string EndCondition { get; set; }

        [JsonProperty("endValue")]
        public string EndValue { get; set; }

        [JsonProperty("data_type")]
        public string DataType { get; set; }
    }

    /// <summary>
    /// Recipe显示配置
    /// </summary>
    public class RecipeDisplayConfig
    {
        // 简化的显示配置，可以根据需要扩展
    }
}
