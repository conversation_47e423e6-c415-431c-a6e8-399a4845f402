using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using DatalogDrawing.Models;  // 确保引用Models命名空间

namespace DatalogDrawing.Views
{
    public partial class DisplayConfigWindow : Window
    {
        public ObservableCollection<SlotDisplayViewModel> SlotItems { get; } = new ObservableCollection<SlotDisplayViewModel>();
        public string EquipmentName { get; private set; }
        public int DecimalPlaces { get; private set; }
        public double UserInputValue { get; private set; }
        public Dictionary<string, string> SlotDisplayNames { get; private set; } = new Dictionary<string, string>();
        
        private Dictionary<string, SlotInfo> _slotInfos;

        public DisplayConfigWindow(Dictionary<string, SlotInfo> slotInfos, Dictionary<string, string> existingDisplayNames = null,
                                  string equipmentName = "KLASFX200(@ 49Point)", int decimalPlaces = 2, double userInputValue = 1.0)
        {
            InitializeComponent();
            
            _slotInfos = slotInfos;
            
            // 设置初始值
            EquipmentNameTextBox.Text = equipmentName;
            DecimalPlacesComboBox.SelectedIndex = Math.Min(Math.Max(decimalPlaces, 0), 5);
            UserInputValueTextBox.Text = userInputValue.ToString();
            
            // 填充槽位数据
            foreach (var slot in slotInfos)
            {
                string displayName;
                if (existingDisplayNames != null && existingDisplayNames.TryGetValue(slot.Key, out displayName))
                {
                    // 使用已存在的显示名称
                }
                else
                {
                    // 使用实际的Slot ID作为默认名称
                    displayName = slot.Key;
                }

                SlotItems.Add(new SlotDisplayViewModel
                {
                    SlotNumber = slot.Key,
                    WaferId = slot.Value.WaferId,
                    DisplayName = displayName
                });
            }
            
            SlotNamesDataGrid.ItemsSource = SlotItems;
            
            // 如果有已存在的显示名称，取消选中默认名称选项
            if (existingDisplayNames != null && existingDisplayNames.Count > 0)
            {
                UseDefaultNamesCheckBox.IsChecked = false;
            }
            else
            {
                UseDefaultNamesCheckBox.IsChecked = true;
            }
        }

        private void Confirm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取设备名称
                EquipmentName = EquipmentNameTextBox.Text;
                
                // 获取小数位数
                DecimalPlaces = DecimalPlacesComboBox.SelectedIndex;
                
                // 获取用户输入值
                if (!double.TryParse(UserInputValueTextBox.Text, out double userInput))
                {
                    MessageBox.Show("请输入有效的用户输入值", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                UserInputValue = userInput;
                
                // 获取槽位显示名称
                foreach (var item in SlotItems)
                {
                    if (!string.IsNullOrWhiteSpace(item.DisplayName))
                    {
                        SlotDisplayNames[item.SlotNumber] = item.DisplayName;
                    }
                }
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void UseDefaultNamesCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (var item in SlotItems)
            {
                item.DisplayName = item.SlotNumber;
            }
            SlotNamesDataGrid.Items.Refresh();
        }
    }

    public class SlotDisplayViewModel : INotifyPropertyChanged
    {
        private string _displayName;

        public string SlotNumber { get; set; }
        public string WaferId { get; set; }

        public string DisplayName
        {
            get => _displayName;
            set
            {
                if (_displayName != value)
                {
                    _displayName = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 