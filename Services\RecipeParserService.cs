using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DatalogDrawing.Models;
using DatalogDrawing.Interfaces;

namespace DatalogDrawing.Services
{
    /// <summary>
    /// Recipe解析服务，复用现有解析框架
    /// </summary>
    public class RecipeParserService : BaseParser, IDataParser<RecipeData>
    {
        private readonly RecipeConfigService _configService;

        public RecipeParserService()
        {
            _configService = new RecipeConfigService();
        }

        public RecipeParserService(RecipeConfigService configService)
        {
            _configService = configService ?? new RecipeConfigService();
        }

        public override Dictionary<string, object> Parse(string filePath, AppConfig config)
        {
            var recipeData = ParseData(filePath, config);
            var result = new Dictionary<string, object>
            {
                ["RecipeData"] = recipeData,
                ["Steps"] = recipeData.Steps.ToList(),
                ["TotalTime"] = recipeData.TotalTime,
                ["StepCount"] = recipeData.StepCount
            };
            return result;
        }

        public RecipeData ParseData(string filePath, AppConfig config)
        {
            return ParseRecipeFile(filePath);
        }

        public Task<Dictionary<string, object>> ParseAsync(string filePath, AppConfig config)
        {
            return Task.FromResult(Parse(filePath, config));
        }

        /// <summary>
        /// 解析Recipe CSV文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>Recipe数据</returns>
        public RecipeData ParseRecipeFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException("Recipe文件未找到", filePath);

            var config = _configService.CurrentConfig;
            var recipeData = new RecipeData
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath,
                Type = DetermineRecipeType(filePath),
                ImportTime = DateTime.Now
            };

            var lines = File.ReadAllLines(filePath);
            if (lines.Length < 2) // 至少需要标题行和一行数据
                throw new InvalidDataException("Recipe文件格式无效");

            // 解析标题行
            var headers = ParseCsvLine(lines[0], config.CsvParserConfig.Delimiter);
            var headerMap = CreateHeaderMap(headers);

            // 解析数据行
            for (int i = 1; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (string.IsNullOrEmpty(line) && config.CsvParserConfig.SkipEmptyLines) continue;

                try
                {
                    var values = ParseCsvLine(line, config.CsvParserConfig.Delimiter);
                    var step = ParseRecipeStep(values, headerMap);
                    recipeData.Steps.Add(step);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"解析第{i + 1}行时出错: {ex.Message}");
                    // 继续解析其他行
                }
            }

            return recipeData;
        }

        /// <summary>
        /// 确定Recipe类型
        /// </summary>
        private RecipeType DetermineRecipeType(string filePath)
        {
            var fileName = Path.GetFileName(filePath).ToLower();
            if (fileName.Contains("sub") || fileName.Contains("子"))
                return RecipeType.SubRecipe;
            return RecipeType.MainRecipe;
        }

        /// <summary>
        /// 创建标题映射
        /// </summary>
        private Dictionary<string, int> CreateHeaderMap(List<string> headers)
        {
            var map = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            for (int i = 0; i < headers.Count; i++)
            {
                var headerName = headers[i].Trim();
                // 使用配置映射
                var mappedName = _configService.GetMappedFieldName(headerName);
                map[mappedName] = i;
                // 同时保留原始名称映射
                map[headerName] = i;
            }
            return map;
        }

        /// <summary>
        /// 解析Recipe步骤
        /// </summary>
        private RecipeStep ParseRecipeStep(List<string> values, Dictionary<string, int> headerMap)
        {
            var step = new RecipeStep();

            // 基本信息
            step.StepNo = GetIntValue(values, headerMap, "StepNo");
            step.StepName = GetStringValue(values, headerMap, "StepName");
            step.StepTime = ParseTimeSpan(GetStringValue(values, headerMap, "StepTime"));
            step.StepUpCondition = GetStringValue(values, headerMap, "StepUpCondition");
            step.StepUpConditionTargetValue = GetDoubleValue(values, headerMap, "StepUpConditionTargetValue");
            step.CommandCode = GetStringValue(values, headerMap, "CommandCode");

            // 升降机和舟转动
            step.ElevatorCommand = GetIntValue(values, headerMap, "ElevatorCommand");
            step.ElevatorSpeed = GetIntValue(values, headerMap, "ElevatorSpeed");
            step.BoatRotateCommand = GetIntValue(values, headerMap, "BoatRotateCommand");
            step.BoatRotateSpeed = GetIntValue(values, headerMap, "BoatRotateSpeed");

            // 温度设置
            step.TempZone1Set = GetDoubleValue(values, headerMap, "TempZone1Set");
            step.TempZone2Set = GetDoubleValue(values, headerMap, "TempZone2Set");
            step.TempZone3Set = GetDoubleValue(values, headerMap, "TempZone3Set");
            step.TempZone4Set = GetDoubleValue(values, headerMap, "TempZone4Set");
            step.TempZone5Set = GetDoubleValue(values, headerMap, "TempZone5Set");
            step.TempZone6Set = GetDoubleValue(values, headerMap, "TempZone6Set");
            step.TempZone7Set = GetDoubleValue(values, headerMap, "TempZone7Set");
            step.TempZone8Set = GetDoubleValue(values, headerMap, "TempZone8Set");

            // 温度斜率设置
            step.TempZone1RampSet = GetDoubleValue(values, headerMap, "TempZone1RampSet");
            step.TempZone2RampSet = GetDoubleValue(values, headerMap, "TempZone2RampSet");
            step.TempZone3RampSet = GetDoubleValue(values, headerMap, "TempZone3RampSet");
            step.TempZone4RampSet = GetDoubleValue(values, headerMap, "TempZone4RampSet");
            step.TempZone5RampSet = GetDoubleValue(values, headerMap, "TempZone5RampSet");
            step.TempZone6RampSet = GetDoubleValue(values, headerMap, "TempZone6RampSet");
            step.TempZone7RampSet = GetDoubleValue(values, headerMap, "TempZone7RampSet");
            step.TempZone8RampSet = GetDoubleValue(values, headerMap, "TempZone8RampSet");

            // 其他设置
            step.TempControlMode = GetStringValue(values, headerMap, "TempControlMode");
            step.LoopCount = GetIntValue(values, headerMap, "LoopCount");

            // APC设置
            step.APC2ControlMode = GetStringValue(values, headerMap, "APC2ControlMode");
            step.APC2Set = GetDoubleValue(values, headerMap, "APC2Set");
            step.APC2RampSet = GetDoubleValue(values, headerMap, "APC2RampSet");
            step.APC3ControlMode = GetStringValue(values, headerMap, "APC3ControlMode");
            step.APC3Set = GetDoubleValue(values, headerMap, "APC3Set");
            step.APC3RampSet = GetDoubleValue(values, headerMap, "APC3RampSet");

            // 子配方
            step.SubRecipeName = GetStringValue(values, headerMap, "SubRecipeName");
            step.CallCount = GetIntValue(values, headerMap, "CallCount");

            return step;
        }

        /// <summary>
        /// 解析时间字符串
        /// </summary>
        private TimeSpan ParseTimeSpan(string timeStr)
        {
            if (string.IsNullOrEmpty(timeStr)) return TimeSpan.Zero;
            
            if (TimeSpan.TryParse(timeStr, out var result))
                return result;
            
            return TimeSpan.Zero;
        }

        /// <summary>
        /// 获取字符串值
        /// </summary>
        private string GetStringValue(List<string> values, Dictionary<string, int> headerMap, string columnName)
        {
            if (headerMap.TryGetValue(columnName, out var index) && index < values.Count)
                return values[index]?.Trim() ?? "";
            return "";
        }

        /// <summary>
        /// 获取整数值
        /// </summary>
        private int GetIntValue(List<string> values, Dictionary<string, int> headerMap, string columnName)
        {
            var strValue = GetStringValue(values, headerMap, columnName);
            return int.TryParse(strValue, out var result) ? result : 0;
        }

        /// <summary>
        /// 获取双精度值
        /// </summary>
        private double GetDoubleValue(List<string> values, Dictionary<string, int> headerMap, string columnName)
        {
            var strValue = GetStringValue(values, headerMap, columnName);
            return double.TryParse(strValue, out var result) ? result : 0.0;
        }


    }
}
