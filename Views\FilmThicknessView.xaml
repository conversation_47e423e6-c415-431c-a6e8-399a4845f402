<UserControl x:Class="DatalogDrawing.Views.FilmThicknessView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:local="clr-namespace:DatalogDrawing.Views"
             xmlns:oxy="http://oxyplot.org/wpf"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1100">
    <UserControl.Resources>
        <Style TargetType="DataGridCell">
            <Setter Property="Padding" Value="5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="LightGray"/>
                </Trigger>
                <DataTrigger Binding="{Binding Path=(Grid.ColumnSpan), RelativeSource={RelativeSource Self}}" Value="2">
                    <Setter Property="BorderThickness" Value="1,1,0,1"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Path=(Grid.ColumnSpan), RelativeSource={RelativeSource Self}}" Value="3">
                    <Setter Property="BorderThickness" Value="1,1,0,1"/>
                </DataTrigger>
                <Trigger Property="Visibility" Value="Collapsed">
                    <Setter Property="BorderThickness" Value="0"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        <DataTemplate x:Key="TextCellTemplate">
            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
        </DataTemplate>
        <DataTemplate x:Key="PlotCellTemplate">
            <oxy:PlotView Model="{Binding}" Height="120"/>
        </DataTemplate>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 文件选择区域 -->
        <GroupBox Grid.Row="0" Header="📊 膜厚分析 (Film Thickness)" Margin="5">
            <StackPanel>
                <!-- 文件选择控件 -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="CSV文件:" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                    <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding CsvFiles}" SelectedItem="{Binding SelectedCsvFile}" DisplayMemberPath="FileName" Margin="5"/>
                    <Button Grid.Row="0" Grid.Column="2" Content="📁 浏览..." Command="{Binding ImportCsvCommand}" Margin="5" Padding="8,4"/>
                    <Button Grid.Row="0" Grid.Column="3" Content="🗑️ 清空" Command="{Binding ClearCsvsCommand}" Margin="5" Padding="8,4"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="配置文件:" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                    <ComboBox Grid.Row="1" Grid.Column="1" ItemsSource="{Binding ConfigFiles}" SelectedItem="{Binding SelectedConfigFile}" DisplayMemberPath="FileName" Margin="5"/>
                    <Button Grid.Row="1" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectConfigCommand}" Margin="5" Padding="8,4"/>
                    <Button Grid.Row="1" Grid.Column="3" Content="🗑️ 移除" Command="{Binding RemoveConfigCommand}" Margin="5" Padding="8,4"/>
                </Grid>

                <!-- 当前选中文件信息显示 -->
                <Border BorderBrush="DarkBlue" BorderThickness="1" Margin="5,10,5,5" Padding="10" Background="AliceBlue" CornerRadius="3" Visibility="Visible">
                    <StackPanel>
                        <TextBlock Text="📁 当前选中文件" FontSize="12" FontWeight="Bold" Foreground="DarkBlue" Margin="0,0,0,8"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="📊 CSV:" FontWeight="Bold" Margin="0,0,8,0" Foreground="DarkBlue"/>
                            <TextBlock Grid.Column="1" Margin="0,0,20,0" FontFamily="Consolas">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0} (路径: {1})">
                                        <Binding Path="SelectedCsvFile.FileName" FallbackValue="未选择"/>
                                        <Binding Path="SelectedCsvFile.FilePath" FallbackValue="无路径"/>
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>

                            <TextBlock Grid.Column="2" Text="⚙️ 配置:" FontWeight="Bold" Margin="0,0,8,0" Foreground="DarkBlue"/>
                            <TextBlock Grid.Column="3" Margin="0,0,20,0" FontFamily="Consolas">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0} (路径: {1})">
                                        <Binding Path="SelectedConfigFile.FileName" FallbackValue="未选择"/>
                                        <Binding Path="SelectedConfigFile.FilePath" FallbackValue="无路径"/>
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>

                            <TextBlock Grid.Column="4" Text="📈 状态:" FontWeight="Bold" Margin="0,0,8,0" Foreground="DarkBlue"/>
                            <TextBlock Grid.Column="5" Text="{Binding DataStatus, FallbackValue='状态未知'}" FontFamily="Consolas"/>
                        </Grid>

                        <!-- 解析时间信息 -->
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,0" Visibility="Visible">
                            <TextBlock Text="🕒 解析时间:" FontWeight="Bold" Margin="0,0,8,0" Foreground="DarkBlue"/>
                            <TextBlock Text="{Binding ParseTime, FallbackValue='未解析'}" FontFamily="Consolas"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </GroupBox>

        <!-- 操作按钮区域 -->
        <Border Grid.Row="1" Background="LightGray" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🔍 解析" Command="{Binding ParseCommand}" Margin="5" Padding="15,8" FontWeight="Bold" Background="DodgerBlue" Foreground="White"/>
                <Button Content="📤 导出结果" Command="{Binding ExportCommand}" Margin="5" Padding="15,8" FontWeight="Bold" Background="Green" Foreground="White"/>
                <Button Content="🗑️ 清除数据" Command="{Binding ClearDataCommand}" Margin="5" Padding="15,8" FontWeight="Bold" Background="OrangeRed" Foreground="White"/>
                <TextBlock Text="{Binding ErrorMessage}" Foreground="Red" Margin="10,0,0,0" VerticalAlignment="Center" TextWrapping="Wrap"/>
            </StackPanel>
        </Border>

        <!-- 结果显示区域 -->
        <TabControl Grid.Row="2" Margin="5" SelectedIndex="{Binding SelectedTabIndex}">
            <!-- 图表可视化选项卡 -->
            <TabItem Header="📊 图表可视化">
                <Grid>
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                        <Grid x:Name="ResultGrid" Margin="5">
                            <!-- 动态生成行列 -->
                        </Grid>
                    </ScrollViewer>

                    <!-- 空状态提示 -->
                    <TextBlock Text="请先选择CSV文件和配置文件，然后点击解析按钮"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               FontSize="16"
                               Foreground="Gray">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsDataEmpty}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </Grid>
            </TabItem>
            
            <!-- 数据表格选项卡 -->
            <TabItem Header="📋 数据表格">
                <TabControl>
                    <TabItem Header="统计数据">
                        <DataGrid x:Name="StatisticsDataGrid"
                                  ItemsSource="{Binding StatisticsTable}"
                                  AutoGenerateColumns="True"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="All"
                                  AlternatingRowBackground="LightBlue"/>
                    </TabItem>
                    <TabItem Header="明细数据">
                        <DataGrid x:Name="DetailDataGrid"
                                  ItemsSource="{Binding DetailTable}"
                                  AutoGenerateColumns="True"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="All"
                                  AlternatingRowBackground="LightGreen"/>
                    </TabItem>
                </TabControl>
            </TabItem>

            <!-- 统计信息选项卡 -->
            <TabItem Header="📈 统计信息">
                <ScrollViewer>
                    <StackPanel Margin="20">
                        <!-- 标题和操作按钮 -->
                        <Grid Margin="0,0,0,20">
                            <TextBlock Text="膜厚分析统计信息" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <Button Content="📋 详细报告" Command="{Binding ShowDetailedReportCommand}" Margin="5" Padding="10,5" Background="LightBlue"/>
                                <Button Content="📤 导出统计" Command="{Binding ExportStatisticsCommand}" Margin="5" Padding="10,5" Background="LightGreen"/>
                            </StackPanel>
                        </Grid>



                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 左侧统计信息 -->
                            <Border Grid.Column="0" BorderBrush="DarkBlue" BorderThickness="2" Margin="0,0,10,0" Padding="15" Background="AliceBlue">
                                <StackPanel>
                                    <TextBlock Text="📊 基本统计" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="DarkBlue"/>
                                    <TextBlock Text="{Binding StatisticsSummary, FallbackValue='暂无统计数据'}" FontSize="12" TextWrapping="Wrap" LineHeight="18"/>
                                </StackPanel>
                            </Border>

                            <!-- 右侧详细信息 -->
                            <Border Grid.Column="1" BorderBrush="DarkGreen" BorderThickness="2" Margin="10,0,0,0" Padding="15" Background="Honeydew">
                                <StackPanel>
                                    <TextBlock Text="📋 详细信息" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="DarkGreen"/>
                                    <TextBlock Text="{Binding DetailSummary, FallbackValue='暂无详细信息'}" FontSize="12" TextWrapping="Wrap" LineHeight="18"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- 数据质量指示器 -->
                        <Border BorderBrush="Orange" BorderThickness="2" Margin="0,20,0,0" Padding="15" Background="FloralWhite">
                            <StackPanel>
                                <TextBlock Text="🎯 数据质量评估" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="DarkOrange"/>
                                <TextBlock Text="{Binding DataQualityInfo, FallbackValue='暂无数据质量信息'}" FontSize="12" TextWrapping="Wrap" LineHeight="18"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>


        </TabControl>
    </Grid>
</UserControl> 