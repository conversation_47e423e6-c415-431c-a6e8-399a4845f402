using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using DatalogDrawing.Models;
using DatalogDrawing.Interfaces;

namespace DatalogDrawing.Services
{
    public abstract class BaseParser
    {
        public virtual Dictionary<string, object> Parse(string filePath, AppConfig config)
        {
            // 基础实现，子类应该重写此方法
            throw new NotImplementedException("子类必须重写Parse方法");
        }
        
        public virtual string ReadFileContent(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException("文件未找到", filePath);

            using (var reader = new StreamReader(filePath))
            {
                return reader.ReadToEnd();
            }
        }
        
        protected virtual List<string> ApplyFilters(List<string> data, Dictionary<string, object> config)
        {
            if (config == null) return data;

            if (config.ContainsKey("row_filters"))
            {
                var filters = config["row_filters"] as Dictionary<string, object>;
                if (filters != null)
                {
                    foreach (var filter in filters)
                    {
                        data = data.Where(row =>
                            Regex.IsMatch(row, filter.Value.ToString()))
                            .ToList();
                    }
                }
            }
            return data;
        }

        protected virtual List<string> ParseCsvLine(string line, string delimiter)
        {
            return line.Split(new[] { delimiter }, StringSplitOptions.None).ToList();
        }
    }
}