using System;
using System.Collections.Generic;
using System.IO;
using DatalogDrawing.Models;
using Newtonsoft.Json;

namespace DatalogDrawing.Services
{
    /// <summary>
    /// Recipe配置服务
    /// </summary>
    public class RecipeConfigService
    {
        private const string DEFAULT_CONFIG_FILE = "RecipeConfig.json";
        private RecipeConfig _currentConfig;

        /// <summary>
        /// 当前配置
        /// </summary>
        public RecipeConfig CurrentConfig
        {
            get
            {
                if (_currentConfig == null)
                {
                    LoadDefaultConfig();
                }
                return _currentConfig;
            }
            private set => _currentConfig = value;
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        public void LoadDefaultConfig()
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, DEFAULT_CONFIG_FILE);
            LoadConfig(configPath);
        }

        /// <summary>
        /// 加载指定配置文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        public void LoadConfig(string configPath)
        {
            if (!File.Exists(configPath))
            {
                throw new FileNotFoundException($"Recipe配置文件不存在: {configPath}");
            }

            try
            {
                var json = File.ReadAllText(configPath);
                CurrentConfig = JsonConvert.DeserializeObject<RecipeConfig>(json);
                
                // 验证配置
                if (CurrentConfig?.DisplayConfig?.Columns == null || CurrentConfig.DisplayConfig.Columns.Count == 0)
                {
                    throw new InvalidOperationException("配置文件格式错误：DisplayConfig.Columns为空");
                }
                
                System.Diagnostics.Debug.WriteLine($"Recipe配置加载成功: {configPath}");
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"Recipe配置文件格式错误: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载Recipe配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        public void SaveConfig(string configPath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(CurrentConfig, Formatting.Indented);
                File.WriteAllText(configPath, json);
                System.Diagnostics.Debug.WriteLine($"Recipe配置保存成功: {configPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存Recipe配置失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private RecipeConfig CreateDefaultConfig()
        {
            var config = new RecipeConfig();

            // CSV解析配置
            config.CsvParserConfig.Delimiter = ",";
            config.CsvParserConfig.Encoding = "UTF-8";
            config.CsvParserConfig.HasHeader = true;
            config.CsvParserConfig.SkipEmptyLines = true;

            // 基本字段映射
            var mapping = config.CsvParserConfig.ColumnMapping;
            mapping["StepNo"] = "StepNo";
            mapping["StepName"] = "StepName";
            mapping["StepTime"] = "StepTime";
            mapping["StepUpCondition"] = "StepUpCondition";
            mapping["StepUpConditionTargetValue"] = "StepUpConditionTargetValue";
            mapping["CommandCode"] = "CommandCode";
            mapping["ElevatorCommand"] = "ElevatorCommand";
            mapping["ElevatorSpeed"] = "ElevatorSpeed";
            mapping["BoatRotateCommand"] = "BoatRotateCommand";
            mapping["BoatRotateSpeed"] = "BoatRotateSpeed";
            mapping["TempZone1Set"] = "TempZone1Set";
            mapping["TempZone2Set"] = "TempZone2Set";
            mapping["TempZone3Set"] = "TempZone3Set";
            mapping["TempZone4Set"] = "TempZone4Set";
            mapping["TempZone5Set"] = "TempZone5Set";
            mapping["TempZone6Set"] = "TempZone6Set";
            mapping["TempZone7Set"] = "TempZone7Set";
            mapping["TempZone8Set"] = "TempZone8Set";
            mapping["TempZone1RampSet"] = "TempZone1RampSet";
            mapping["TempZone2RampSet"] = "TempZone2RampSet";
            mapping["TempZone3RampSet"] = "TempZone3RampSet";
            mapping["TempZone4RampSet"] = "TempZone4RampSet";
            mapping["TempZone5RampSet"] = "TempZone5RampSet";
            mapping["TempZone6RampSet"] = "TempZone6RampSet";
            mapping["TempZone7RampSet"] = "TempZone7RampSet";
            mapping["TempZone8RampSet"] = "TempZone8RampSet";
            mapping["TempControlMode"] = "TempControlMode";
            mapping["LoopCount"] = "LoopCount";
            mapping["APC2ControlMode"] = "APC2ControlMode";
            mapping["APC2Set"] = "APC2Set";
            mapping["APC2RampSet"] = "APC2RampSet";
            mapping["APC3ControlMode"] = "APC3ControlMode";
            mapping["APC3Set"] = "APC3Set";
            mapping["APC3RampSet"] = "APC3RampSet";
            mapping["SubRecipeName"] = "SubRecipeName";
            mapping["CallCount"] = "CallCount";

            // 创建默认的显示配置（基于配置文件的结构）
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "StepNo", Header = "StepNo" });
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "StepName", Header = "StepName" });
            
            // 复合字段：Condition
            var conditionColumn = new RecipeColumnConfig 
            { 
                Field = "Condition", 
                Header = "Condition", 
                Type = "composite",
                Sources = new List<string> { "StepUpCondition", "StepUpConditionTargetValue" },
                Format = "{0}({1})"
            };
            config.DisplayConfig.Columns.Add(conditionColumn);
            
            // 简单字段：Command
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "Command", Header = "Command", Source = "CommandCode" });
            
            // 条件字段：ElevatorCommand
            var elevatorColumn = new RecipeColumnConfig 
            { 
                Field = "ElevatorCommand", 
                Header = "ElevatorCommand", 
                Type = "conditional",
                Conditions = new List<RecipeColumnCondition> 
                { 
                    new RecipeColumnCondition { Check = "equals", Value = "0", Result = "None" }
                },
                Default = new RecipeColumnDefault 
                { 
                    Format = "{0}({1})", 
                    Sources = new List<string> { "ElevatorCommand", "ElevatorSpeed" } 
                }
            };
            config.DisplayConfig.Columns.Add(elevatorColumn);
            
            // 条件字段：BoatRotateCommand
            var boatColumn = new RecipeColumnConfig 
            { 
                Field = "BoatRotateCommand", 
                Header = "BoatRotateCommand", 
                Type = "conditional",
                Conditions = new List<RecipeColumnCondition> 
                { 
                    new RecipeColumnCondition { Check = "equals", Value = "0", Result = "None" }
                },
                Default = new RecipeColumnDefault 
                { 
                    Format = "{0}({1})", 
                    Sources = new List<string> { "BoatRotateCommand", "BoatRotateSpeed" } 
                }
            };
            config.DisplayConfig.Columns.Add(boatColumn);
            
            // 简单字段：温度相关
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "Temp Set(°C)", Header = "Temp Set(°C)", Source = "TempZone1Set" });
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "Temp Rate(°C/min)", Header = "Temp Rate(°C/min)", Source = "TempZone1RampSet" });
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "Temp Mode", Header = "Temp Mode", Source = "TempControlMode" });
            config.DisplayConfig.Columns.Add(new RecipeColumnConfig { Field = "Loop", Header = "Loop", Source = "LoopCount" });
            
            // 选择器字段：APC2
            var apc2Column = new RecipeColumnConfig 
            { 
                Field = "APC2 Setting(mTorr)", 
                Header = "APC2 Setting(mTorr)", 
                Type = "selector",
                Source = "APC2ControlMode",
                Selectors = new List<RecipeColumnSelector> 
                { 
                    new RecipeColumnSelector 
                    { 
                        CheckField = "APC2Set", 
                        Check = "not_equals", 
                        Value = "0", 
                        Format = "{0}({1})", 
                        Sources = new List<string> { "APC2ControlMode", "APC2Set" } 
                    },
                    new RecipeColumnSelector 
                    { 
                        CheckField = "APC2RampSet", 
                        Check = "not_equals", 
                        Value = "0", 
                        Format = "{0}({1})", 
                        Sources = new List<string> { "APC2ControlMode", "APC2RampSet" } 
                    }
                },
                Fallback = "{0}",
                FallbackSources = new List<string> { "APC2ControlMode" }
            };
            config.DisplayConfig.Columns.Add(apc2Column);
            
            // 选择器字段：APC3
            var apc3Column = new RecipeColumnConfig 
            { 
                Field = "APC3 Setting(mTorr)", 
                Header = "APC3 Setting(mTorr)", 
                Type = "selector",
                Source = "APC3ControlMode",
                Selectors = new List<RecipeColumnSelector> 
                { 
                    new RecipeColumnSelector 
                    { 
                        CheckField = "APC3Set", 
                        Check = "not_equals", 
                        Value = "0", 
                        Format = "{0}({1})", 
                        Sources = new List<string> { "APC3ControlMode", "APC3Set" } 
                    },
                    new RecipeColumnSelector 
                    { 
                        CheckField = "APC3RampSet", 
                        Check = "not_equals", 
                        Value = "0", 
                        Format = "{0}({1})", 
                        Sources = new List<string> { "APC3ControlMode", "APC3RampSet" } 
                    }
                },
                Fallback = "{0}",
                FallbackSources = new List<string> { "APC3ControlMode" }
            };
            config.DisplayConfig.Columns.Add(apc3Column);
            
            // 复合字段：SubRecipeName
            var subRecipeColumn = new RecipeColumnConfig 
            { 
                Field = "SubRecipeName", 
                Header = "SubRecipeName", 
                Type = "composite",
                Sources = new List<string> { "SubRecipeName", "CallCount" },
                Format = "{0}({1})"
            };
            config.DisplayConfig.Columns.Add(subRecipeColumn);

            return config;
        }

        /// <summary>
        /// 获取字段映射
        /// </summary>
        /// <param name="csvFieldName">CSV字段名</param>
        /// <returns>映射后的字段名</returns>
        public string GetMappedFieldName(string csvFieldName)
        {
            if (CurrentConfig.CsvParserConfig.ColumnMapping.TryGetValue(csvFieldName, out var mappedName))
            {
                return mappedName;
            }
            return csvFieldName; // 如果没有映射，返回原字段名
        }


    }
}
