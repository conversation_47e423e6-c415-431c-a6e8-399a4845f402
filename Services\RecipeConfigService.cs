using System;
using System.IO;
using DatalogDrawing.Models;
using Newtonsoft.Json;

namespace DatalogDrawing.Services
{
    /// <summary>
    /// Recipe配置服务
    /// </summary>
    public class RecipeConfigService
    {
        private const string DEFAULT_CONFIG_FILE = "RecipeConfig.json";
        private RecipeConfig _currentConfig;

        /// <summary>
        /// 当前配置
        /// </summary>
        public RecipeConfig CurrentConfig
        {
            get
            {
                if (_currentConfig == null)
                {
                    LoadDefaultConfig();
                }
                return _currentConfig;
            }
            private set => _currentConfig = value;
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        public void LoadDefaultConfig()
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug", DEFAULT_CONFIG_FILE);
            LoadConfig(configPath);
        }

        /// <summary>
        /// 加载指定配置文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        public void LoadConfig(string configPath)
        {
            try
            {
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    CurrentConfig = JsonConvert.DeserializeObject<RecipeConfig>(json);
                    System.Diagnostics.Debug.WriteLine($"Recipe配置加载成功: {configPath}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Recipe配置文件不存在，使用默认配置: {configPath}");
                    CurrentConfig = CreateDefaultConfig();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载Recipe配置失败: {ex.Message}");
                CurrentConfig = CreateDefaultConfig();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        public void SaveConfig(string configPath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(CurrentConfig, Formatting.Indented);
                File.WriteAllText(configPath, json);
                System.Diagnostics.Debug.WriteLine($"Recipe配置保存成功: {configPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存Recipe配置失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private RecipeConfig CreateDefaultConfig()
        {
            var config = new RecipeConfig();

            // CSV解析配置
            config.CsvParserConfig.Delimiter = ",";
            config.CsvParserConfig.Encoding = "UTF-8";
            config.CsvParserConfig.HasHeader = true;
            config.CsvParserConfig.SkipEmptyLines = true;

            // 基本字段映射
            var mapping = config.CsvParserConfig.ColumnMapping;
            mapping["StepNo"] = "StepNo";
            mapping["StepName"] = "StepName";
            mapping["StepTime"] = "StepTime";
            mapping["CommandCode"] = "CommandCode";
            mapping["TempZone1Set"] = "TempZone1Set";
            mapping["APC2ControlMode"] = "APC2ControlMode";
            mapping["APC3ControlMode"] = "APC3ControlMode";

            return config;
        }

        /// <summary>
        /// 获取字段映射
        /// </summary>
        /// <param name="csvFieldName">CSV字段名</param>
        /// <returns>映射后的字段名</returns>
        public string GetMappedFieldName(string csvFieldName)
        {
            if (CurrentConfig.CsvParserConfig.ColumnMapping.TryGetValue(csvFieldName, out var mappedName))
            {
                return mappedName;
            }
            return csvFieldName; // 如果没有映射，返回原字段名
        }


    }
}
