#!/usr/bin/env python3
"""
膜厚热力图Python实现
实现与MATLAB mesh函数类似的效果，包括三次样条插值和等高线叠加
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata
from scipy.spatial.distance import cdist
import json
import pandas as pd
from typing import List, Dict, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

class FilmThicknessHeatmap:
    """膜厚热力图生成器，模拟MATLAB mesh函数效果"""
    
    def __init__(self, grid_size: int = 150, contour_levels: int = 12):
        """
        初始化热力图生成器
        
        Args:
            grid_size: 插值网格密度，默认150
            contour_levels: 等高线数量，默认12
        """
        self.grid_size = grid_size
        self.contour_levels = contour_levels
        
    def create_heatmap(self, 
                      points: List[Dict[str, float]], 
                      title: str = "Film Thickness Heatmap",
                      save_path: Optional[str] = None,
                      show_plot: bool = True,
                      figsize: Tuple[int, int] = (10, 8),
                      dpi: int = 100,
                      use_median_filter: bool = True) -> plt.Figure:
        """
        创建膜厚热力图
        
        Args:
            points: 数据点列表，每个点包含 {'X': x, 'Y': y, 'Value': value}
            title: 图表标题
            save_path: 保存路径，如果为None则不保存
            show_plot: 是否显示图表
            figsize: 图像大小
            dpi: 图像分辨率
            
        Returns:
            matplotlib Figure对象
        """
        if len(points) < 4:
            raise ValueError("至少需要4个数据点才能进行插值")
            
        # 提取坐标和数值
        x_data = np.array([p['X'] for p in points])
        y_data = np.array([p['Y'] for p in points])
        z_data = np.array([p['Value'] for p in points])
        
        # 计算数据边界和圆形区域
        center_x, center_y = np.mean(x_data), np.mean(y_data)
        radius = np.max(np.sqrt((x_data - center_x)**2 + (y_data - center_y)**2)) * 1.05
        
        # 创建规则网格
        x_min, x_max = center_x - radius, center_x + radius
        y_min, y_max = center_y - radius, center_y + radius
        
        xi = np.linspace(x_min, x_max, self.grid_size)
        yi = np.linspace(y_min, y_max, self.grid_size)
        xi_grid, yi_grid = np.meshgrid(xi, yi)
        
        # 修正的优化流程：只内插，不外插
        if use_median_filter:
            # 1. 先进行基础插值（线性插值）- 只内插
            zi_linear = griddata((x_data, y_data), z_data, (xi_grid, yi_grid), method='linear', fill_value=np.nan)
            
            # 2. 使用移动中位数滤波填充z值 - 只处理有效区域
            from scipy.ndimage import median_filter
            # 只对有效数据进行滤波，不填充NaN
            valid_mask = ~np.isnan(zi_linear)
            zi_filtered = zi_linear.copy()
            
            if np.any(valid_mask):
                # 只对有效区域进行中位数滤波
                zi_median = median_filter(zi_linear, size=3)
                zi_filtered[valid_mask] = zi_median[valid_mask]
            
            # 3. 然后三次样条插值：只内插，不外插
            valid_points = ~np.isnan(zi_filtered)
            if np.any(valid_points):
                zi_grid = griddata((xi_grid[valid_points], yi_grid[valid_points]), 
                                  zi_filtered[valid_points], (xi_grid, yi_grid), method='cubic', fill_value=np.nan)
            else:
                zi_grid = zi_filtered
        else:
            # 原始方法：直接三次样条插值 - 只内插
            zi_grid = griddata((x_data, y_data), z_data, (xi_grid, yi_grid), method='cubic', fill_value=np.nan)
        
        # 应用圆形遮罩 - 确保边界外完全透明
        mask = (xi_grid - center_x)**2 + (yi_grid - center_y)**2 > radius**2
        zi_grid[mask] = np.nan
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize, dpi=dpi)
        
        # 绘制热力图（类似MATLAB mesh的俯视图效果）
        im = ax.imshow(zi_grid, extent=[x_min, x_max, y_min, y_max], 
                      origin='lower', cmap='turbo', aspect='equal',
                      interpolation='bilinear', alpha=1.0)
        
        # 添加等高线（类似MATLAB的contour效果）
        contour_levels_array = np.linspace(np.nanmin(zi_grid), np.nanmax(zi_grid), self.contour_levels)
        contours = ax.contour(xi_grid, yi_grid, zi_grid, levels=contour_levels_array, 
                            colors='black', linewidths=2.0, alpha=0.7)
        
        # 添加原始数据点
        scatter = ax.scatter(x_data, y_data, c=z_data, cmap='turbo', 
                           s=60, edgecolors='black', linewidths=1.2, zorder=5)
        
        # 设置坐标轴（模拟MATLAB的xy视角）
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.set_title(f'{title}\nThickness Distribution (nm)')
        # 删除网格线
        # ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02)
        cbar.set_label('Thickness (nm)', rotation=270, labelpad=20)
        
        # 设置图表样式
        plt.tight_layout()
        
        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=dpi, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"热力图已保存到: {save_path}")
        
        # 显示图像
        if show_plot:
            plt.show()
            
        return fig
    
    def create_3d_surface(self, 
                         points: List[Dict[str, float]], 
                         title: str = "Film Thickness 3D Surface",
                         save_path: Optional[str] = None,
                         show_plot: bool = True,
                         figsize: Tuple[int, int] = (12, 9),
                         dpi: int = 100,
                         use_median_filter: bool = True) -> plt.Figure:
        """
        创建3D表面图（类似MATLAB mesh函数的原始效果）
        
        Args:
            points: 数据点列表
            title: 图表标题
            save_path: 保存路径
            show_plot: 是否显示图表
            figsize: 图像大小
            dpi: 图像分辨率
            
        Returns:
            matplotlib Figure对象
        """
        if len(points) < 4:
            raise ValueError("至少需要4个数据点才能进行插值")
            
        # 提取坐标和数值
        x_data = np.array([p['X'] for p in points])
        y_data = np.array([p['Y'] for p in points])
        z_data = np.array([p['Value'] for p in points])
        
        # 计算数据边界
        center_x, center_y = np.mean(x_data), np.mean(y_data)
        radius = np.max(np.sqrt((x_data - center_x)**2 + (y_data - center_y)**2)) * 1.05
        
        # 创建规则网格
        x_min, x_max = center_x - radius, center_x + radius
        y_min, y_max = center_y - radius, center_y + radius
        
        xi = np.linspace(x_min, x_max, self.grid_size)
        yi = np.linspace(y_min, y_max, self.grid_size)
        xi_grid, yi_grid = np.meshgrid(xi, yi)
        
        # 修正的优化流程：只内插，不外插
        if use_median_filter:
            # 1. 先进行基础插值（线性插值）- 只内插
            zi_linear = griddata((x_data, y_data), z_data, (xi_grid, yi_grid), method='linear', fill_value=np.nan)
            
            # 2. 使用移动中位数滤波填充z值 - 只处理有效区域
            from scipy.ndimage import median_filter
            # 只对有效数据进行滤波，不填充NaN
            valid_mask = ~np.isnan(zi_linear)
            zi_filtered = zi_linear.copy()
            
            if np.any(valid_mask):
                # 只对有效区域进行中位数滤波
                zi_median = median_filter(zi_linear, size=3)
                zi_filtered[valid_mask] = zi_median[valid_mask]
            
            # 3. 然后三次样条插值：只内插，不外插
            valid_points = ~np.isnan(zi_filtered)
            if np.any(valid_points):
                zi_grid = griddata((xi_grid[valid_points], yi_grid[valid_points]), 
                                  zi_filtered[valid_points], (xi_grid, yi_grid), method='cubic', fill_value=np.nan)
            else:
                zi_grid = zi_filtered
        else:
            # 原始方法：直接三次样条插值 - 只内插
            zi_grid = griddata((x_data, y_data), z_data, (xi_grid, yi_grid), method='cubic', fill_value=np.nan)
        
        # 应用圆形遮罩 - 确保边界外完全透明
        mask = (xi_grid - center_x)**2 + (yi_grid - center_y)**2 > radius**2
        zi_grid[mask] = np.nan
        
        # 创建3D图形
        fig = plt.figure(figsize=figsize, dpi=dpi)
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制3D表面
        surf = ax.plot_surface(xi_grid, yi_grid, zi_grid, cmap='turbo', 
                              alpha=1.0, linewidth=0, antialiased=True)
        
        # 添加等高线投影到底部
        contour_levels_array = np.linspace(np.nanmin(zi_grid), np.nanmax(zi_grid), self.contour_levels)
        ax.contour(xi_grid, yi_grid, zi_grid, levels=contour_levels_array, 
                  zdir='z', offset=np.nanmin(zi_grid), colors='black', alpha=0.5)
        
        # 添加原始数据点
        ax.scatter(x_data, y_data, z_data, c=z_data, cmap='turbo', 
                  s=30, edgecolors='black', linewidths=0.5, zorder=5)
        
        # 设置标签和标题
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.set_zlabel('Thickness (nm)')
        ax.set_title(f'{title}\n3D Surface View')
        
        # 添加颜色条
        cbar = plt.colorbar(surf, ax=ax, shrink=0.6, pad=0.1)
        cbar.set_label('Thickness (nm)', rotation=270, labelpad=20)
        
        # 设置视角（可以调整为俯视图）
        ax.view_init(elev=30, azim=45)  # 改为 elev=90, azim=0 可得到俯视图
        
        plt.tight_layout()
        
        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=dpi, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"3D表面图已保存到: {save_path}")
        
        # 显示图像
        if show_plot:
            plt.show()
            
        return fig

def load_data_from_json(json_path: str) -> List[Dict[str, float]]:
    """从JSON文件加载数据点"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def load_data_from_csv(csv_path: str, x_col: str = 'X', y_col: str = 'Y', value_col: str = 'Value') -> List[Dict[str, float]]:
    """从CSV文件加载数据点"""
    df = pd.read_csv(csv_path)
    points = []
    for _, row in df.iterrows():
        points.append({
            'X': float(row[x_col]),
            'Y': float(row[y_col]),
            'Value': float(row[value_col])
        })
    return points

def create_sample_data() -> List[Dict[str, float]]:
    """创建示例数据（模拟圆形晶圆上的测量点）"""
    np.random.seed(42)
    
    # 生成圆形分布的测量点
    n_points = 49  # 类似49点测量
    angles = np.linspace(0, 2*np.pi, n_points//7, endpoint=False)
    radii = [0, 0.3, 0.6, 0.8, 1.0, 1.2, 1.4]  # 不同半径的圆
    
    points = []
    base_thickness = 1000  # 基础厚度1000nm
    
    for r in radii:
        if r == 0:  # 中心点
            points.append({
                'X': 0.0,
                'Y': 0.0,
                'Value': base_thickness + np.random.normal(0, 10)
            })
        else:
            for angle in angles:
                x = r * np.cos(angle)
                y = r * np.sin(angle)
                # 添加一些径向和角度变化
                thickness_variation = 50 * np.sin(2*angle) + 30 * (1 - r/1.5)
                thickness = base_thickness + thickness_variation + np.random.normal(0, 5)
                points.append({
                    'X': x,
                    'Y': y,
                    'Value': thickness
                })
    
    return points

if __name__ == "__main__":
    # 示例用法
    print("膜厚热力图Python实现 - MATLAB风格")
    print("=" * 50)
    
    # 创建热力图生成器
    heatmap_generator = FilmThicknessHeatmap(grid_size=150, contour_levels=15)
    
    # 使用示例数据
    sample_points = create_sample_data()
    print(f"生成了 {len(sample_points)} 个示例数据点")
    
    # 创建2D热力图（类似MATLAB mesh的俯视图）
    print("\n创建2D热力图...")
    fig_2d = heatmap_generator.create_heatmap(
        points=sample_points,
        title="Sample Wafer Thickness Map",
        save_path="thickness_heatmap_2d.png",
        show_plot=True
    )
    
    # 创建3D表面图（类似MATLAB mesh的原始效果）
    print("\n创建3D表面图...")
    fig_3d = heatmap_generator.create_3d_surface(
        points=sample_points,
        title="Sample Wafer Thickness 3D",
        save_path="thickness_heatmap_3d.png",
        show_plot=True
    )
    
    print("\n完成！图像已保存。")
