using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;  // 添加ICommand引用
using DatalogDrawing.Models;
using DatalogDrawing.Interfaces;
using DatalogDrawing.Services;
using DatalogDrawing.Helpers;

namespace DatalogDrawing.ViewModels
{
    public class MainViewModel : ViewModelBase
    {
        private readonly IDataParser<FilmThicknessData> _parser;

        // App-level commands
        public ICommand ExitCommand { get; }
        public ICommand AboutCommand { get; }

        // Child ViewModels
        public FilmThicknessViewModel FilmThicknessVM { get; }
        public PADataViewModel PADataVM { get; }
        public SummaryViewModel SummaryVM { get; }
        public RecipeViewModel RecipeVM { get; }

        private ViewModelBase _activeViewModel;
        public ViewModelBase ActiveViewModel
        {
            get => _activeViewModel;
            set
            {
                _activeViewModel = value;
                OnPropertyChanged(nameof(ActiveViewModel));
            }
        }

        private object _currentView;
        public object CurrentView
        {
            get => _currentView;
            private set => SetProperty(ref _currentView, value);
        }

        // Status properties
        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        public ICommand ShowFilmThicknessView { get; }
        public ICommand ShowPADataView { get; }
        public ICommand ShowRecipeView { get; }

        public MainViewModel(IDataParser<FilmThicknessData> parser)
        {
            _parser = parser;

            // Initialize child ViewModels
            var configService = new ConfigService();
            FilmThicknessVM = new FilmThicknessViewModel(configService, parser, SetBusy, SetStatusMessage);
            PADataVM = new PADataViewModel();
            SummaryVM = new SummaryViewModel();
            RecipeVM = new RecipeViewModel();

            // Initialize commands
            ShowFilmThicknessView = new RelayCommand(ShowFilmThickness);
            ShowPADataView = new RelayCommand(ShowPAData);
            ShowRecipeView = new RelayCommand(ShowRecipe);
            ExitCommand = new RelayCommand(Exit);
            AboutCommand = new RelayCommand(ShowAbout);

            // Set default active view
            ActiveViewModel = FilmThicknessVM;
        }

        private void ShowFilmThickness()
        {
            ActiveViewModel = FilmThicknessVM;
            StatusMessage = "膜厚分析模块";
        }



        private void ShowPAData()
        {
            ActiveViewModel = PADataVM;
            StatusMessage = "颗粒分析模块 (PA数据)";
        }

        private void ShowRecipe()
        {
            ActiveViewModel = RecipeVM;
            StatusMessage = "Recipe分析模块";
        }

        private void Exit()
        {
            System.Windows.Application.Current.Shutdown();
        }

        private void ShowAbout()
        {
            System.Windows.MessageBox.Show("数据分析平台 v1.0\n\n支持膜厚、颗粒、PA数据分析", "关于",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        private void SetBusy(bool isBusy)
        {
            IsBusy = isBusy;
            if (isBusy)
                StatusMessage = "处理中...";
        }

        private void SetStatusMessage(string message)
        {
            StatusMessage = message;
        }
    }
}
