using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using DatalogDrawing.ViewModels;
using DatalogDrawing.Models;
using DatalogDrawing.Services;
using System.Linq;
using System;

namespace DatalogDrawing.Views
{
    public partial class SummaryView : UserControl
    {
        public SummaryView()
        {
            InitializeComponent();

            // 监听颗粒数据会话选择变化
            PADataSessionsGrid.SelectionChanged += PADataSessionsGrid_SelectionChanged;

            // 监听数据加载完成事件，绘制表格中的颗粒图
            PADataSessionsGrid.LoadingRow += PADataSessionsGrid_LoadingRow;

            // 监听表格加载完成事件
            PADataSessionsGrid.Loaded += PADataSessionsGrid_Loaded;

            // 监听DataContext变化，订阅ViewModel事件
            this.DataContextChanged += SummaryView_DataContextChanged;
        }

        private void SummaryView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 取消订阅旧的ViewModel事件
            if (e.OldValue is SummaryViewModel oldViewModel)
            {
                oldViewModel.ParticleChartsRefreshRequested -= OnParticleChartsRefreshRequested;
            }

            // 订阅新的ViewModel事件
            if (e.NewValue is SummaryViewModel newViewModel)
            {
                newViewModel.ParticleChartsRefreshRequested += OnParticleChartsRefreshRequested;
            }
        }

        private void OnParticleChartsRefreshRequested(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("OnParticleChartsRefreshRequested: 收到刷新请求");
            RefreshParticleCharts();
        }

        private void PADataSessionsGrid_Loaded(object sender, RoutedEventArgs e)
        {
            // 当表格加载完成时，延迟重新绘制所有行
            Dispatcher.BeginInvoke(new Action(() =>
            {
                System.Diagnostics.Debug.WriteLine("PADataSessionsGrid_Loaded: 开始重新绘制所有行");
                RedrawAllParticleCharts();
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        // 公共方法，供外部调用来重新绘制颗粒图
        public void RefreshParticleCharts()
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                System.Diagnostics.Debug.WriteLine("RefreshParticleCharts: 手动触发重新绘制");
                RedrawAllParticleCharts();
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        private void RedrawAllParticleCharts()
        {
            try
            {
                if (PADataSessionsGrid.ItemsSource != null)
                {
                    for (int i = 0; i < PADataSessionsGrid.Items.Count; i++)
                    {
                        var container = PADataSessionsGrid.ItemContainerGenerator.ContainerFromIndex(i) as DataGridRow;
                        if (container != null && container.Item is Dictionary<string, object> session)
                        {
                            System.Diagnostics.Debug.WriteLine($"RedrawAllParticleCharts: 重新绘制行 {i}");
                            DrawParticleChartsInRow(container, session);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"RedrawAllParticleCharts失败: {ex.Message}");
            }
        }

        private void PADataSessionsGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 选择变化时不再更新下方预览区域，因为已经删除了
            // 颗粒图直接在表格行中显示
        }

        private void PADataSessionsGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            // 当行加载时，绘制该行的颗粒图
            if (e.Row.Item is Dictionary<string, object> session)
            {
                System.Diagnostics.Debug.WriteLine($"LoadingRow: 开始处理行，会话ID={(session.ContainsKey("Id") ? session["Id"] : "未知")}");

                // 延迟执行以确保UI元素已经加载
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    System.Diagnostics.Debug.WriteLine("LoadingRow: 延迟执行开始");
                    DrawParticleChartsInRow(e.Row, session);
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
        }

        private void DrawParticleChartsInRow(DataGridRow row, Dictionary<string, object> session)
        {
            try
            {
                if (DataContext is SummaryViewModel viewModel)
                {
                    // 调试：打印会话数据结构
                    System.Diagnostics.Debug.WriteLine($"DrawParticleChartsInRow: 会话ID={(session.ContainsKey("Id") ? session["Id"] : "未知")}");
                    System.Diagnostics.Debug.WriteLine($"Session keys: {string.Join(", ", session.Keys)}");

                    // 处理FullResultData的类型转换问题
                    Dictionary<string, object> fullResultData = null;
                    var fullResultDataObj = session["FullResultData"];

                    System.Diagnostics.Debug.WriteLine($"FullResultData原始类型: {fullResultDataObj?.GetType().Name}");

                    if (fullResultDataObj is Dictionary<string, object> dict)
                    {
                        fullResultData = dict;
                        System.Diagnostics.Debug.WriteLine("FullResultData是Dictionary类型");
                    }
                    else if (fullResultDataObj is Newtonsoft.Json.Linq.JObject jObject)
                    {
                        // 将JObject转换为Dictionary
                        fullResultData = jObject.ToObject<Dictionary<string, object>>();
                        System.Diagnostics.Debug.WriteLine("FullResultData从JObject转换为Dictionary");
                    }
                    else if (fullResultDataObj != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"FullResultData是未知类型: {fullResultDataObj.GetType().Name}");
                        try
                        {
                            // 尝试通过JSON序列化/反序列化转换
                            var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(fullResultDataObj);
                            fullResultData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
                            System.Diagnostics.Debug.WriteLine("通过JSON转换成功");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"JSON转换失败: {ex.Message}");
                        }
                    }

                    if (fullResultData != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"FullResultData keys: {string.Join(", ", fullResultData.Keys)}");
                        foreach (var key in fullResultData.Keys)
                        {
                            System.Diagnostics.Debug.WriteLine($"  {key}: {fullResultData[key]?.GetType().Name}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("FullResultData转换失败，跳过绘制");
                        return;
                    }

                    // 检查是否有颗粒数据
                    if (!fullResultData.ContainsKey("PreData") && !fullResultData.ContainsKey("AfterData") && !fullResultData.ContainsKey("DeltaData"))
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过无颗粒数据的会话，会话ID={(session.ContainsKey("Id") ? session["Id"] : "未知")}");
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"开始绘制颗粒数据，会话ID={(session.ContainsKey("Id") ? session["Id"] : "未知")}");

                    // 查找行中的Canvas元素（使用Tag属性）
                    var preCanvas = FindCanvasByTag(row, "PreCanvas");
                    var afterCanvas = FindCanvasByTag(row, "AfterCanvas");
                    var addCanvas = FindCanvasByTag(row, "AddCanvas");

                    System.Diagnostics.Debug.WriteLine($"Canvas查找结果: Pre={preCanvas != null}, After={afterCanvas != null}, Add={addCanvas != null}");

                    // 从FullResultData中直接读取JSON格式的颗粒数据
                    List<PAData> preData = new List<PAData>();
                    List<PAData> afterData = new List<PAData>();
                    List<PAData> addData = new List<PAData>();

                    try
                        {
                            // 从FullResultData中获取颗粒数据
                            preData = ExtractPADataFromObject(fullResultData, "PreData");
                            afterData = ExtractPADataFromObject(fullResultData, "AfterData");
                            // 优先尝试新的AddData键名，如果不存在则尝试旧的DeltaData键名（向后兼容）
                            if (fullResultData.ContainsKey("AddData"))
                            {
                                addData = ExtractPADataFromObject(fullResultData, "AddData");
                            }
                            else if (fullResultData.ContainsKey("DeltaData"))
                            {
                                addData = ExtractPADataFromObject(fullResultData, "DeltaData");
                            }

                            System.Diagnostics.Debug.WriteLine($"提取颗粒数据: Pre={preData.Count}, After={afterData.Count}, Add={addData.Count}");
                            if (preData.Count > 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"第一个Pre点: X={preData[0].XREL}, Y={preData[0].YREL}, Size={preData[0].DSIZE}");
                            }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"从JSON加载颗粒数据失败: {ex.Message}");
                    }

                    // 绘制圆形颗粒图（直接调用共享服务）
                    if (preCanvas != null && afterCanvas != null && addCanvas != null)
                    {
                        // 直接调用共享的绘制服务
                        ParticleDrawingService.DrawParticlesOnCanvas(preCanvas, preData);
                        ParticleDrawingService.DrawParticlesOnCanvas(afterCanvas, afterData);
                        ParticleDrawingService.DrawParticlesOnCanvas(addCanvas, addData);

                        System.Diagnostics.Debug.WriteLine($"成功绘制圆形颗粒图: Pre={preData.Count}, After={afterData.Count}, Add={addData.Count}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("未找到Canvas元素");

                        // 尝试使用更通用的方法查找Canvas
                        var allCanvases = FindAllVisualChildren<Canvas>(row);
                        System.Diagnostics.Debug.WriteLine($"找到的所有Canvas数量: {allCanvases.Count()}");

                        var canvasList = allCanvases.ToList();
                        if (canvasList.Count >= 3)
                        {
                            System.Diagnostics.Debug.WriteLine("尝试使用找到的Canvas直接绘制");
                            ParticleDrawingService.DrawParticlesOnCanvas(canvasList[0], preData);
                            ParticleDrawingService.DrawParticlesOnCanvas(canvasList[1], afterData);
                            ParticleDrawingService.DrawParticlesOnCanvas(canvasList[2], addData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"绘制行颗粒图失败: {ex.Message}");
            }
        }

        // 辅助方法：查找可视化树中的子元素
        private T FindVisualChild<T>(DependencyObject parent, string name) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T && ((FrameworkElement)child).Name == name)
                {
                    return (T)child;
                }
                var childOfChild = FindVisualChild<T>(child, name);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        // 辅助方法：根据Tag属性查找Canvas
        private static Canvas FindCanvasByTag(DependencyObject parent, string tag)
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is Canvas canvas && canvas.Tag?.ToString() == tag)
                    return canvas;

                var childCanvas = FindCanvasByTag(child, tag);
                if (childCanvas != null)
                    return childCanvas;
            }
            return null;
        }



        // 辅助方法：查找所有指定类型的子元素
        private static IEnumerable<T> FindAllVisualChildren<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) yield break;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T)
                    yield return (T)child;

                foreach (var childOfChild in FindAllVisualChildren<T>(child))
                    yield return childOfChild;
            }
        }

        // 辅助方法：从复杂对象中提取PAData列表
        private List<PAData> ExtractPADataFromObject(Dictionary<string, object> data, string key)
        {
            if (!data.ContainsKey(key))
            {
                System.Diagnostics.Debug.WriteLine($"未找到键: {key}");
                return new List<PAData>();
            }

            var dataObj = data[key];
            System.Diagnostics.Debug.WriteLine($"{key}类型: {dataObj?.GetType().Name}");

            try
            {
                if (dataObj is Newtonsoft.Json.Linq.JArray jArray)
                {
                    var result = jArray.ToObject<List<PAData>>();
                    System.Diagnostics.Debug.WriteLine($"从JArray提取{key}: {result?.Count ?? 0}个");
                    return result ?? new List<PAData>();
                }
                else if (dataObj is List<PAData> paDataList)
                {
                    System.Diagnostics.Debug.WriteLine($"直接使用{key}: {paDataList.Count}个");
                    return paDataList;
                }
                else if (dataObj is System.Collections.IEnumerable enumerable && !(dataObj is string))
                {
                    // 尝试转换为PAData列表
                    var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(dataObj);
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<List<PAData>>(jsonString);
                    System.Diagnostics.Debug.WriteLine($"通过JSON转换{key}: {result?.Count ?? 0}个");
                    return result ?? new List<PAData>();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"{key}不是可识别的数据类型: {dataObj?.GetType().Name}");
                    return new List<PAData>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"提取{key}失败: {ex.Message}");
                return new List<PAData>();
            }
        }













        // 膜厚数据按钮事件
        private void DeleteFilmThicknessButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Dictionary<string, object> session)
            {
                if (DataContext is SummaryViewModel viewModel)
                {
                    viewModel.DeleteSessionCommand.Execute(session);
                }
            }
        }

        private void PreviewFilmThicknessButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Dictionary<string, object> session)
            {
                if (DataContext is SummaryViewModel viewModel)
                {
                    viewModel.PreviewSessionCommand.Execute(session);
                }
            }
        }

        // 颗粒数据按钮事件
        private void DeletePADataButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Dictionary<string, object> session)
            {
                if (DataContext is SummaryViewModel viewModel)
                {
                    // 调用删除颗粒数据会话方法
                    viewModel.DeletePADataSession(session);
                }
            }
        }

        private void PreviewPADataButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Dictionary<string, object> session)
            {
                if (DataContext is SummaryViewModel viewModel)
                {
                    // 直接调用SummaryViewModel的预览方法，复用PADataView
                    viewModel.PreviewPADataSession(session);
                }
            }
        }



        // 设置对比模式，禁用操作按钮（类似预览模式）
        public void SetComparisonMode(bool isComparisonMode)
        {
            if (isComparisonMode)
            {
                // 延迟执行以确保控件已加载
                this.Loaded += (s, e) => DisableOperationButtons();

                // 如果已经加载，立即执行
                if (this.IsLoaded)
                {
                    DisableOperationButtons();
                }
            }
        }

        private void DisableOperationButtons()
        {
            // 查找DataGrid中的所有按钮并禁用
            DisableButtonsInDataGrid(PADataSessionsGrid);
        }

        private void DisableButtonsInDataGrid(DataGrid dataGrid)
        {
            for (int i = 0; i < dataGrid.Items.Count; i++)
            {
                var container = dataGrid.ItemContainerGenerator.ContainerFromIndex(i) as DataGridRow;
                if (container != null)
                {
                    DisableButtonsInContainer(container);
                }
            }
        }

        private void DisableButtonsInContainer(DependencyObject container)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(container); i++)
            {
                var child = VisualTreeHelper.GetChild(container, i);

                if (child is Button button)
                {
                    // 检查是否是操作按钮
                    var content = button.Content?.ToString();
                    if (content != null && (content.Contains("删除") || content.Contains("预览")))
                    {
                        button.IsEnabled = false;
                        button.ToolTip = "对比模式下不可操作";
                    }
                }
                else
                {
                    DisableButtonsInContainer(child);
                }
            }
        }
    }
}