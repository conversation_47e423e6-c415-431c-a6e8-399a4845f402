using System.Collections.Generic;
using DatalogDrawing.Models;

namespace DatalogDrawing.Interfaces
{
    public interface IDataParser<out T>
    {
        Dictionary<string, object> Parse(string filePath, AppConfig config);
        
        // 添加泛型Parse方法
        T ParseData(string filePath, AppConfig config);
        
        // 添加异步解析方法
        System.Threading.Tasks.Task<Dictionary<string, object>> ParseAsync(string filePath, AppConfig config);
    }
}