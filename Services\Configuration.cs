using System.Collections.Generic;
using Newtonsoft.Json;

namespace DatalogDrawing
{
    public class CsvParserConfig
    {
        [JsonProperty("delimiter")]
        public string Delimiter { get; set; } = ",";

        [JsonProperty("encoding")]
        public string Encoding { get; set; } = "UTF-8";

        [JsonProperty("row_filters")]
        public List<RowFilter> RowFilters { get; set; } = new List<RowFilter>();
    }

    public class RowFilter
    {
        [JsonProperty("name")]
        public string Name { get; set; } = "默认过滤器";

        [JsonProperty("enabled")]
        public bool Enabled { get; set; } = true;

        [JsonProperty("filter_type")]
        public string FilterType { get; set; } = "RowValue";

        [JsonProperty("startCondition")]
        public string StartCondition { get; set; }

        [JsonProperty("startValue")]
        public string StartValue { get; set; }

        [JsonProperty("endCondition")]
        public string EndCondition { get; set; }

        [JsonProperty("endValue")]
        public string EndValue { get; set; }

        [JsonProperty("data_type")]
        public string DataType { get; set; } = "Measurement";

        // 不需要保存的配置项
        [JsonIgnore]
        public List<string> ColumnMappings { get; set; }
    }

    public class DisplayConfig
    {
        // 不需要保存的配置项，只在内存中使用
        [JsonIgnore]
        public List<string> TableColumns { get; set; } = new List<string> { "Column1", "Column2", "Column3" };

        [JsonIgnore]
        public string ChartXAxis { get; set; } = "Column1";

        [JsonIgnore]
        public string ChartYAxis { get; set; } = "Column2";

        [JsonProperty("max_rows_display")]
        public int MaxRowsDisplay { get; set; } = 1000;

        [JsonProperty("auto_refresh")]
        public bool AutoRefresh { get; set; } = false;
        
        [JsonProperty("slot_display_names")]
        public Dictionary<string, string> SlotDisplayNames { get; set; } = new Dictionary<string, string>();
        
        [JsonProperty("user_input_value")]
        public double UserInputValue { get; set; } = 1.0;
        
        [JsonProperty("equipment_name")]
        public string EquipmentName { get; set; } = "KLASFX200(@ 49Point)";
        
        [JsonProperty("decimal_places")]
        public int DecimalPlaces { get; set; } = 2;

        [JsonProperty("particle_spacing_distance")]
        public double ParticleSpacingDistance { get; set; } = 130.0;
    }

    public class AppConfig
    {
        [JsonProperty("csv_parser_config")]
        public CsvParserConfig CsvParserConfig { get; set; } = new CsvParserConfig();

        [JsonProperty("display_config")]
        public DisplayConfig DisplayConfig { get; set; } = new DisplayConfig();
    }
}