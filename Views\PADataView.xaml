<UserControl x:Class="DatalogDrawing.Views.PADataView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:oxy="http://oxyplot.org/wpf"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000">
            <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

        <!-- 文件选择区域 -->
        <GroupBox Grid.Row="0" Header="🔬 颗粒分析 (PA数据)" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="Pre文件(.001):" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding PreFilePath}" IsReadOnly="True" Margin="5" Background="LightGray"/>
                <Button Grid.Row="0" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectPreFileCommand}" Margin="5" Padding="8,4"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="After文件(.001):" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding AfterFilePath}" IsReadOnly="True" Margin="5" Background="LightGray"/>
                <Button Grid.Row="1" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectAfterFileCommand}" Margin="5" Padding="8,4"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="配置文件:" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding ConfigFilePath}" IsReadOnly="True" Margin="5" Background="LightGray"/>
                <Button Grid.Row="2" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectConfigFileCommand}" Margin="5" Padding="8,4"/>
            </Grid>
        </GroupBox>

        <!-- 文件选择区域 -->
        <GroupBox Grid.Row="0" Header="🔬 颗粒分析 (PA数据)" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="Pre文件(.001):" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding PreFilePath}" IsReadOnly="True" Margin="5" Background="LightGray"/>
                <Button Grid.Row="0" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectPreFileCommand}" Margin="5" Padding="8,4"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="After文件(.001):" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding AfterFilePath}" IsReadOnly="True" Margin="5" Background="LightGray"/>
                <Button Grid.Row="1" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectAfterFileCommand}" Margin="5" Padding="8,4"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="配置文件:" VerticalAlignment="Center" Margin="5" FontWeight="Bold"/>
                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding ConfigFilePath}" IsReadOnly="True" Margin="5" Background="LightGray"/>
                <Button Grid.Row="2" Grid.Column="2" Content="📁 浏览..." Command="{Binding SelectConfigFileCommand}" Margin="5" Padding="8,4"/>
            </Grid>
        </GroupBox>

        <!-- 操作按钮区域 -->
        <Border Grid.Row="1" Background="LightBlue" Padding="8" Margin="5,0,5,5" x:Name="OperationButtonsBorder">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🚀 开始解析" Command="{Binding ParseCommand}" Margin="0,0,10,0" Padding="12,6"
                        Background="Orange" FontWeight="Bold" FontSize="12"/>
                <Button Content="💾 导出结果" Command="{Binding ExportResultCommand}" Margin="0,0,10,0" Padding="8,6"/>
                <Button Content="🔄 刷新配置" Click="RefreshConfig_Click" Margin="0,0,10,0" Padding="8,6"
                        Background="LightGreen" ToolTip="重新加载颜色配置文件"/>
                <Button Content="🗑️ 清除数据" Command="{Binding ClearDataCommand}" Padding="8,6"/>
            </StackPanel>
        </Border>

        <!-- 结果显示区域 -->
        <TabControl Grid.Row="2" Margin="5">
            <TabItem Header="📊 颗粒分布可视化">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2.5*"/>
                        <ColumnDefinition Width="1.5*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 左侧：三个圆形图表 -->
                    <StackPanel Grid.Column="0" Orientation="Vertical" Margin="0,0,10,0">
                        <TextBlock Text="颗粒分布图" FontSize="16" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        
                        <!-- 横向排列的三个图表 -->
                        <Grid Height="160">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Initial 图表 -->
                            <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="1" 
                                    Background="Black" CornerRadius="3">
                                <Grid>
                                    <Ellipse Fill="Black" Stroke="White" StrokeThickness="1" 
                                             Margin="3" x:Name="InitialCircle"/>
                                    <Canvas x:Name="InitialCanvas" Margin="3"/>
                                    <TextBlock Text="Initial" Foreground="White" FontWeight="Bold" 
                                               HorizontalAlignment="Center" VerticalAlignment="Top" 
                                               Margin="0,3,0,0" FontSize="10"/>
                                </Grid>
                            </Border>
                            
                            <!-- After 图表 -->
                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="1" 
                                    Background="Black" CornerRadius="3">
                                <Grid>
                                    <Ellipse Fill="Black" Stroke="White" StrokeThickness="1" 
                                             Margin="3" x:Name="AfterCircle"/>
                                    <Canvas x:Name="AfterCanvas" Margin="3"/>
                                    <TextBlock Text="After" Foreground="White" FontWeight="Bold" 
                                               HorizontalAlignment="Center" VerticalAlignment="Top" 
                                               Margin="0,3,0,0" FontSize="10"/>
                                </Grid>
                            </Border>
                            
                            <!-- Add 图表 -->
                            <Border Grid.Column="2" BorderBrush="Black" BorderThickness="1" Margin="1" 
                                    Background="Black" CornerRadius="3">
                                <Grid>
                                    <Ellipse Fill="Black" Stroke="White" StrokeThickness="1" 
                                             Margin="3" x:Name="AddCircle"/>
                                    <Canvas x:Name="AddCanvas" Margin="3"/>
                                    <TextBlock Text="Add" Foreground="White" FontWeight="Bold" 
                                               HorizontalAlignment="Center" VerticalAlignment="Top" 
                                               Margin="0,3,0,0" FontSize="10"/>
                                </Grid>
                            </Border>
                        </Grid>
                    </StackPanel>
                    
                    <!-- 右侧：数据表格 -->
                    <Border Grid.Column="1" BorderBrush="Gray" BorderThickness="1" 
                            Background="White" CornerRadius="3">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <!-- 表格标题 -->
                            <Border Grid.Row="0" Background="LightGray" Padding="8">
                                <TextBlock Text="颗粒尺寸分布统计表" FontSize="14" FontWeight="Bold" 
                                           HorizontalAlignment="Center"/>
                            </Border>
                            
                            <!-- 数据表格 -->
                            <DataGrid Grid.Row="1" x:Name="ParticleDataGrid" 
                                      AutoGenerateColumns="False" IsReadOnly="True"
                                      GridLinesVisibility="All"
                                      HeadersVisibility="Column" CanUserResizeRows="False"
                                      CanUserResizeColumns="True" CanUserSortColumns="True"
                                      Background="White"
                                      FontSize="11" RowHeight="25">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Size" Binding="{Binding SizeRange}" 
                                                        Width="100" FontWeight="Bold"/>
                                    <DataGridTextColumn Header="Before" Binding="{Binding Before}" 
                                                        Width="60"/>
                                    <DataGridTextColumn Header="After" Binding="{Binding After}" 
                                                        Width="60"/>
                                    <DataGridTextColumn Header="Delta" Binding="{Binding Delta}" 
                                                        Width="60"/>
                                    <DataGridTextColumn Header="Add" Binding="{Binding Add}" 
                                                        Width="60"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>

            <TabItem Header="📊 图表分析">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 图表标题 -->
                    <Border Grid.Row="0" Background="DarkBlue" Padding="10">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="Pre数据" Foreground="White" FontWeight="Bold" Margin="0,0,50,0"/>
                            <TextBlock Text="After数据" Foreground="White" FontWeight="Bold" Margin="0,0,50,0"/>
                            <TextBlock Text="Add数据" Foreground="White" FontWeight="Bold"/>
                        </StackPanel>
                    </Border>

                    <!-- 图表区域 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" BorderBrush="Blue" BorderThickness="2" Margin="2">
                            <oxy:PlotView Model="{Binding PrePlotModel}"/>
                        </Border>

                        <Border Grid.Column="1" BorderBrush="Green" BorderThickness="2" Margin="2">
                            <oxy:PlotView Model="{Binding AfterPlotModel}"/>
                        </Border>

                        <Border Grid.Column="2" BorderBrush="Red" BorderThickness="2" Margin="2">
                            <oxy:PlotView Model="{Binding AddPlotModel}"/>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>

            <TabItem Header="📋 数据表格">
                <TabControl>
                    <TabItem Header="Pre数据">
                        <DataGrid ItemsSource="{Binding PreData}" AutoGenerateColumns="True"
                                 IsReadOnly="True" GridLinesVisibility="All" AlternatingRowBackground="LightBlue"/>
                    </TabItem>
                    <TabItem Header="After数据">
                        <DataGrid ItemsSource="{Binding AfterData}" AutoGenerateColumns="True"
                                 IsReadOnly="True" GridLinesVisibility="All" AlternatingRowBackground="LightGreen"/>
                    </TabItem>
                    <TabItem Header="Add数据">
                        <DataGrid ItemsSource="{Binding AddData}" AutoGenerateColumns="True"
                                 IsReadOnly="True" GridLinesVisibility="All" AlternatingRowBackground="LightPink"/>
                    </TabItem>
                </TabControl>
            </TabItem>

            <TabItem Header="📈 统计信息">
                <ScrollViewer>
                    <StackPanel Margin="20">
                        <TextBlock Text="颗粒分析统计信息" FontSize="18" FontWeight="Bold" Margin="0,0,0,20" HorizontalAlignment="Center"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Pre数据统计 -->
                            <GroupBox Grid.Column="0" Header="Pre数据统计" Margin="5" Background="LightBlue">
                                <StackPanel Margin="10">
                                    <TextBlock Text="{Binding PreDataCount, StringFormat='数据点数: {0}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding PreDataAvgSize, StringFormat='平均尺寸: {0:F2}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding PreDataMaxSize, StringFormat='最大尺寸: {0:F2}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding PreDataMinSize, StringFormat='最小尺寸: {0:F2}'}" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>

                            <!-- After数据统计 -->
                            <GroupBox Grid.Column="1" Header="After数据统计" Margin="5" Background="LightGreen">
                                <StackPanel Margin="10">
                                    <TextBlock Text="{Binding AfterDataCount, StringFormat='数据点数: {0}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding AfterDataAvgSize, StringFormat='平均尺寸: {0:F2}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding AfterDataMaxSize, StringFormat='最大尺寸: {0:F2}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding AfterDataMinSize, StringFormat='最小尺寸: {0:F2}'}" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>

                            <!-- Add数据统计 -->
                            <GroupBox Grid.Column="2" Header="Add数据统计" Margin="5" Background="LightPink">
                                <StackPanel Margin="10">
                                    <TextBlock Text="{Binding AddDataCount, StringFormat='数据点数: {0}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding AddDataAvgSize, StringFormat='平均大小: {0:F2}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding AddDataMaxSize, StringFormat='最大大小: {0:F2}'}" Margin="0,5"/>
                                    <TextBlock Text="{Binding AddDataMinSize, StringFormat='最小大小: {0:F2}'}" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>