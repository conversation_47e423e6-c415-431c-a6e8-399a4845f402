using System;
using System.Collections.Generic;

namespace DatalogDrawing.Models
{
    public class FilmThicknessData
    {
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        
        public object this[string columnName]
        {
            get => Data.TryGetValue(columnName, out var value) ? value : null;
            set => Data[columnName] = value;
        }
    }
}