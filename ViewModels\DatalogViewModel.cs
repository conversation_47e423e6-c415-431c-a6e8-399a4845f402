using System;
using System.IO;
using System.Windows.Input;
using DatalogDrawing.ViewModels;

namespace DatalogDrawing.ViewModels
{
    public class DatalogViewModel : ViewModelBase
    {
        public ICommand SelectConfigCommand { get; }
        private string _selectedConfigFile;
        public string SelectedConfigFile
        {
            get => _selectedConfigFile;
            set => SetProperty(ref _selectedConfigFile, value);
        }

        public DatalogViewModel()
        {
            SelectConfigCommand = new RelayCommand<object>(param => SelectConfigFile());
        }

        private void SelectConfigFile()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "JSON Config Files (*.json)|*.json|All Files (*.*)|*.*",
                Title = "选择解析配置文件"
            };
            if (openFileDialog.ShowDialog() == true)
            {
                SelectedConfigFile = openFileDialog.FileName;
            }
        }
    }
} 