<UserControl x:Class="DatalogDrawing.Views.RecipeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:oxy="http://oxyplot.org/wpf"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="LightBlue" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
                <Button Content="📁 选择Recipe文件" Command="{Binding SelectRecipeFileCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <ComboBox Width="120" Margin="0,0,10,0" SelectedIndex="{Binding SelectedRecipeTypeIndex}" VerticalAlignment="Center">
                    <ComboBoxItem Content="主配方"/>
                    <ComboBoxItem Content="子配方"/>
                </ComboBox>
                <Button Content="⚙️ 选择配置" Command="{Binding SelectConfigCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <Button Content="🔍 解析" Command="{Binding ParseRecipeCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <Button Content="📊 生成图表" Command="{Binding GenerateChartCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <Button Content="🗑️ 清空" Command="{Binding ClearDataCommand}" Margin="0,0,10,0" Padding="8,4"/>

                <!-- 测试按钮 -->
                <Separator Margin="10,0" VerticalAlignment="Stretch"/>
                <Button Content="🧪 快速测试" Command="{Binding QuickTestCommand}" Margin="0,0,10,0" Padding="8,4" Background="LightGreen"/>
                <Button Content="🔄 重置测试" Command="{Binding ResetTestCommand}" Margin="0,0,10,0" Padding="8,4" Background="LightYellow"/>
                <Button Content="🐛 调试绑定" Command="{Binding DebugBindingCommand}" Margin="0,0,10,0" Padding="8,4" Background="LightCoral"/>

                <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center" FontWeight="Bold" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 信息栏 -->
        <Border Grid.Row="1" Background="LightGray" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="文件: " FontWeight="Bold"/>
                <TextBlock Text="{Binding SelectedRecipeFile}" Margin="0,0,20,0"/>
                <TextBlock Text="配置: " FontWeight="Bold"/>
                <TextBlock Text="{Binding SelectedConfigFile}" Margin="0,0,20,0"/>
                <TextBlock Text="类型: " FontWeight="Bold"/>
                <TextBlock Text="{Binding RecipeTypeDisplay}" Margin="0,0,20,0"/>
                <TextBlock Text="步骤数: " FontWeight="Bold"/>
                <TextBlock Text="{Binding StepCountDisplay}" Margin="0,0,20,0"/>
                <TextBlock Text="总时间: " FontWeight="Bold"/>
                <TextBlock Text="{Binding TotalTimeDisplay}" Margin="0,0,20,0"/>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <TabControl Grid.Row="2" Margin="5">
            <!-- 数据表格选项卡 -->
            <TabItem Header="Recipe步骤数据">
                <Border BorderBrush="LightGray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <DataGrid x:Name="RecipeStepsDataGrid" Grid.Row="0" ItemsSource="{Binding RecipeSteps}"
                                  AutoGenerateColumns="False" CanUserAddRows="False"
                                  CanUserDeleteRows="False" IsReadOnly="True"
                                  GridLinesVisibility="All" HeadersVisibility="All"
                                  Margin="5">
                            <!-- 列定义将通过代码动态生成 -->
                        </DataGrid>
                    </Grid>
                </Border>
            </TabItem>

            <!-- 工艺时序图选项卡 -->
            <TabItem Header="工艺时序图">
                <Border BorderBrush="LightGray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <oxy:PlotView Grid.Row="0" Model="{Binding ProcessTimelineChart}"
                                      Margin="5" Background="White"/>
                    </Grid>
                </Border>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
 