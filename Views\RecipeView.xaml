<UserControl x:Class="DatalogDrawing.Views.RecipeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:oxy="http://oxyplot.org/wpf"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="LightBlue" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
                <Button Content="📁 选择Recipe文件" Command="{Binding SelectRecipeFileCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <ComboBox Width="120" Margin="0,0,10,0" SelectedIndex="{Binding SelectedRecipeTypeIndex}" VerticalAlignment="Center">
                    <ComboBoxItem Content="主配方"/>
                    <ComboBoxItem Content="子配方"/>
                </ComboBox>
                <Button Content="⚙️ 选择配置" Command="{Binding SelectConfigCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <Button Content="🔍 解析" Command="{Binding ParseRecipeCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <Button Content="📊 生成图表" Command="{Binding GenerateChartCommand}" Margin="0,0,10,0" Padding="8,4"/>
                <Button Content="🗑️ 清空" Command="{Binding ClearDataCommand}" Margin="0,0,10,0" Padding="8,4"/>

                <!-- 测试按钮 -->
                <Separator Margin="10,0" VerticalAlignment="Stretch"/>
                <Button Content="🧪 快速测试" Command="{Binding QuickTestCommand}" Margin="0,0,10,0" Padding="8,4" Background="LightGreen"/>
                <Button Content="🔄 重置测试" Command="{Binding ResetTestCommand}" Margin="0,0,10,0" Padding="8,4" Background="LightYellow"/>

                <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center" FontWeight="Bold" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 信息栏 -->
        <Border Grid.Row="1" Background="LightGray" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="文件: " FontWeight="Bold"/>
                <TextBlock Text="{Binding SelectedRecipeFile}" Margin="0,0,20,0"/>
                <TextBlock Text="配置: " FontWeight="Bold"/>
                <TextBlock Text="{Binding SelectedConfigFile}" Margin="0,0,20,0"/>
                <TextBlock Text="类型: " FontWeight="Bold"/>
                <TextBlock Text="{Binding RecipeTypeDisplay}" Margin="0,0,20,0"/>
                <TextBlock Text="步骤数: " FontWeight="Bold"/>
                <TextBlock Text="{Binding StepCountDisplay}" Margin="0,0,20,0"/>
                <TextBlock Text="总时间: " FontWeight="Bold"/>
                <TextBlock Text="{Binding TotalTimeDisplay}" Margin="0,0,20,0"/>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="2" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：数据表格 -->
            <Border Grid.Column="0" BorderBrush="LightGray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Recipe步骤数据" FontWeight="Bold" FontSize="14"
                               Margin="10" HorizontalAlignment="Center"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding RecipeSteps}"
                              AutoGenerateColumns="False" CanUserAddRows="False"
                              CanUserDeleteRows="False" IsReadOnly="True"
                              GridLinesVisibility="All" HeadersVisibility="All"
                              Margin="5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="StepNo" Binding="{Binding StepNo}" Width="50"/>
                            <DataGridTextColumn Header="StepName" Binding="{Binding StepName}" Width="100"/>
                            <DataGridTextColumn Header="StepTime" Binding="{Binding StepTime}" Width="80"/>
                            <DataGridTextColumn Header="StepUpCondition" Binding="{Binding StepUpCondition}" Width="80"/>
                            <DataGridTextColumn Header="CommandCode" Binding="{Binding CommandCode}" Width="80"/>
                            <DataGridTextColumn Header="ElevatorCommand" Binding="{Binding ElevatorCommandDisplay}" Width="100"/>
                            <DataGridTextColumn Header="BoatRotateCommand" Binding="{Binding BoatRotateCommandDisplay}" Width="100"/>
                            <DataGridTextColumn Header="TempZone1Set" Binding="{Binding TempSetDisplay}" Width="80"/>
                            <DataGridTextColumn Header="TempZone1RampSet" Binding="{Binding RampRateDisplay}" Width="80"/>
                            <DataGridTextColumn Header="TempControlMode" Binding="{Binding TempControlMode}" Width="80"/>
                            <DataGridTextColumn Header="LoopCount" Binding="{Binding LoopCount}" Width="50"/>
                            <DataGridTextColumn Header="APC2ControlMode" Binding="{Binding APC2SettingDisplay}" Width="100"/>
                            <DataGridTextColumn Header="APC3ControlMode" Binding="{Binding APC3SettingDisplay}" Width="100"/>
                            <DataGridTextColumn Header="SubRecipeName" Binding="{Binding SubRecipeNameDisplay}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray"/>

            <!-- 右侧：工艺时序图 -->
            <Border Grid.Column="2" BorderBrush="LightGray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="工艺时序图" FontWeight="Bold" FontSize="14"
                               Margin="10" HorizontalAlignment="Center"/>

                    <oxy:PlotView Grid.Row="1" Model="{Binding ProcessTimelineChart}"
                                  Margin="5" Background="White"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
 