using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Input;
using DatalogDrawing.Models;
using DatalogDrawing.Interfaces;
using DatalogDrawing.Services;
using DatalogDrawing.Views;
using Newtonsoft.Json;
using System.Threading.Tasks;  // 添加Task引用

namespace DatalogDrawing.ViewModels
{
    public class FilmThicknessViewModel : ViewModelBase
    {
        private readonly ConfigService _configService;
        private readonly IDataParser<FilmThicknessData> _parser;
        private CsvFile _selectedCsvFile;
        private ConfigFile _selectedConfigFile;
        private readonly Action<bool> _setBusy;
        private readonly Action<string> _setStatusMessage;
        private string _searchText;
        private string _errorMessage;
        private int _selectedTabIndex;
        private Dictionary<string, object> _csvResult;
        private Visibility _leftPanelVisibility = Visibility.Visible;
        public Visibility LeftPanelVisibility
        {
            get => _leftPanelVisibility;
            set
            {
                if (SetProperty(ref _leftPanelVisibility, value))
                {
                    OnPropertyChanged(nameof(IsCompareMode));
                }
            }
        }

        private GridLength _leftPanelWidth = new GridLength(250);
        public GridLength LeftPanelWidth
        {
            get => _leftPanelWidth;
            set => SetProperty(ref _leftPanelWidth, value);
        }

        // 是否处于对比模式
        public bool IsCompareMode => LeftPanelVisibility == Visibility.Collapsed;

        public ObservableCollection<CsvFile> CsvFiles { get; } = new ObservableCollection<CsvFile>();
        public ObservableCollection<ConfigFile> ConfigFiles { get; } = new ObservableCollection<ConfigFile>();
        public ObservableCollection<CsvRow> FilteredResults { get; } = new ObservableCollection<CsvRow>();
        public ObservableCollection<CsvRow> FilteredResultsStatistics { get; } = new ObservableCollection<CsvRow>();
        public ObservableCollection<CsvRow> FilteredResultsDetail { get; } = new ObservableCollection<CsvRow>();
        private readonly List<Dictionary<string, object>> _allParsedResults = new List<Dictionary<string, object>>();

        

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterDisplayData(false);
                }
            }
        }

        public CsvFile SelectedCsvFile
        {
            get => _selectedCsvFile;
            set
            {
                SetProperty(ref _selectedCsvFile, value);
                (ParseCommand as RelayCommand<object>)?.RaiseCanExecuteChanged();
                OnPropertyChanged(nameof(ShowFileInfo));
            }
        }

        public ConfigFile SelectedConfigFile
        {
            get => _selectedConfigFile;
            set
            {
                SetProperty(ref _selectedConfigFile, value);
                (ParseCommand as RelayCommand<object>)?.RaiseCanExecuteChanged();
                ((RelayCommand<object>)ClearConfigCommand)?.RaiseCanExecuteChanged();
                OnPropertyChanged(nameof(ShowFileInfo));
            }
        }

        public int SelectedTabIndex
        {
            get => _selectedTabIndex;
            set => SetProperty(ref _selectedTabIndex, value);
        }

        public ICommand ExportCommand { get; }
        public ICommand ParseCommand { get; }
        public ICommand LoadHistoryCommand { get; }
        public ICommand SelectConfigCommand { get; }
        public ICommand ImportCsvCommand { get; }
        public ICommand ClearDataCommand { get; }
        public ICommand RemoveCsvCommand { get; }
        public ICommand ClearCsvsCommand { get; }
        public ICommand ClearConfigCommand { get; }
        public ICommand RemoveConfigCommand { get; }
        public ICommand ClearConfigsCommand { get; }
        public ICommand ShowDetailedReportCommand { get; }
        public ICommand ExportStatisticsCommand { get; }

        public DataTable StatisticsTable { get; } = new DataTable();
        public DataTable DetailTable { get; } = new DataTable();
        public DataTable ResultTable { get; } = new DataTable();

        // 新增属性用于统计信息显示
        private string _statisticsSummary = "暂无统计数据";
        public string StatisticsSummary
        {
            get => _statisticsSummary;
            set => SetProperty(ref _statisticsSummary, value);
        }

        private string _detailSummary = "暂无详细信息";
        public string DetailSummary
        {
            get => _detailSummary;
            set => SetProperty(ref _detailSummary, value);
        }

        private string _dataQualityInfo = "暂无数据质量信息";
        public string DataQualityInfo
        {
            get => _dataQualityInfo;
            set => SetProperty(ref _dataQualityInfo, value);
        }

        private string _parseTime = "未解析";
        public string ParseTime
        {
            get => _parseTime;
            set => SetProperty(ref _parseTime, value);
        }

        private string _dataStatus = "无数据";
        public string DataStatus
        {
            get => _dataStatus;
            set => SetProperty(ref _dataStatus, value);
        }

        public bool ShowFileInfo
        {
            get
            {
                // 只要有CSV文件就显示文件信息，配置文件可选
                var result = SelectedCsvFile != null;
                System.Diagnostics.Debug.WriteLine($"ShowFileInfo: CSV={SelectedCsvFile?.FileName}, Config={SelectedConfigFile?.FileName}, Result={result}");
                return result;
            }
        }

        private bool _isDataEmpty = true;
        public bool IsDataEmpty
        {
            get => _isDataEmpty;
            set
            {
                SetProperty(ref _isDataEmpty, value);
                // 更新命令状态
                ((RelayCommand<object>)ShowDetailedReportCommand)?.RaiseCanExecuteChanged();
                ((RelayCommand<object>)ExportStatisticsCommand)?.RaiseCanExecuteChanged();
            }
        }

        public FilmThicknessViewModel(ConfigService configService, IDataParser<FilmThicknessData> dataParser, Action<bool> setBusy, Action<string> setStatusMessage = null)
        {
            _configService = configService;
            _parser = dataParser;
            _setBusy = setBusy;
            _setStatusMessage = setStatusMessage;

            // 初始化默认选项卡索引为0（第一个选项卡）
            _selectedTabIndex = 0;

            ImportCsvCommand = new RelayCommand<object>(param => ImportCsv());
            ParseCommand = new RelayCommand<object>(async param => await Parse(), param => CanParse());
            ExportCommand = new RelayCommand<object>(param => ExportToCsv());
            LoadHistoryCommand = new RelayCommand<object>(param => LoadHistory());
            SelectConfigCommand = new RelayCommand<object>(param => SelectConfigFile());
            ClearDataCommand = new RelayCommand<object>(param => ClearData());
            RemoveCsvCommand = new RelayCommand<object>(param => RemoveCsv(), param => SelectedCsvFile != null);
            ClearCsvsCommand = new RelayCommand<object>(param => ClearCsvs(), param => CsvFiles.Count > 0);
            ClearConfigCommand = new RelayCommand<object>(param => ClearConfig(), param => SelectedConfigFile != null);
            RemoveConfigCommand = new RelayCommand<object>(param => RemoveConfig(), param => SelectedConfigFile != null);
            ClearConfigsCommand = new RelayCommand<object>(param => ClearConfigs(), param => ConfigFiles.Count > 0);
            ShowDetailedReportCommand = new RelayCommand<object>(param => ShowDetailedReport(), param => !IsDataEmpty);
            ExportStatisticsCommand = new RelayCommand<object>(param => ExportStatistics(), param => !IsDataEmpty);
            //LoadHistory();

            // 初始化 DatabaseService（单例）
            var db = DatabaseService.Instance; // 确保初始化

            // 自动加载最新的膜厚数据会话
            LoadLatestSession();
        }

        private void ImportCsv()
        {
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "CSV文件 (*.csv)|*.csv",
                Title = "选择要导入的CSV文件",
                Multiselect = true 
            };

            if (dialog.ShowDialog() == true)
            {
                foreach (var filePath in dialog.FileNames)
                {
                    var fileInfo = new FileInfo(filePath);
                    var csvFile = new CsvFile
                    {
                        FileName = fileInfo.Name,
                        FilePath = fileInfo.FullName,
                        FileSize = fileInfo.Length / 1024,
                        ImportTime = DateTime.Now
                    };
                    if (!CsvFiles.Any(f => f.FilePath == csvFile.FilePath))
                    {
                        CsvFiles.Add(csvFile);
                    }
                }
                if (CsvFiles.Count > 0 && SelectedCsvFile == null)
                {
                    SelectedCsvFile = CsvFiles[0];
                }
                
                // 检查是否加载了配置
                bool configLoaded = _configService.Config != null && 
                                   _configService.Config.CsvParserConfig != null && 
                                   _configService.Config.CsvParserConfig.RowFilters != null && 
                                   _configService.Config.CsvParserConfig.RowFilters.Any();
                
                // 如果已经选择了配置文件，自动触发解析
                if (SelectedCsvFile != null && configLoaded)
                {
                    _ = Parse();
                }
                else if (!configLoaded)
                {
                    _setStatusMessage?.Invoke("请选择配置文件");
                }
            }
        }

        private bool CanParse() => SelectedCsvFile != null && SelectedConfigFile != null;

        private void SelectConfigFile()
        {
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "JSON文件 (*.json)|*.json",
                Title = "选择配置文件"
            };
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    // 设置配置路径并加载配置文件
                    if (_configService.SetConfigPath(dialog.FileName) && _configService.LoadConfig())
                    {
                        var fileInfo = new System.IO.FileInfo(dialog.FileName);
                        var configFile = new ConfigFile
                        {
                            FileName = fileInfo.Name,
                            FilePath = dialog.FileName,
                            FileSize = fileInfo.Length,
                            ImportTime = DateTime.Now
                        };

                        // 检查是否已存在相同文件
                        var existingFile = ConfigFiles.FirstOrDefault(f => f.FilePath == dialog.FileName);
                        if (existingFile == null)
                        {
                            ConfigFiles.Add(configFile);
                        }
                        else
                        {
                            // 更新导入时间
                            existingFile.ImportTime = DateTime.Now;
                            configFile = existingFile;
                        }

                        SelectedConfigFile = configFile;
                        _setStatusMessage?.Invoke("配置文件已加载");
                    }
                    else
                    {
                        _setStatusMessage?.Invoke("配置文件加载失败");
                    }
                }
                catch (Exception ex)
                {
                    _setStatusMessage?.Invoke($"加载配置文件失败: {ex.Message}");
                }
            }
        }
        
        private void LoadHistory()
        {
            var path = GetHistoryFilePath();
            if (File.Exists(path))
            {
                try
                {
                    var json = File.ReadAllText(path);
                    var history = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(json);
                    if (history != null)
                    {
                        _allParsedResults.Clear();
                        _allParsedResults.AddRange(history);
                        FilterDisplayData(false);
                        ErrorMessage = "历史记录已加载";
                    }
                }
                catch (Exception ex)
                {
                    ErrorMessage = $"加载历史记录失败: {ex.Message}";
                }
            }
        }

        private void SaveHistory()
        {
            try
            {
                var path = GetHistoryFilePath();
                var json = JsonConvert.SerializeObject(_allParsedResults, Formatting.Indented);
                File.WriteAllText(path, json);
            }
            catch (Exception ex)
            {
                 ErrorMessage = $"保存历史记录失败: {ex.Message}";
            }
        }

        private string GetHistoryFilePath()
        {
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appData, "DatalogDrawing");
            Directory.CreateDirectory(appFolder);
            return Path.Combine(appFolder, "history.json");
        }

        // 自动加载最新的膜厚数据会话
        private void LoadLatestSession()
        {
            try
            {
                var sessions = DatabaseService.Instance.QueryFullResults("FilmThickness");
                if (sessions != null && sessions.Count > 0)
                {
                    // 获取最新的会话（按时间戳排序）
                    var latestSession = sessions.OrderByDescending(s => s["Timestamp"]).FirstOrDefault();
                    if (latestSession != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"LoadLatestSession: 找到最新会话 ID={latestSession["Id"]}");

                        // 提取数据
                        Dictionary<string, object> fullResultData = null;
                        if (latestSession["FullResultData"] is Dictionary<string, object> dict)
                            fullResultData = dict;
                        else if (latestSession["FullResultData"] is Newtonsoft.Json.Linq.JObject jObj)
                            fullResultData = jObj.ToObject<Dictionary<string, object>>();

                        // 转换SlotInfo
                        if (fullResultData != null)
                        {
                            var keys = fullResultData.Keys.ToList();
                            foreach (var key in keys)
                            {
                                var val = fullResultData[key];
                                if (val is Newtonsoft.Json.Linq.JObject jobj)
                                {
                                    fullResultData[key] = jobj.ToObject<SlotInfo>();
                                }
                            }

                            // 获取文件路径
                            var csvFilePath = latestSession["ParsedFilePath"]?.ToString();
                            var configFilePath = latestSession["ConfigFilePath"]?.ToString();

                            // 设置数据
                            SetResultData(fullResultData, true, csvFilePath, configFilePath);

                            System.Diagnostics.Debug.WriteLine($"LoadLatestSession: 已加载最新会话数据");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("LoadLatestSession: 没有找到历史会话");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadLatestSession 失败: {ex.Message}");
            }
        }

        private void ExportToCsv()
        {
            if (FilteredResults == null || FilteredResults.Count == 0)
            {
                _setStatusMessage?.Invoke("没有数据可以导出");
                return;
            }

            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "CSV文件 (*.csv)|*.csv",
                FileName = "ExportedFilmThicknessData.csv"
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var sb = new StringBuilder();
                    sb.AppendLine("SlotNumber,SlotTotalInfo,SlotDetailInfo");

                    foreach (var row in FilteredResults)
                    {
                        var slotNumber = row["SlotNumber"]?.ToString() ?? string.Empty;
                        var totalInfo = row["SlotTotalInfo"]?.ToString() ?? string.Empty;
                        var detailInfo = row["SlotDetailInfo"]?.ToString() ?? string.Empty;

                        var escapedSlotNumber = $"\"{slotNumber.Replace("\"", "\"\"")}\"";
                        var escapedTotalInfo = $"\"{totalInfo.Replace("\"", "\"\"")}\"";
                        var escapedDetailInfo = $"\"{detailInfo.Replace("\"", "\"\"")}\"";

                        sb.AppendLine($"{escapedSlotNumber},{escapedTotalInfo},{escapedDetailInfo}");
                    }

                    File.WriteAllText(dialog.FileName, sb.ToString(), Encoding.UTF8);
                    _setStatusMessage?.Invoke("导出成功");
                }
                catch (Exception ex)
                {
                    _setStatusMessage?.Invoke($"导出失败: {ex.Message}");
                }
            }
        }

        private async Task Parse()
        {
            // 1. 配置文件选择记忆逻辑
            if (_configService.Config == null || _configService.Config.CsvParserConfig == null || _configService.Config.CsvParserConfig.RowFilters == null || !_configService.Config.CsvParserConfig.RowFilters.Any())
            {
                // 提示用户选择配置文件
                _setStatusMessage?.Invoke("请先选择配置文件");
                SelectConfigFile();

                // 再次检查配置是否有效
                if (_configService.Config == null || _configService.Config.CsvParserConfig == null || _configService.Config.CsvParserConfig.RowFilters == null || !_configService.Config.CsvParserConfig.RowFilters.Any())
                {
                    _setStatusMessage?.Invoke("未选择有效配置文件，无法解析");
                    return;
                }
            }

            if (SelectedCsvFile == null)
            {
                _setStatusMessage?.Invoke("请先选择要解析的CSV文件");
                return;
            }

            _setBusy(true);
            _setStatusMessage?.Invoke("开始解析数据...");

            try
            {
                _csvResult = await _parser.ParseAsync(SelectedCsvFile.FilePath, _configService.Config);
                
                // 检查slot详细信息的行数是否一致
                bool isSameDetailCount = CheckSlotDetailConsistency();
                if (!isSameDetailCount)
                {
                    // 打开选择窗口，让用户选择需要显示的slot
                    var selectedSlots = ShowSlotSelectorDialog();
                    if (selectedSlots == null || selectedSlots.Count == 0)
                    {
                        // 用户取消或未选择，使用所有slot
                        selectedSlots = new List<string>(_csvResult.Keys);
                    }
                    
                    // 根据用户选择过滤slot
                    var filteredResult = new Dictionary<string, object>();
                    foreach (var slot in selectedSlots)
                    {
                        if (_csvResult.ContainsKey(slot))
                        {
                            filteredResult[slot] = _csvResult[slot];
                        }
                    }
                    
                    // 使用过滤后的结果
                    _csvResult = filteredResult;
                }
               
                // 保存整个解析结果到数据库
                try
                {
                    var fullData = new Dictionary<string, object>
                    {
                        ["ParsedFilePath"] = SelectedCsvFile.FilePath,
                        ["ConfigFilePath"] = SelectedConfigFile?.FilePath,
                        ["UserInputValue"] = _configService.Config?.DisplayConfig?.UserInputValue ?? 1.0,
                        ["FullResultData"] = _csvResult,
                        ["HeatMapData"] = GetHeatMapData()
                    };
                    DatabaseService.Instance.InsertFullResult("FilmThickness", fullData);
                }
                catch (Exception dbEx)
                {
                    _setStatusMessage?.Invoke($"数据库保存失败: {dbEx.Message}");
                }

                FilterDisplayData(false);
                SaveHistory();
                UpdateStatisticsSummary();

                // 更新解析状态信息
                ParseTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                DataStatus = $"已解析 ({_csvResult?.Count ?? 0} 个Slot)";
                IsDataEmpty = false;

                System.Diagnostics.Debug.WriteLine($"Parse完成: IsDataEmpty = {IsDataEmpty}, _csvResult.Count = {_csvResult?.Count}");

                _setStatusMessage?.Invoke("解析完成");
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"解析失败: {ex.Message}");
            }
            finally { _setBusy(false); }
        }

        // 更新统计信息摘要
        private void UpdateStatisticsSummary()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始更新统计信息");

                if (_csvResult != null && _csvResult.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"_csvResult包含 {_csvResult.Count} 个键: {string.Join(", ", _csvResult.Keys)}");

                    var totalSlots = _csvResult.Count;
                    var totalStatisticsRows = 0;
                    var totalDetailRows = 0;
                    var allThicknessValues = new List<double>();
                    var slotSummaries = new List<string>();

                    foreach (var kvp in _csvResult)
                    {
                        var slotNumber = kvp.Key;
                        var slotInfo = kvp.Value as SlotInfo;

                        if (slotInfo != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"处理Slot {slotNumber}: Statistics={slotInfo.SlotTotalInfo?.Count ?? 0}, Details={slotInfo.SlotDetailInfo?.Count ?? 0}");

                            // 统计行数
                            if (slotInfo.SlotTotalInfo != null)
                                totalStatisticsRows += slotInfo.SlotTotalInfo.Count;

                            if (slotInfo.SlotDetailInfo != null)
                                totalDetailRows += slotInfo.SlotDetailInfo.Count;

                            // 提取厚度数据
                            var slotThicknesses = ExtractThicknessFromSlot(slotInfo);
                            allThicknessValues.AddRange(slotThicknesses);

                            // 生成Slot摘要
                            if (slotThicknesses.Count > 0)
                            {
                                var slotAvg = slotThicknesses.Average();
                                var slotMin = slotThicknesses.Min();
                                var slotMax = slotThicknesses.Max();
                                slotSummaries.Add($"Slot {slotNumber}: {slotThicknesses.Count}点, 平均{slotAvg:F1}Å, 范围{slotMin:F1}-{slotMax:F1}Å");
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"统计结果: 总Slot={totalSlots}, 统计行={totalStatisticsRows}, 详细行={totalDetailRows}, 厚度数据点={allThicknessValues.Count}");

                    if (allThicknessValues.Count > 0)
                    {
                        var avgThickness = allThicknessValues.Average();
                        var minThickness = allThicknessValues.Min();
                        var maxThickness = allThicknessValues.Max();
                        var stdDev = CalculateStandardDeviation(allThicknessValues);

                        StatisticsSummary = $"总Slot数: {totalSlots}\n" +
                                          $"统计数据行: {totalStatisticsRows}\n" +
                                          $"详细数据行: {totalDetailRows}\n" +
                                          $"有效厚度点: {allThicknessValues.Count}\n" +
                                          $"平均厚度: {avgThickness:F2}Å\n" +
                                          $"厚度范围: {minThickness:F2} - {maxThickness:F2}Å\n" +
                                          $"标准差: {stdDev:F2}Å";

                        var topSlots = slotSummaries.Take(5).ToList();
                        var slotSummaryText = topSlots.Count > 0 ? string.Join("\n", topSlots) : "无有效Slot数据";
                        if (slotSummaries.Count > 5)
                        {
                            slotSummaryText += $"\n... 还有{slotSummaries.Count - 5}个Slot";
                        }

                        // 计算数据质量和分布统计
                        var dataQuality = totalDetailRows > 0 ? (allThicknessValues.Count * 100.0 / totalDetailRows) : 0;
                        var uniformity = maxThickness > minThickness ? ((maxThickness - minThickness) / avgThickness * 100) : 0;

                        // 更新数据质量信息
                        UpdateDataQualityInfo(dataQuality, uniformity, allThicknessValues.Count, totalDetailRows, slotSummaries.Count);

                        DetailSummary = $"📊 解析信息:\n" +
                                      $"解析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                                      $"CSV文件: {SelectedCsvFile?.FileName ?? "未知"}\n" +
                                      $"配置文件: {SelectedConfigFile?.FileName ?? "未知"}\n\n" +
                                      $"📈 数据质量:\n" +
                                      $"数据完整性: {dataQuality:F1}% ({allThicknessValues.Count}/{totalDetailRows})\n" +
                                      $"厚度均匀性: {uniformity:F1}% 变异\n" +
                                      $"数据分布: 正常\n\n" +
                                      $"🎯 Slot详情:\n{slotSummaryText}";

                        System.Diagnostics.Debug.WriteLine($"统计信息已更新: StatisticsSummary={StatisticsSummary}");
                        System.Diagnostics.Debug.WriteLine($"详细信息已更新: DetailSummary={DetailSummary}");
                        System.Diagnostics.Debug.WriteLine($"数据质量信息: DataQualityInfo={DataQualityInfo}");
                    }
                    else
                    {
                        StatisticsSummary = $"总Slot数: {totalSlots}\n" +
                                          $"统计数据行: {totalStatisticsRows}\n" +
                                          $"详细数据行: {totalDetailRows}\n" +
                                          $"但未找到有效的厚度数据";
                        DetailSummary = "请检查数据格式和配置文件是否正确\n可能的原因:\n1. 厚度字段名不匹配\n2. 数据格式不正确\n3. 配置文件设置有误";
                    }
                }
                else
                {
                    StatisticsSummary = "暂无解析数据";
                    DetailSummary = "请先选择CSV文件和配置文件，然后点击解析按钮";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"统计信息更新失败: {ex.Message}");
                StatisticsSummary = $"统计信息计算失败: {ex.Message}";
                DetailSummary = $"详细错误信息: {ex.Message}\n请检查数据格式和配置文件";
            }
        }

        // 更新数据质量信息
        private void UpdateDataQualityInfo(double dataQuality, double uniformity, int validPoints, int totalPoints, int slotCount)
        {
            try
            {
                var qualityLevel = GetQualityLevel(dataQuality);
                var uniformityLevel = GetUniformityLevel(uniformity);

                DataQualityInfo = $"数据完整性: {dataQuality:F1}% ({validPoints}/{totalPoints}) - {qualityLevel}\n" +
                                $"厚度均匀性: {uniformity:F1}% 变异 - {uniformityLevel}\n" +
                                $"Slot覆盖: {slotCount} 个Slot\n" +
                                $"数据密度: {(validPoints / (double)slotCount):F1} 点/Slot\n" +
                                $"整体评级: {GetOverallRating(dataQuality, uniformity)}";
            }
            catch (Exception ex)
            {
                DataQualityInfo = $"数据质量评估失败: {ex.Message}";
            }
        }

        // 获取数据质量等级
        private string GetQualityLevel(double quality)
        {
            if (quality >= 90) return "优秀 ✅";
            if (quality >= 75) return "良好 ✔️";
            if (quality >= 60) return "一般 ⚠️";
            if (quality >= 40) return "较差 ❌";
            return "很差 ❌❌";
        }

        // 获取均匀性等级
        private string GetUniformityLevel(double uniformity)
        {
            if (uniformity <= 5) return "非常均匀 ✅";
            if (uniformity <= 10) return "均匀 ✔️";
            if (uniformity <= 20) return "一般 ⚠️";
            if (uniformity <= 30) return "不均匀 ❌";
            return "很不均匀 ❌❌";
        }

        // 获取整体评级
        private string GetOverallRating(double quality, double uniformity)
        {
            var score = (quality / 100.0) * 0.6 + (Math.Max(0, 100 - uniformity) / 100.0) * 0.4;

            if (score >= 0.9) return "A+ 优秀";
            if (score >= 0.8) return "A 良好";
            if (score >= 0.7) return "B+ 中上";
            if (score >= 0.6) return "B 中等";
            if (score >= 0.5) return "C 中下";
            return "D 需要改进";
        }

        // 显示详细报告
        private void ShowDetailedReport()
        {
            try
            {
                var report = GenerateDetailedStatistics();
                var window = new System.Windows.Window
                {
                    Title = "膜厚详细统计报告",
                    Width = 600,
                    Height = 500,
                    WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen
                };

                var scrollViewer = new System.Windows.Controls.ScrollViewer();
                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = report,
                    Margin = new System.Windows.Thickness(20),
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 12,
                    TextWrapping = System.Windows.TextWrapping.Wrap
                };

                scrollViewer.Content = textBlock;
                window.Content = scrollViewer;
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"显示详细报告失败: {ex.Message}");
            }
        }

        // 导出统计信息
        private void ExportStatistics()
        {
            try
            {
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "文本文件 (*.txt)|*.txt|CSV文件 (*.csv)|*.csv",
                    Title = "导出统计信息",
                    FileName = $"膜厚统计_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (dialog.ShowDialog() == true)
                {
                    var report = GenerateDetailedStatistics();
                    System.IO.File.WriteAllText(dialog.FileName, report, System.Text.Encoding.UTF8);
                    _setStatusMessage?.Invoke($"统计信息已导出到: {dialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"导出统计信息失败: {ex.Message}");
            }
        }

        // 生成详细的统计报告
        private string GenerateDetailedStatistics()
        {
            try
            {
                if (_csvResult == null || _csvResult.Count == 0)
                    return "暂无数据";

                var report = new StringBuilder();
                report.AppendLine("📊 详细统计报告");
                report.AppendLine("=" + new string('=', 30));

                var allThicknesses = new List<double>();
                var slotStats = new List<SlotStatistics>();

                foreach (var kvp in _csvResult)
                {
                    var slotNumber = kvp.Key;
                    var slotInfo = kvp.Value as SlotInfo;

                    if (slotInfo != null)
                    {
                        var slotThicknesses = ExtractThicknessFromSlot(slotInfo);
                        allThicknesses.AddRange(slotThicknesses);

                        if (slotThicknesses.Count > 0)
                        {
                            slotStats.Add(new SlotStatistics
                            {
                                Slot = slotNumber,
                                Count = slotThicknesses.Count,
                                Average = slotThicknesses.Average(),
                                Min = slotThicknesses.Min(),
                                Max = slotThicknesses.Max()
                            });
                        }
                    }
                }

                // 整体统计
                if (allThicknesses.Count > 0)
                {
                    var overall = allThicknesses;
                    report.AppendLine($"整体统计:");
                    report.AppendLine($"  数据点数: {overall.Count}");
                    report.AppendLine($"  平均值: {overall.Average():F2}Å");
                    report.AppendLine($"  中位数: {GetMedian(overall):F2}Å");
                    report.AppendLine($"  标准差: {CalculateStandardDeviation(overall):F2}Å");
                    report.AppendLine($"  最小值: {overall.Min():F2}Å");
                    report.AppendLine($"  最大值: {overall.Max():F2}Å");
                    report.AppendLine($"  范围: {overall.Max() - overall.Min():F2}Å");
                    report.AppendLine();
                }

                // Slot统计
                if (slotStats.Count > 0)
                {
                    report.AppendLine("各Slot统计:");
                    foreach (var stat in slotStats.OrderBy(s => s.Slot))
                    {
                        report.AppendLine($"  Slot {stat.Slot}: {stat.Count}点, 平均{stat.Average:F1}Å, 范围{stat.Min:F1}-{stat.Max:F1}Å");
                    }
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"生成统计报告失败: {ex.Message}";
            }
        }

        // 计算中位数
        private double GetMedian(List<double> values)
        {
            if (values.Count == 0) return 0;

            var sorted = values.OrderBy(x => x).ToList();
            int count = sorted.Count;

            if (count % 2 == 0)
            {
                return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
            }
            else
            {
                return sorted[count / 2];
            }
        }

        // 从SlotInfo中提取厚度数据
        private List<double> ExtractThicknessFromSlot(SlotInfo slotInfo)
        {
            var thicknesses = new List<double>();

            try
            {
                // 从统计数据中提取厚度
                if (slotInfo.SlotTotalInfo != null)
                {
                    foreach (var row in slotInfo.SlotTotalInfo)
                    {
                        var thickness = ExtractThicknessFromRow(row);
                        if (thickness.HasValue)
                        {
                            thicknesses.Add(thickness.Value);
                        }
                    }
                }

                // 从详细数据中提取厚度
                if (slotInfo.SlotDetailInfo != null)
                {
                    foreach (var row in slotInfo.SlotDetailInfo)
                    {
                        var thickness = ExtractThicknessFromRow(row);
                        if (thickness.HasValue)
                        {
                            thicknesses.Add(thickness.Value);
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Slot {slotInfo.SlotNumber} 提取到 {thicknesses.Count} 个厚度值");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从Slot {slotInfo.SlotNumber} 提取厚度数据失败: {ex.Message}");
            }

            return thicknesses;
        }

        // 从数据行中提取厚度值
        private double? ExtractThicknessFromRow(Dictionary<string, object> row)
        {
            try
            {
                // 尝试常见的厚度字段名
                var possibleKeys = new[] {
                    "Thickness", "thickness", "厚度", "膜厚",
                    "THK", "thk", "Value", "value", "值",
                    "Mean", "mean", "平均", "Avg", "avg",
                    "Target", "target", "目标"
                };

                foreach (var key in possibleKeys)
                {
                    if (row.ContainsKey(key))
                    {
                        var value = row[key];
                        if (value != null && double.TryParse(value.ToString(), out double thickness))
                        {
                            // 过滤明显异常的值
                            if (thickness > 0 && thickness < 100000) // 假设厚度在0-100000Å之间
                            {
                                return thickness;
                            }
                        }
                    }
                }

                // 如果没有找到明确的厚度字段，尝试第一个数值字段
                foreach (var kvp in row)
                {
                    if (kvp.Value != null && double.TryParse(kvp.Value.ToString(), out double thickness))
                    {
                        // 过滤明显异常的值
                        if (thickness > 0 && thickness < 100000)
                        {
                            return thickness;
                        }
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        // 计算标准差（重载版本）
        private double CalculateStandardDeviation(List<double> values)
        {
            try
            {
                if (values.Count <= 1) return 0.0;

                var mean = values.Average();
                var variance = values.Select(x => Math.Pow(x - mean, 2)).Average();
                return Math.Sqrt(variance);
            }
            catch
            {
                return 0.0;
            }
        }

        // 计算标准差
        private double CalculateStandardDeviation()
        {
            try
            {
                var allThicknesses = new List<double>();
                if (_csvResult != null && _csvResult.ContainsKey("SlotResults"))
                {
                    var slotResults = _csvResult["SlotResults"] as Dictionary<string, object>;
                    if (slotResults != null)
                    {
                        foreach (var slot in slotResults.Values)
                        {
                            if (slot is Dictionary<string, object> slotData && slotData.ContainsKey("Details"))
                            {
                                var details = slotData["Details"] as List<FilmThicknessData>;
                                if (details != null)
                                {
                                    var thicknesses = details.Select(d => GetThicknessValue(d))
                                                            .Where(t => t.HasValue)
                                                            .Select(t => t.Value);
                                    allThicknesses.AddRange(thicknesses);
                                }
                            }
                        }
                    }
                }

                if (allThicknesses.Count > 1)
                {
                    var mean = allThicknesses.Average();
                    var variance = allThicknesses.Select(x => Math.Pow(x - mean, 2)).Average();
                    return Math.Sqrt(variance);
                }
                return 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        // 清除数据
        private void ClearData()
        {
            try
            {
                // 清除表格数据
                StatisticsTable.Clear();
                DetailTable.Clear();
                ResultTable.Clear();

                // 清除解析结果
                _csvResult = null;
                _allParsedResults.Clear();

                // 重置统计信息
                StatisticsSummary = "暂无统计数据";
                DetailSummary = "暂无详细信息";
                DataQualityInfo = "暂无数据质量信息";
                ParseTime = "未解析";
                DataStatus = "无数据";
                IsDataEmpty = true;

                // 切换到第一个选项卡
                SelectedTabIndex = 0;

                _setStatusMessage?.Invoke("数据已清除");
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"清除数据失败: {ex.Message}");
            }
        }

        // 移除选中的CSV文件
        private void RemoveCsv()
        {
            try
            {
                if (SelectedCsvFile != null)
                {
                    CsvFiles.Remove(SelectedCsvFile);
                    SelectedCsvFile = CsvFiles.FirstOrDefault();
                    _setStatusMessage?.Invoke("CSV文件已移除");

                    // 更新命令状态
                    ((RelayCommand<object>)RemoveCsvCommand).RaiseCanExecuteChanged();
                    ((RelayCommand<object>)ClearCsvsCommand).RaiseCanExecuteChanged();
                    ((RelayCommand<object>)ParseCommand).RaiseCanExecuteChanged();
                }
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"移除CSV文件失败: {ex.Message}");
            }
        }

        // 清空所有CSV文件
        private void ClearCsvs()
        {
            try
            {
                CsvFiles.Clear();
                SelectedCsvFile = null;
                _setStatusMessage?.Invoke("所有CSV文件已清空");

                // 更新命令状态
                ((RelayCommand<object>)RemoveCsvCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)ClearCsvsCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)ParseCommand).RaiseCanExecuteChanged();
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"清空CSV文件失败: {ex.Message}");
            }
        }

        // 清除选中的配置文件
        private void ClearConfig()
        {
            try
            {
                SelectedConfigFile = null;
                _setStatusMessage?.Invoke("配置文件选择已清除");

                // 更新命令状态
                ((RelayCommand<object>)ClearConfigCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)RemoveConfigCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)ParseCommand).RaiseCanExecuteChanged();
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"清除配置文件失败: {ex.Message}");
            }
        }

        // 移除选中的配置文件
        private void RemoveConfig()
        {
            try
            {
                if (SelectedConfigFile != null)
                {
                    ConfigFiles.Remove(SelectedConfigFile);
                    SelectedConfigFile = ConfigFiles.FirstOrDefault();
                    _setStatusMessage?.Invoke("配置文件已移除");

                    // 更新命令状态
                    ((RelayCommand<object>)RemoveConfigCommand).RaiseCanExecuteChanged();
                    ((RelayCommand<object>)ClearConfigsCommand).RaiseCanExecuteChanged();
                    ((RelayCommand<object>)ClearConfigCommand).RaiseCanExecuteChanged();
                    ((RelayCommand<object>)ParseCommand).RaiseCanExecuteChanged();
                }
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"移除配置文件失败: {ex.Message}");
            }
        }

        // 清空所有配置文件
        private void ClearConfigs()
        {
            try
            {
                ConfigFiles.Clear();
                SelectedConfigFile = null;
                _setStatusMessage?.Invoke("所有配置文件已清空");

                // 更新命令状态
                ((RelayCommand<object>)RemoveConfigCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)ClearConfigsCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)ClearConfigCommand).RaiseCanExecuteChanged();
                ((RelayCommand<object>)ParseCommand).RaiseCanExecuteChanged();
            }
            catch (Exception ex)
            {
                _setStatusMessage?.Invoke($"清空配置文件失败: {ex.Message}");
            }
        }

        // 从FilmThicknessData中获取厚度值的辅助方法
        private double? GetThicknessValue(FilmThicknessData data)
        {
            try
            {
                if (data?.Data == null || data.Data.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("FilmThicknessData.Data为空");
                    return null;
                }

                System.Diagnostics.Debug.WriteLine($"FilmThicknessData包含字段: {string.Join(", ", data.Data.Keys)}");

                // 尝试常见的厚度字段名
                var possibleKeys = new[] { "Thickness", "thickness", "厚度", "Value", "value", "膜厚", "THK", "thk" };

                foreach (var key in possibleKeys)
                {
                    if (data.Data.ContainsKey(key))
                    {
                        var value = data.Data[key];
                        System.Diagnostics.Debug.WriteLine($"找到字段 {key}: {value}");
                        if (value != null && double.TryParse(value.ToString(), out double thickness))
                        {
                            System.Diagnostics.Debug.WriteLine($"成功解析厚度值: {thickness}");
                            return thickness;
                        }
                    }
                }

                // 如果没有找到明确的厚度字段，尝试第一个数值字段
                System.Diagnostics.Debug.WriteLine("未找到明确的厚度字段，尝试第一个数值字段");
                foreach (var kvp in data.Data)
                {
                    if (kvp.Value != null && double.TryParse(kvp.Value.ToString(), out double thickness))
                    {
                        System.Diagnostics.Debug.WriteLine($"使用字段 {kvp.Key} 作为厚度值: {thickness}");
                        return thickness;
                    }
                }

                System.Diagnostics.Debug.WriteLine("未找到任何有效的数值字段");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetThicknessValue异常: {ex.Message}");
                return null;
            }
        }

        // 添加方法获取热力图数据
        public Dictionary<string, List<HeatMapPoint>> GetHeatMapData()
        {
            var result = new Dictionary<string, List<HeatMapPoint>>();
            
            try
            {
                if (_csvResult == null || _csvResult.Count == 0)
                    return result;
                    
                foreach (var slotEntry in _csvResult)
                {
                    string slotName = slotEntry.Key;
                    if (slotEntry.Value is SlotInfo slotInfo)
                    {
                        // 获取热力图数据
                        var detailList = slotInfo.SlotDetailInfo;
                        var pointList = new List<HeatMapPoint>();
                        
                        // 从详细数据中提取测量点的厚度值
                        foreach (var item in detailList)
                        {
                            // 查找X和Y列
                            string xKey = slotInfo.columnDetailNames.FirstOrDefault(k => k == "X");
                            string yKey = slotInfo.columnDetailNames.FirstOrDefault(k => k == "Y");
                            
                            // 查找厚度列，可能包含@符号
                            string thicknessKey = slotInfo.columnDetailNames.FirstOrDefault(k => k.Contains("Thickness"));
                            
                            if (!string.IsNullOrEmpty(xKey) && !string.IsNullOrEmpty(yKey) && 
                                !string.IsNullOrEmpty(thicknessKey) &&
                                item.ContainsKey(xKey) && item.ContainsKey(yKey) && item.ContainsKey(thicknessKey))
                            {
                                try
                                {
                                    double x = Convert.ToDouble(item[xKey]);
                                    double y = Convert.ToDouble(item[yKey]);
                                    double thickness = Convert.ToDouble(item[thicknessKey]);
                                    
                                    // 添加数据点
                                    pointList.Add(new HeatMapPoint { X = x, Y = y, Value = thickness });
                                }
                                catch (Exception ex)
                                {
                                    // 记录转换错误但继续处理
                                    System.Diagnostics.Debug.WriteLine($"Error converting data: {ex.Message}");
                                }
                            }
                        }
                        
                        if (pointList.Count > 0)
                        {
                            result[slotName] = pointList;
                            System.Diagnostics.Debug.WriteLine($"Found {pointList.Count} points for slot {slotName}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating heat map data: {ex.Message}");
            }
            
            return result;
        }

        // 获取特定槽位的SlotInfo数据
        public SlotInfo GetSlotData(string slotName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(slotName) || _csvResult == null)
                    return null;

                // 1. 直接按原slot编号查找
                if (!_csvResult.ContainsKey(slotName))
                {
                    // 2. 如果slotName形如 "SLOT 14" 去掉前缀再试
                    if (slotName.StartsWith("SLOT ", System.StringComparison.OrdinalIgnoreCase))
                    {
                        var pureNumber = slotName.Substring(5).Trim();
                        if (_csvResult.ContainsKey(pureNumber))
                            slotName = pureNumber;
                    }
                }

                // 3. 如果仍未找到，尝试通过显示名称映射反查slot编号
                if (!_csvResult.ContainsKey(slotName) &&
                    _configService.Config?.DisplayConfig?.SlotDisplayNames != null)
                {
                    foreach (var kv in _configService.Config.DisplayConfig.SlotDisplayNames)
                    {
                        if (kv.Value == slotName && _csvResult.ContainsKey(kv.Key))
                        {
                            slotName = kv.Key;
                            break;
                        }
                    }
                }

                if (!_csvResult.ContainsKey(slotName))
                    return null;

                return _csvResult[slotName] as SlotInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting slot data for slot {slotName}: {ex.Message}");
                return null;
            }
        }

        // 获取特定槽位的热力图数据
        public List<HeatMapPoint> GetHeatMapPointsForSlot(string slotName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(slotName) || _csvResult == null)
                    return new List<HeatMapPoint>();

                // 1. 直接按原slot编号查找
                if (!_csvResult.ContainsKey(slotName))
                {
                    // 2. 如果slotName形如 "SLOT 14" 去掉前缀再试
                    if (slotName.StartsWith("SLOT ", System.StringComparison.OrdinalIgnoreCase))
                    {
                        var pureNumber = slotName.Substring(5).Trim();
                        if (_csvResult.ContainsKey(pureNumber))
                            slotName = pureNumber;
                    }
                }

                // 3. 如果仍未找到，尝试通过显示名称映射反查slot编号
                if (!_csvResult.ContainsKey(slotName) &&
                    _configService.Config?.DisplayConfig?.SlotDisplayNames != null)
                {
                    foreach (var kv in _configService.Config.DisplayConfig.SlotDisplayNames)
                    {
                        if (kv.Value == slotName && _csvResult.ContainsKey(kv.Key))
                        {
                            slotName = kv.Key;
                            break;
                        }
                    }
                }

                if (!_csvResult.ContainsKey(slotName))
                    return new List<HeatMapPoint>();

                var allData = GetHeatMapData();
                return allData != null && allData.TryGetValue(slotName, out var data) ? data : new List<HeatMapPoint>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting heat map points for slot {slotName}: {ex.Message}");
                return new List<HeatMapPoint>();
            }
        }

        // 热力图点数据结构
        public class HeatMapPoint
        {
            public double X { get; set; }
            public double Y { get; set; }
            public double Value { get; set; }
        }
        
        // 检查Slot明细行数是否一致
        private bool CheckSlotDetailConsistency()
        {
            if (_csvResult == null || _csvResult.Count <= 1)
                return true; // 只有一个Slot或没有数据，认为是一致的
                
            int firstDetailCount = -1;
            foreach (var slotObj in _csvResult.Values)
            {
                if (slotObj is SlotInfo slotInfo)
                {
                    if (firstDetailCount == -1)
                        firstDetailCount = slotInfo.SlotDetailInfo.Count;
                    else if (slotInfo.SlotDetailInfo.Count != firstDetailCount)
                        return false; // 发现行数不一致
                }
            }
            
            return true;
        }
        
        // 显示Slot选择窗口
        private List<string> ShowSlotSelectorDialog()
        {
            try
            {
                // 将Dictionary<string, object>转换为Dictionary<string, SlotInfo>
                var slotInfos = new Dictionary<string, SlotInfo>();
                foreach (var item in _csvResult)
                {
                    if (item.Value is SlotInfo slotInfo)
                    {
                        slotInfos.Add(item.Key, slotInfo);
                    }
                }
                
                var dialog = new SlotSelectionWindow(slotInfos);
                if (System.Windows.Application.Current.MainWindow != null)
                {
                    dialog.Owner = System.Windows.Application.Current.MainWindow;
                }
                
                bool? result = dialog.ShowDialog();
                if (result == true)
                {                  
                    // 返回选择的SlotNumbers
                    return dialog.SelectedSlotNumbers;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"显示Slot选择窗口失败: {ex.Message}";
                return null;
            }
        }

        // 显示配置确认窗口
        private bool ShowDisplayConfigWindow()
        {
            try
            {
                // 将Dictionary<string, object>转换为Dictionary<string, SlotInfo>
                var slotInfos = new Dictionary<string, SlotInfo>();
                foreach (var item in _csvResult)
                {
                    if (item.Value is SlotInfo slotInfo)
                    {
                        slotInfos.Add(item.Key, slotInfo);
                    }
                }
                
                // 获取现有配置
                var existingDisplayNames = _configService.Config?.DisplayConfig?.SlotDisplayNames;
                var equipmentName = _configService.Config?.DisplayConfig?.EquipmentName ?? "KLASFX200(@ 49Point)";
                var decimalPlaces = _configService.Config?.DisplayConfig?.DecimalPlaces ?? 2;
                var userInputValue = _configService.Config?.DisplayConfig?.UserInputValue ?? 1.0;
                
                var dialog = new DisplayConfigWindow(slotInfos, existingDisplayNames, equipmentName, decimalPlaces, userInputValue);
                if (System.Windows.Application.Current.MainWindow != null)
                {
                    dialog.Owner = System.Windows.Application.Current.MainWindow;
                }
                
                bool? result = dialog.ShowDialog();
                if (result == true)
                {
                    // 保存配置
                    if (_configService.Config.DisplayConfig == null)
                    {
                        _configService.Config.DisplayConfig = new DisplayConfig();
                    }
                    
                    // 更新配置
                    _configService.Config.DisplayConfig.SlotDisplayNames = dialog.SlotDisplayNames;
                    _configService.Config.DisplayConfig.EquipmentName = dialog.EquipmentName;
                    _configService.Config.DisplayConfig.DecimalPlaces = dialog.DecimalPlaces;
                    _configService.Config.DisplayConfig.UserInputValue = dialog.UserInputValue;
                    
                    // 保存配置，但不显示消息
                    _configService.SaveConfig(false);
                    
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"显示配置窗口失败: {ex.Message}";
                return false;
            }
        }

        // 允许外部注入FullResultData
        public void SetResultData(Dictionary<string, object> data, bool skipConfigDialog = true, string csvFilePath = null, string configFilePath = null)
        {
            System.Diagnostics.Debug.WriteLine($"SetResultData 被调用: data.Count={data?.Count}, csvFilePath={System.IO.Path.GetFileName(csvFilePath)}");

            _csvResult = data;

            // 如果提供了文件路径，设置文件选择状态
            if (!string.IsNullOrEmpty(csvFilePath))
            {
                var csvFile = new CsvFile { FileName = System.IO.Path.GetFileName(csvFilePath), FilePath = csvFilePath };
                if (!CsvFiles.Any(f => f.FilePath == csvFilePath))
                {
                    CsvFiles.Add(csvFile);
                }
                SelectedCsvFile = CsvFiles.FirstOrDefault(f => f.FilePath == csvFilePath);
                System.Diagnostics.Debug.WriteLine($"SetResultData: 设置CSV文件 = {SelectedCsvFile?.FileName}");
            }

            if (!string.IsNullOrEmpty(configFilePath))
            {
                var configFile = new ConfigFile { FileName = System.IO.Path.GetFileName(configFilePath), FilePath = configFilePath };
                if (!ConfigFiles.Any(f => f.FilePath == configFilePath))
                {
                    ConfigFiles.Add(configFile);
                }
                SelectedConfigFile = ConfigFiles.FirstOrDefault(f => f.FilePath == configFilePath);
                System.Diagnostics.Debug.WriteLine($"SetResultData: 设置配置文件 = {SelectedConfigFile?.FileName}");
            }

            FilterDisplayData(skipConfigDialog);

            // 确保数据状态正确
            if (data != null && data.Count > 0)
            {
                IsDataEmpty = false;
                System.Diagnostics.Debug.WriteLine($"SetResultData: IsDataEmpty = {IsDataEmpty}, data.Count = {data.Count}");

                // 更新统计信息
                UpdateStatisticsSummary();

                // 更新解析状态信息
                ParseTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                DataStatus = $"已加载 ({data.Count} 个Slot)";
            }
        }

        // 支持跳过配置弹窗的重载
        private void FilterDisplayData(bool skipConfigDialog)
        {
            StatisticsTable.Clear();
            DetailTable.Clear();
            StatisticsTable.Columns.Clear();
            DetailTable.Columns.Clear();

            string colSlot = "SlotNumber";
            string colID = "WAFER ID";
            StatisticsTable.Columns.Add(colSlot);
            StatisticsTable.Columns.Add(colID);

            DetailTable.Columns.Add(colSlot);
            DetailTable.Columns.Add(colID);

            foreach (var slotEntry in _csvResult)
            {
                if (slotEntry.Value is SlotInfo slotInfo)
                {
                    // 统计表
                    foreach (var col in slotInfo.columnStaticNames)
                    {
                        if(StatisticsTable.Columns.Contains(col))
                        {
                            continue;
                        }
                        StatisticsTable.Columns.Add(col);
                    }

                    // 明细表
                    foreach (var col in slotInfo.columnDetailNames)
                    {
                        if (DetailTable.Columns.Contains(col))
                        {
                            continue;
                        }
                        DetailTable.Columns.Add(col);
                    }
                }
            }
            foreach (var slotEntry in _csvResult)
            {
                if (slotEntry.Value is SlotInfo slotInfo)
                {
                    // 统计表
                    foreach (var row in slotInfo.SlotTotalInfo)
                    {
                        var dr = StatisticsTable.NewRow();
                        foreach (var col in slotInfo.columnStaticNames)
                            dr[col] = row[col];
                        dr[colSlot]= slotInfo.SlotNumber;
                        dr[colID] = slotInfo.WaferId;

                        StatisticsTable.Rows.Add(dr);
                    }

                    // 明细表
                    foreach (var row in slotInfo.SlotDetailInfo)
                    {
                        var dr = DetailTable.NewRow();
                        foreach (var col in slotInfo.columnDetailNames)
                            dr[col] = row[col];
                        dr[colSlot] = slotInfo.SlotNumber;
                        dr[colID] = slotInfo.WaferId;
                        DetailTable.Rows.Add(dr);
                    }
                }
            }

            OnPropertyChanged(nameof(StatisticsTable));
            OnPropertyChanged(nameof(DetailTable));

            // 更新数据状态
            bool hasData = _csvResult != null && _csvResult.Count > 0;
            if (hasData)
            {
                ParseTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                DataStatus = $"已解析 ({_csvResult.Count} 个Slot)";
            }

            // 结果表
            FilterResultDisplayData(skipConfigDialog);
        }

        // 支持跳过配置弹窗的重载
        private void FilterResultDisplayData(bool skipConfigDialog)
        {
            ResultTable.Clear();
            ResultTable.Columns.Clear();
            if (!skipConfigDialog)
            {
                if (!ShowDisplayConfigWindow())
                {
                    ErrorMessage = "用户取消了显示配置";
                    return;
                }
            }
                
            // 获取小数位数用于格式化
            int decimalPlaces = _configService.Config?.DisplayConfig?.DecimalPlaces ?? 2;
            string format = $"F{decimalPlaces}";
            
            // 列名
            int slotCount = _csvResult.Count;
            for (int i = 1; i <= slotCount + 2; i++)
                ResultTable.Columns.Add("Col" + i);
            
            // 第一行
            var row1 = ResultTable.NewRow();
            row1["Col1"] = "Measuring Equipment";
            row1["Col2"] = null;
            // 使用配置的设备名称
            row1["Col3"] = _configService.Config?.DisplayConfig?.EquipmentName ?? "KLASFX200(@ 49Point)";
            for (int i = 4; i < slotCount + 3; i++)
            {
                row1["Col" + i] = null;
            }
            ResultTable.Rows.Add(row1);

            // 第二行
            var row2 = ResultTable.NewRow();
            row2["Col1"] = "Position";
            row2["Col2"] = null;

            // 获取配置中的显示名称
            var keys = _csvResult.Keys.ToList();
            for (int i = 0; i < keys.Count; i++)
            {
                string slotNumber = keys[i];
                string displayName;
                
                // 尝试从配置中获取显示名称
                if (_configService.Config?.DisplayConfig?.SlotDisplayNames != null && 
                    _configService.Config.DisplayConfig.SlotDisplayNames.ContainsKey(slotNumber))
                {
                    displayName = _configService.Config.DisplayConfig.SlotDisplayNames[slotNumber];
                }
                else
                {
                    displayName = "SLOT " + slotNumber;
                }
                
                row2["Col" + (i + 3)] = displayName;
            }
            ResultTable.Rows.Add(row2);

            // 第三行
            var row3 = ResultTable.NewRow();
            row3["Col1"] = "Thickness";
            row3["Col2"] = "THK[A]";

            var values = _csvResult.Values.ToList();
            for (int i = 0; i < values.Count; i++)
            {
                var data = values[i];
                if (data is SlotInfo slotInfo)
                {
                    var totalList = slotInfo.SlotTotalInfo;
                    foreach (var item in totalList)
                    {
                        if (item["RESULT TYPE"].Equals("MEAN"))
                        {
                            // 处理列名中的@字符
                            if (item.ContainsKey("1st Thickness"))
                            {
                                row3["Col" + (i + 3)] = item["1st Thickness"];
                            }
                        }
                    }
                }
            }
            ResultTable.Rows.Add(row3);

            // 第四行
            var row4 = ResultTable.NewRow();
            row4["Col1"] = "Thickness";
            row4["Col2"] = "Range[A]";

            for (int i = 0; i < values.Count; i++)
            {
                var data = values[i];
                if (data is SlotInfo slotInfo)
                {
                    var totalList = slotInfo.SlotTotalInfo;
                    foreach (var item in totalList)
                    {
                        if (item["RESULT TYPE"].Equals("RANGE"))
                        {
                            if (item.ContainsKey("1st Thickness"))
                            {
                                row4["Col" + (i + 3)] = item["1st Thickness"];
                            }
                        }
                    }
                }
            }
            ResultTable.Rows.Add(row4);

            // 第五行
            var row5 = ResultTable.NewRow();
            row5["Col1"] = "Thickness";
            row5["Col2"] = "STDDEV[A]";

            for (int i = 0; i < values.Count; i++)
            {
                var data = values[i];
                if (data is SlotInfo slotInfo)
                {
                    var totalList = slotInfo.SlotTotalInfo;
                    foreach (var item in totalList)
                    {
                        if (item["RESULT TYPE"].Equals("STDDEV"))
                        {
                            if (item.ContainsKey("1st Thickness"))
                            {
                                row5["Col" + (i + 3)] = item["1st Thickness"];
                            }
                        }
                    }
                }
            }
            ResultTable.Rows.Add(row5);

            // 第6行
            var row6 = ResultTable.NewRow();
            row6["Col1"] = "Thickness";
            row6["Col2"] = "Unif.[+/-%]";

            for (int i = 0; i < values.Count; i++)
            {
                double unif = Convert.ToDouble(row4["Col" + (i + 3)].ToString()) / Convert.ToDouble(row3["Col" + (i + 3)].ToString()) / 2 * 100;
                row6["Col" + (i + 3)] = unif.ToString(format) + "%";
            }
            ResultTable.Rows.Add(row6);

            // 第7行
            var row7 = ResultTable.NewRow();
            row7["Col1"] = "Thickness";
            row7["Col2"] = "RI";
            var row8 = ResultTable.NewRow();
            row8["Col1"] = "Thickness";
            row8["Col2"] = "GOF";

            for (int i = 0; i < values.Count; i++)
            {
                var data = values[i];
                if (data is SlotInfo slotInfo)
                {
                    var totalList = slotInfo.SlotTotalInfo;
                    foreach (var item in totalList)
                    {
                        if (item["RESULT TYPE"].Equals("MEAN"))
                        {
                            // 处理RI列名，可能包含@符号
                            string riKey = slotInfo.columnStaticNames.FirstOrDefault(k => k.Contains("RI"));
                            if (!string.IsNullOrEmpty(riKey) && item.ContainsKey(riKey))
                            {
                                row7["Col" + (i + 3)] = item[riKey];
                            }

                            // 处理GOF列名
                            if (item.ContainsKey("GOF"))
                            {
                                row8["Col" + (i + 3)] = item["GOF"];
                            }
                        }
                    }
                }
            }
            ResultTable.Rows.Add(row7);
            ResultTable.Rows.Add(row8);

            // 第9行
            var row9 = ResultTable.NewRow();
            row9["Col1"] = "Thickness";
            row9["Col2"] = "WTW Unif";

            // 计算所有slot的均值
            double allMean = 0;

            for (int i = 0; i < values.Count; i++)
            {
                allMean += Convert.ToDouble(row3["Col" + (i + 3)].ToString());
            }
            allMean /= values.Count;

            for (int i = 0; i < values.Count; i++)
            {
                // THK的最大值
                double maxTHK = 0;
                // THK的最小值
                double minTHK = 0;

                var data = values[i];
                if (data is SlotInfo slotInfo)
                {
                    var totalList = slotInfo.SlotTotalInfo;
                    foreach (var item in totalList)
                    {
                        if (item["RESULT TYPE"].Equals("MAX"))
                        {
                            if (item.ContainsKey("1st Thickness"))
                            {
                                maxTHK = Convert.ToDouble(item["1st Thickness"].ToString());
                            }
                        }

                        if(item["RESULT TYPE"].Equals("MIN"))
                        {
                            if (item.ContainsKey("1st Thickness"))
                            {
                                minTHK = Convert.ToDouble(item["1st Thickness"].ToString());
                            }
                        }
                    }
                }

                double val = (maxTHK - minTHK) / allMean / 2 * 100;
                row9["Col" + (i + 3)] = val.ToString(format) + "%";
            }

            ResultTable.Rows.Add(row9);

            // 第10行
            var row10 = ResultTable.NewRow();
            row10["Col1"] = "Thickness";
            row10["Col2"] = "GPC[A/C]";
            
            // 从配置中获取用户输入的值
            double userInput = 1; // 默认值
            if (_configService.Config?.DisplayConfig != null)
            {
                userInput = _configService.Config.DisplayConfig.UserInputValue;
            }
            
            for (int i = 0; i < keys.Count; i++)
            {
                double value = Convert.ToDouble(row3["Col" + (i + 3)].ToString()) / userInput;
                row10["Col" + (i + 3)] = value.ToString(format);
            }
            ResultTable.Rows.Add(row10);

            // 第11行
            var row11 = ResultTable.NewRow();
            row11["Col1"] = "Thickness";
            row11["Col2"] = "α/x[%]";
            
            for (int i = 0; i < values.Count; i++)
            {
                double sigma = Convert.ToDouble(row5["Col" + (i + 3)].ToString()) / Convert.ToDouble(row3["Col" + (i + 3)].ToString()) * 100;
                row11["Col" + (i + 3)] = sigma.ToString(format) + "%";
            }
            ResultTable.Rows.Add(row11);

            // 第12行
            var row12 = ResultTable.NewRow();
            row12["Col1"] = "THK MAP";
            row12["Col2"] = null;

            for (int i = 0; i < values.Count; i++)
            {
                row12["Col" + (i + 3)] = "待插入等高线热力图";
            }
            ResultTable.Rows.Add(row12);

            // 设置数据状态
            IsDataEmpty = false;
            System.Diagnostics.Debug.WriteLine($"FilterResultDisplayData完成: IsDataEmpty = {IsDataEmpty}");

            // 通知UI更新
            OnPropertyChanged(nameof(ResultTable));
            OnPropertyChanged(nameof(ResultTable.DefaultView));

            // 确保在下一个UI循环中完成更新
            System.Threading.Tasks.Task.Delay(10).ContinueWith(t =>
            {
                try
                {
                    if (System.Windows.Application.Current != null && !System.Windows.Application.Current.Dispatcher.HasShutdownStarted)
                    {
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            OnPropertyChanged(nameof(ResultTable));
                            OnPropertyChanged(nameof(ResultTable.DefaultView));
                        });
                    }
                }
                catch { /* 忽略异常 */ }
            });
        }
    }

    // 辅助类：Slot统计信息
    public class SlotStatistics
    {
        public string Slot { get; set; }
        public int Count { get; set; }
        public double Average { get; set; }
        public double Min { get; set; }
        public double Max { get; set; }
    }
}