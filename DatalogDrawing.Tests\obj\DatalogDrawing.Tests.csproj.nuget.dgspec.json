{"format": 1, "restore": {"E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\DatalogDrawing.Tests.csproj": {}}, "projects": {"E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj": {"restore": {"projectUniqueName": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj", "projectName": "DatalogDrawing", "projectPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj", "frameworks": {"net451": {"projectReferences": {}}}}, "frameworks": {"net451": {}}}, "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\DatalogDrawing.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\DatalogDrawing.Tests.csproj", "projectName": "DatalogDrawing.Tests", "projectPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\DatalogDrawing.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Software\\Vs2022Share\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net451"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net451": {"targetAlias": "net451", "projectReferences": {"E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj": {"projectPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net451": {"targetAlias": "net451", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[2.2.10, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[2.2.10, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[3.2.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.200\\RuntimeIdentifierGraph.json"}}}}}