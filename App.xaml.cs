using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using DatalogDrawing.Views;  // 包含主窗口引用
using DatalogDrawing.ViewModels;  // 包含视图模型引用
using DatalogDrawing.Services;
using DatalogDrawing.Interfaces;

namespace DatalogDrawing
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 初始化数据库服务
            DatabaseService.Instance.InitializeDatabase();

            // 创建主窗口
            var parser = new FilmThicknessParser();
            var mainWindow = new MainWindow(parser);
            mainWindow.Show();
        }
    }
}
