using System.Windows;
using DatalogDrawing.ViewModels;

namespace DatalogDrawing.Views
{
    public partial class FilmThicknessPreviewWindow : Window
    {
        public FilmThicknessPreviewWindow(FilmThicknessViewModel vm)
        {
            InitializeComponent();
            vm.LeftPanelVisibility = Visibility.Collapsed;
            vm.LeftPanelWidth = new GridLength(0);
            DataContext = vm;
        }

        public FilmThicknessPreviewWindow() : this(new FilmThicknessViewModel(new ConfigService(), new Services.FilmThicknessParser(), b => { }))
        {
        }
    }
} 