using System.Collections.Generic;

namespace DatalogDrawing.Models
{
    public class CsvRow
    {
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        public object this[string columnName]
        {
            get => Data.TryGetValue(columnName, out var value) ? value : null;
            set => Data[columnName] = value;
        }

        public double DeltaDSIZE
        {
            get => Data.TryGetValue(nameof(DeltaDSIZE), out var value) && value is double d ? d : 0.0;
            set => Data[nameof(DeltaDSIZE)] = value;
        }
    }
}