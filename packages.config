﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.5.1" targetFramework="net451" />
  <package id="LiveCharts" version="0.9.7" targetFramework="net462" />
  <package id="LiveCharts.Wpf" version="0.9.7" targetFramework="net462" />
  <package id="MathNet.Numerics" version="4.15.0" targetFramework="net451" />
  <package id="MIConvexHull" version="1.1.19.1019" targetFramework="net451" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net451" />
  <package id="OxyPlot.Core" version="2.1.2" targetFramework="net462" />
  <package id="OxyPlot.Wpf" version="2.1.2" targetFramework="net462" />
  <package id="OxyPlot.Wpf.Shared" version="2.1.2" targetFramework="net462" />
  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="1.0.115.5" targetFramework="net451" />
  <package id="System.Data.SQLite" version="1.0.115.5" targetFramework="net451" />
  <package id="System.Data.SQLite.Core" version="1.0.115.5" targetFramework="net451" />
  <package id="System.Data.SQLite.EF6" version="1.0.115.5" targetFramework="net451" />
  <package id="System.Data.SQLite.Linq" version="1.0.115.5" targetFramework="net451" />
</packages>