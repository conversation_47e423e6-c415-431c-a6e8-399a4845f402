<Window x:Class="DatalogDrawing.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:DatalogDrawing.Views"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="数据分析平台" Height="800" Width="1200">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 导航菜单 -->
        <Menu Grid.Row="0">
            <MenuItem Header="功能">
                <MenuItem Header="膜厚分析" Command="{Binding ShowFilmThicknessView}"/>
                <MenuItem Header="PA数据分析" Command="{Binding ShowPADataView}"/>
                <MenuItem Header="Recipe分析" Command="{Binding ShowRecipeView}"/>
            </MenuItem>
        </Menu>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="1">
             <Button Content="导入CSV" Command="{Binding FilmThicknessVM.ImportCsvCommand}" ToolTip="导入单个CSV文件"/>
             <Button Content="解析" Command="{Binding FilmThicknessVM.ParseCommand}" ToolTip="解析选中的CSV文件"/>
             <Separator/>
             <Button Content="选择配置" Command="{Binding FilmThicknessVM.SelectConfigCommand}" ToolTip="选择配置文件"/>
        </ToolBar>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="2">
            <TabItem Header="📊 膜厚分析">
                <views:FilmThicknessView DataContext="{Binding FilmThicknessVM}" />
            </TabItem>
            <TabItem Header="🔬 颗粒分析 (PA数据)">
                <views:PADataView DataContext="{Binding PADataVM}" />
            </TabItem>
            <TabItem Header="📋 Recipe分析">
                <views:RecipeView DataContext="{Binding RecipeVM}" />
            </TabItem>
            <TabItem Header="📈 汇总对比">
                <views:SummaryView DataContext="{Binding SummaryVM}" />
            </TabItem>
        </TabControl>

        <!-- 状态栏 - 固定在窗口底部 -->
        <StatusBar Grid.Row="3" Background="LightGray" Height="25">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="状态: " FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding StatusMessage}" />
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Center">
                <StackPanel Orientation="Horizontal">
                    <ProgressBar Width="100" Height="16"
                                IsIndeterminate="{Binding IsBusy}"
                                Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Margin="0,0,10,0"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="数据分析平台 v1.0" FontStyle="Italic" Foreground="DarkGray"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
