using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Windows;
using System.Windows.Input;
using DatalogDrawing.Models;
using DatalogDrawing.Services;
using DatalogDrawing.Helpers;
using OxyPlot;
using OxyPlot.Series;
using OxyPlot.Axes;
using System.Linq;
using Newtonsoft.Json;

namespace DatalogDrawing.ViewModels
{
    public class PADataViewModel : ViewModelBase
    {
        private readonly PAParserService _parserService;
        private PlotModel _prePlotModel;
        private PlotModel _afterPlotModel;
        private PlotModel _addPlotModel;
        private string _preFilePath;
        private string _afterFilePath;
        private string _configFilePath;

        public PADataViewModel()
        {
            _parserService = new PAParserService();
            InitializeCommands();
            InitializePlotModels();
        }

        // 属性
        public PlotModel PrePlotModel
        {
            get => _prePlotModel;
            set => SetProperty(ref _prePlotModel, value);
        }

        public PlotModel AfterPlotModel
        {
            get => _afterPlotModel;
            set => SetProperty(ref _afterPlotModel, value);
        }

        public PlotModel AddPlotModel
        {
            get => _addPlotModel;
            set => SetProperty(ref _addPlotModel, value);
        }

        public string PreFilePath
        {
            get => _preFilePath;
            set
            {
                if (SetProperty(ref _preFilePath, value))
                {
                    ((RelayCommand)ParseCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        public string AfterFilePath
        {
            get => _afterFilePath;
            set
            {
                if (SetProperty(ref _afterFilePath, value))
                {
                    ((RelayCommand)ParseCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        public string ConfigFilePath
        {
            get => _configFilePath;
            set => SetProperty(ref _configFilePath, value);
        }

        // 数据属性
        private List<PAData> _preData = new List<PAData>();
        public List<PAData> PreData
        {
            get => _preData;
            set => SetProperty(ref _preData, value);
        }

        private List<PAData> _afterData = new List<PAData>();
        public List<PAData> AfterData
        {
            get => _afterData;
            set => SetProperty(ref _afterData, value);
        }

        private List<PAData> _addData = new List<PAData>();
        public List<PAData> AddData
        {
            get => _addData;
            set => SetProperty(ref _addData, value);
        }

        // 统计信息属性
        private int _preDataCount;
        public int PreDataCount
        {
            get => _preDataCount;
            set => SetProperty(ref _preDataCount, value);
        }

        private double _preDataAvgSize;
        public double PreDataAvgSize
        {
            get => _preDataAvgSize;
            set => SetProperty(ref _preDataAvgSize, value);
        }

        private double _preDataMaxSize;
        public double PreDataMaxSize
        {
            get => _preDataMaxSize;
            set => SetProperty(ref _preDataMaxSize, value);
        }

        private double _preDataMinSize;
        public double PreDataMinSize
        {
            get => _preDataMinSize;
            set => SetProperty(ref _preDataMinSize, value);
        }

        private int _afterDataCount;
        public int AfterDataCount
        {
            get => _afterDataCount;
            set => SetProperty(ref _afterDataCount, value);
        }

        private double _afterDataAvgSize;
        public double AfterDataAvgSize
        {
            get => _afterDataAvgSize;
            set => SetProperty(ref _afterDataAvgSize, value);
        }

        private double _afterDataMaxSize;
        public double AfterDataMaxSize
        {
            get => _afterDataMaxSize;
            set => SetProperty(ref _afterDataMaxSize, value);
        }

        private double _afterDataMinSize;
        public double AfterDataMinSize
        {
            get => _afterDataMinSize;
            set => SetProperty(ref _afterDataMinSize, value);
        }

        private int _addDataCount;
        public int AddDataCount
        {
            get => _addDataCount;
            set => SetProperty(ref _addDataCount, value);
        }

        private double _addDataAvgSize;
        public double AddDataAvgSize
        {
            get => _addDataAvgSize;
            set => SetProperty(ref _addDataAvgSize, value);
        }

        private double _addDataMaxSize;
        public double AddDataMaxSize
        {
            get => _addDataMaxSize;
            set => SetProperty(ref _addDataMaxSize, value);
        }

        private double _addDataMinSize;
        public double AddDataMinSize
        {
            get => _addDataMinSize;
            set => SetProperty(ref _addDataMinSize, value);
        }

        // 命令
        public ICommand SelectPreFileCommand { get; private set; }
        public ICommand SelectAfterFileCommand { get; private set; }
        public ICommand SelectConfigFileCommand { get; private set; }
        public ICommand ParseCommand { get; private set; }
        public ICommand ExportResultCommand { get; private set; }
        public ICommand ClearDataCommand { get; private set; }

        private void InitializeCommands()
        {
            SelectPreFileCommand = new RelayCommand(SelectPreFile);
            SelectAfterFileCommand = new RelayCommand(SelectAfterFile);
            SelectConfigFileCommand = new RelayCommand(SelectConfigFile);
            ParseCommand = new RelayCommand(ExecuteParse, CanExecuteParse);
            ExportResultCommand = new RelayCommand(ExportResult, CanExportResult);
            ClearDataCommand = new RelayCommand(ClearData);

            // 测试用：自动设置文件路径
            TestLoadFiles();
        }

        private void TestLoadFiles()
        {
            try
            {
                // 获取应用程序的基础目录
                var baseDir = System.AppDomain.CurrentDomain.BaseDirectory;
                var preFile = System.IO.Path.Combine(baseDir, "1-FUR-BARE_0.025_Pre_WNONE_s16_2025-04-03-15-43-31.001");
                var afterFile = System.IO.Path.Combine(baseDir, "1-FUR-SiN_100A_0.1-1_Pre_WNONE_s16_2025-04-07-09-46-30.001");
                var configFile = System.IO.Path.Combine(baseDir, "PAconfig.json");

                // 如果基础目录没有文件，尝试相对路径
                if (!System.IO.File.Exists(preFile))
                {
                    preFile = @"bin\Debug\1-FUR-BARE_0.025_Pre_WNONE_s16_2025-04-03-15-43-31.001";
                }

                if (!System.IO.File.Exists(afterFile))
                {
                    afterFile = @"bin\Debug\1-FUR-SiN_100A_0.1-1_Pre_WNONE_s16_2025-04-07-09-46-30.001";
                }

                if (!System.IO.File.Exists(configFile))
                {
                    configFile = @"bin\Debug\PAconfig.json";
                }

                if (System.IO.File.Exists(preFile))
                {
                    PreFilePath = preFile;
                    System.Diagnostics.Debug.WriteLine($"TestLoadFiles: Pre文件已设置: {preFile}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"TestLoadFiles: Pre文件不存在: {preFile}");
                }

                if (System.IO.File.Exists(afterFile))
                {
                    AfterFilePath = afterFile;
                    System.Diagnostics.Debug.WriteLine($"TestLoadFiles: After文件已设置: {afterFile}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"TestLoadFiles: After文件不存在: {afterFile}");
                }

                if (System.IO.File.Exists(configFile))
                {
                    ConfigFilePath = configFile;
                    System.Diagnostics.Debug.WriteLine($"TestLoadFiles: Config文件已设置: {afterFile}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"TestLoadFiles: Config文件不存在: {afterFile}");
                }

                

                // 强制刷新命令状态
                ((RelayCommand)ParseCommand).RaiseCanExecuteChanged();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TestLoadFiles错误: {ex.Message}");
            }
        }

        private void InitializePlotModels()
        {
            PrePlotModel = CreateScatterPlotModel("Pre Data");
            AfterPlotModel = CreateScatterPlotModel("After Data");
            AddPlotModel = CreateScatterPlotModel("Add Data");
        }

        private PlotModel CreateScatterPlotModel(string title)
        {
            var plotModel = new PlotModel
            {
                Title = title,
                Background = OxyColors.Black, // 设置黑色背景
                PlotAreaBorderColor = OxyColors.White, // 白色边框在黑色背景上更明显
                PlotAreaBorderThickness = new OxyThickness(1),
                IsLegendVisible = false, // 隐藏图例避免显示额外文本
                PlotMargins = new OxyThickness(60, 40, 20, 60), // 设置图表边距
                TitleFontSize = 14,
                TitleColor = OxyColors.White // 白色标题在黑色背景上更明显
            };

            // 添加默认坐标轴
            plotModel.Axes.Add(new OxyPlot.Axes.LinearAxis
            {
                Position = OxyPlot.Axes.AxisPosition.Bottom,
                Title = "X Position (μm)",
                TitleColor = OxyColors.White, // 白色轴标题
                TextColor = OxyColors.White, // 白色轴文字
                TicklineColor = OxyColors.White, // 白色刻度线
                AxislineColor = OxyColors.White, // 白色轴线
                MajorGridlineStyle = LineStyle.Solid,
                MajorGridlineColor = OxyColors.Gray, // 深灰色网格线在黑色背景上更合适
                MinorGridlineStyle = LineStyle.Dot,
                MinorGridlineColor = OxyColors.DarkGray,
                FontSize = 12, // 设置字体大小
                TitleFontSize = 14 // 设置标题字体大小
            });
            plotModel.Axes.Add(new OxyPlot.Axes.LinearAxis
            {
                Position = OxyPlot.Axes.AxisPosition.Left,
                Title = "Y Position (μm)",
                TitleColor = OxyColors.White, // 白色轴标题
                TextColor = OxyColors.White, // 白色轴文字
                TicklineColor = OxyColors.White, // 白色刻度线
                AxislineColor = OxyColors.White, // 白色轴线
                MajorGridlineStyle = LineStyle.Solid,
                MajorGridlineColor = OxyColors.Gray, // 深灰色网格线在黑色背景上更合适
                MinorGridlineStyle = LineStyle.Dot,
                MinorGridlineColor = OxyColors.DarkGray,
                FontSize = 12, // 设置字体大小
                TitleFontSize = 14 // 设置标题字体大小
            });

            return plotModel;
        }

        private void SelectPreFile()
        {
            var filePath = MessageBoxService.ShowOpenFileDialog("选择Pre文件", "所有文件 (*.*)|*.*|颗粒数据文件 (*.001)|*.001|CSV文件 (*.csv)|*.csv");
            if (!string.IsNullOrEmpty(filePath))
            {
                PreFilePath = filePath;
            }
        }

        private void SelectAfterFile()
        {
            var filePath = MessageBoxService.ShowOpenFileDialog("选择After文件", "所有文件 (*.*)|*.*|颗粒数据文件 (*.001)|*.001|CSV文件 (*.csv)|*.csv");
            if (!string.IsNullOrEmpty(filePath))
            {
                AfterFilePath = filePath;
            }
        }

        private void SelectConfigFile()
        {
            var filePath = MessageBoxService.ShowOpenFileDialog("选择配置文件", "JSON文件 (*.json)|*.json");
            if (!string.IsNullOrEmpty(filePath))
            {
                ConfigFilePath = filePath;
            }
        }

        private bool CanExecuteParse()
        {
            var canExecute = !string.IsNullOrEmpty(PreFilePath) &&
                           !string.IsNullOrEmpty(AfterFilePath);

            return canExecute;
            // 配置文件是可选的，如果没有提供会使用默认配置
        }

        private void ExecuteParse()
        {
            try
            {
                var result = _parserService.ParseDualFiles(PreFilePath, AfterFilePath, ConfigFilePath);

                // 生成主BatchId
                var mainBatchId = Guid.NewGuid().ToString();

                // 处理Pre数据
                if (result.TryGetValue("PreData", out var preDataObj) && preDataObj is List<PAData> preData)
                {
                    PreData = preData;
                    System.Diagnostics.Debug.WriteLine($"ExecuteParse: 处理 {preData.Count} 个Pre数据点");
                }

                // 处理After数据
                if (result.TryGetValue("AfterData", out var afterDataObj) && afterDataObj is List<PAData> afterData)
                {
                    AfterData = afterData;
                    System.Diagnostics.Debug.WriteLine($"ExecuteParse: 处理 {afterData.Count} 个After数据点");
                }

                // 处理Add数据（支持新旧键名）
                List<PAData> addData = null;
                if (result.TryGetValue("AddData", out var addDataObj) && addDataObj is List<PAData> newAddData)
                {
                    addData = newAddData;
                }
                else if (result.TryGetValue("DeltaData", out var deltaDataObj) && deltaDataObj is List<PAData> oldDeltaData)
                {
                    addData = oldDeltaData; // 向后兼容旧的DeltaData
                }

                if (addData != null)
                {
                    AddData = addData;

                    if (addData.Count > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"ExecuteParse: 处理 {addData.Count} 个Add数据点");
                        System.Diagnostics.Debug.WriteLine($"ExecuteParse: 第一个Add点: X={addData[0].XREL}, Y={addData[0].YREL}, Size={addData[0].DSIZE}");
                    }
                }

                // 所有数据处理完成后，统一刷新图表坐标轴范围
                RefreshAllPlotModels();

                // 保存会话信息和颗粒数据到 PAData 表
                try
                {
                    var sessionData = new Dictionary<string, object>
                    {
                        ["ParsedFilePath"] = $"{PreFilePath} & {AfterFilePath}",
                        ["FullResultData"] = new Dictionary<string, object>
                        {
                            ["MainBatchId"] = mainBatchId,
                            ["PreDataCount"] = PreData?.Count ?? 0,
                            ["AfterDataCount"] = AfterData?.Count ?? 0,
                            ["AddDataCount"] = AddData?.Count ?? 0,
                            ["PreFilePath"] = PreFilePath,
                            ["AfterFilePath"] = AfterFilePath,
                            ["ConfigFilePath"] = ConfigFilePath,
                            // 将颗粒数据作为JSON保存
                            ["PreData"] = PreData ?? new List<PAData>(),
                            ["AfterData"] = AfterData ?? new List<PAData>(),
                            ["AddData"] = AddData ?? new List<PAData>()
                        }
                    };
                    DatabaseService.Instance.InsertFullResult("PAData", sessionData);
                    System.Diagnostics.Debug.WriteLine($"颗粒数据会话已保存到数据库，MainBatchId: {mainBatchId}");
                    System.Diagnostics.Debug.WriteLine($"保存的数据: Pre={PreData?.Count ?? 0}, After={AfterData?.Count ?? 0}, Add={AddData?.Count ?? 0}");
                }
                catch (Exception sessionEx)
                {
                    System.Diagnostics.Debug.WriteLine($"保存颗粒数据会话失败: {sessionEx.Message}");
                }

                // 更新统计信息
                UpdateStatistics();

                MessageBoxService.ShowMessage("解析完成并成功保存到数据库");
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"解析失败: {ex.Message}");
            }
        }

        private bool CanExportResult()
        {
            return AddData?.Count > 0;
        }

        private void ExportResult()
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    Title = "导出PA分析结果"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var lines = new List<string>
                    {
                        "DEFECTID,XREL,YREL,XINDEX,YINDEX,XSIZE,YSIZE,DEFECTAREA,DSIZE,CLASSNUMBER,TEST,CLUSTERNUMBER,ROUGHBINNUMBER,FINEBINNUMBER,REVIEWSAMPLE,IMAGECOUNT,IMAGELIST,DeltaDSIZE,BatchId,Timestamp,WaferId,SlotNumber,FilePath"
                    };

                    foreach (var data in AddData)
                    {
                        lines.Add($"{data.DEFECTID},{data.XREL},{data.YREL},{data.XINDEX},{data.YINDEX},{data.XSIZE},{data.YSIZE},{data.DEFECTAREA},{data.DSIZE},{data.CLASSNUMBER},{data.TEST},{data.CLUSTERNUMBER},{data.ROUGHBINNUMBER},{data.FINEBINNUMBER},{data.REVIEWSAMPLE},{data.IMAGECOUNT},{data.IMAGELIST},{data.DeltaDSIZE},{data.BatchId},{data.Timestamp},{data.WaferId},{data.SlotNumber},{data.FilePath}");
                    }

                    System.IO.File.WriteAllLines(saveFileDialog.FileName, lines);
                    MessageBoxService.ShowMessage("导出成功!");
                }
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"导出失败: {ex.Message}");
            }
        }

        private void ClearData()
        {
            PreData = new List<PAData>();
            AfterData = new List<PAData>();
            AddData = new List<PAData>();

            InitializePlotModels();
            UpdateStatistics();

            MessageBoxService.ShowMessage("数据已清除");
        }

        public void UpdateStatistics()
        {
            // Pre数据统计
            if (PreData?.Count > 0)
            {
                PreDataCount = PreData.Count;
                PreDataAvgSize = PreData.Average(p => p.DSIZE);
                PreDataMaxSize = PreData.Max(p => p.DSIZE);
                PreDataMinSize = PreData.Min(p => p.DSIZE);
            }
            else
            {
                PreDataCount = 0;
                PreDataAvgSize = 0;
                PreDataMaxSize = 0;
                PreDataMinSize = 0;
            }

            // After数据统计
            if (AfterData?.Count > 0)
            {
                AfterDataCount = AfterData.Count;
                AfterDataAvgSize = AfterData.Average(p => p.DSIZE);
                AfterDataMaxSize = AfterData.Max(p => p.DSIZE);
                AfterDataMinSize = AfterData.Min(p => p.DSIZE);
            }
            else
            {
                AfterDataCount = 0;
                AfterDataAvgSize = 0;
                AfterDataMaxSize = 0;
                AfterDataMinSize = 0;
            }

            // Add数据统计
            if (AddData?.Count > 0)
            {
                AddDataCount = AddData.Count;
                AddDataAvgSize = AddData.Average(p => p.DeltaDSIZE);
                AddDataMaxSize = AddData.Max(p => p.DeltaDSIZE);
                AddDataMinSize = AddData.Min(p => p.DeltaDSIZE);
            }
            else
            {
                AddDataCount = 0;
                AddDataAvgSize = 0;
                AddDataMaxSize = 0;
                AddDataMinSize = 0;
            }
        }

        public void UpdatePlotModel(PlotModel plotModel, List<PAData> data, string title)
        {
            System.Diagnostics.Debug.WriteLine($"UpdatePlotModel: 更新 {title} 图表，数据点数量: {data.Count}");

            plotModel.Series.Clear();
            plotModel.Title = title;

            // 计算统一的坐标轴范围
            var axisRange = CalculateUnifiedAxisRange();
            var xMin = axisRange.XMin;
            var xMax = axisRange.XMax;
            var yMin = axisRange.YMin;
            var yMax = axisRange.YMax;

            // 设置坐标轴
            plotModel.Axes.Clear();

            var xAxis = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Title = "X相对位置",
                Minimum = xMin,
                Maximum = xMax,
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot,
                MajorGridlineColor = OxyColors.LightGray,
                MinorGridlineColor = OxyColors.LightGray
            };

            var yAxis = new LinearAxis
            {
                Position = AxisPosition.Left,
                Title = "Y相对位置",
                Minimum = yMin,
                Maximum = yMax,
                MajorGridlineStyle = LineStyle.Solid,
                MinorGridlineStyle = LineStyle.Dot,
                MajorGridlineColor = OxyColors.LightGray,
                MinorGridlineColor = OxyColors.LightGray
            };

            plotModel.Axes.Add(xAxis);
            plotModel.Axes.Add(yAxis);

            // 从配置文件加载颜色映射
            var colorMappings = LoadParticleColorMappings();

            // 按颗粒大小分组，每个大小范围使用配置文件中对应的颜色
            var groupedData = GroupDataBySize(data, colorMappings);

            var totalGroupedPoints = groupedData.Sum(g => g.Data.Count);
            System.Diagnostics.Debug.WriteLine($"UpdatePlotModel: {title} 总点数: {data.Count}, 分组结果: {groupedData.Count} 个组, 分组后总点数: {totalGroupedPoints}");

            // 验证数据完整性
            if (totalGroupedPoints != data.Count)
            {
                System.Diagnostics.Debug.WriteLine($"警告: 数据丢失! 原始数据 {data.Count} 个, 分组后 {totalGroupedPoints} 个");
            }

            // 为每个颜色组创建一个系列，但不在图例中显示分组信息
            int totalPlottedPoints = 0;
            foreach (var group in groupedData)
            {
                var scatterSeries = new ScatterSeries
                {
                    MarkerType = MarkerType.Circle,
                    MarkerSize = 4,
                    MarkerFill = group.OxyColor,
                    MarkerStroke = group.OxyColor,
                    Title = null, // 不显示在图例中，避免图例过于复杂
                    TrackerFormatString = "{0}\nX: {2:F2}\nY: {4:F2}\nSize: {6:F3}μm" // 自定义工具提示
                };

                foreach (var item in group.Data)
                {
                    // 使用XREL和YREL作为坐标，DSIZE作为标记大小的参考
                    var markerSize = Math.Max(3, Math.Min(10, item.DSIZE * 20)); // 根据颗粒大小调整标记大小
                    scatterSeries.Points.Add(new ScatterPoint(item.XREL, item.YREL, markerSize, item.DSIZE));
                    totalPlottedPoints++;
                }

                plotModel.Series.Add(scatterSeries);
                System.Diagnostics.Debug.WriteLine($"添加系列: {group.SizeRange}, 颜色: {group.OxyColor}, 点数: {group.Data.Count}");
            }

            System.Diagnostics.Debug.WriteLine($"UpdatePlotModel: {title} 最终绘制点数: {totalPlottedPoints}");

            // 添加一个总体的图例项
            if (data.Count > 0)
            {
                plotModel.Title = $"{title} ({data.Count} particles)";
            }

            plotModel.InvalidatePlot(true);
        }

        // 从PAconfig.json加载颗粒颜色映射
        private List<ParticleColorMapping> LoadParticleColorMappings()
        {
            try
            {
                // 尝试从PAconfig.json加载
                var configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PAconfig.json");
                if (System.IO.File.Exists(configPath))
                {
                    var json = System.IO.File.ReadAllText(configPath);
                    var config = JsonConvert.DeserializeObject<PAConfig>(json);
                    if (config?.DisplayConfig?.ParticleColorMapping?.Count > 0)
                    {
                        // 转换PAconfig格式到标准格式
                        var mappings = config.DisplayConfig.ParticleColorMapping.Select(m => new ParticleColorMapping
                        {
                            MinSize = m.MinSize,
                            MaxSize = m.MaxSize,
                            Color = m.Color,
                            Name = m.Name
                        }).ToList();

                        System.Diagnostics.Debug.WriteLine($"从PAconfig.json加载了 {mappings.Count} 个颜色映射");
                        return mappings;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载PAconfig.json失败: {ex.Message}");
            }

            // 如果配置文件不存在或加载失败，使用默认配置
            System.Diagnostics.Debug.WriteLine("使用默认颗粒颜色映射");
            return GetDefaultParticleColorMappings();
        }

        // 获取默认颗粒颜色映射（与PAconfig.json保持一致）
        private List<ParticleColorMapping> GetDefaultParticleColorMappings()
        {
            return new List<ParticleColorMapping>
            {
                new ParticleColorMapping { MinSize = 0.0, MaxSize = 0.03, Color = "#FFFFFF", Name = "0.00-0.03μm" },
                new ParticleColorMapping { MinSize = 0.03, MaxSize = 0.05, Color = "#ADD8E6", Name = "0.03-0.05μm" },
                new ParticleColorMapping { MinSize = 0.05, MaxSize = 0.08, Color = "#00008B", Name = "0.05-0.08μm" },
                new ParticleColorMapping { MinSize = 0.08, MaxSize = 0.12, Color = "#FFFF00", Name = "0.08-0.12μm" },
                new ParticleColorMapping { MinSize = 0.12, MaxSize = 0.2, Color = "#90EE90", Name = "0.12-0.20μm" },
                new ParticleColorMapping { MinSize = 0.2, MaxSize = 0.4, Color = "#00FFFF", Name = "0.20-0.40μm" },
                new ParticleColorMapping { MinSize = 0.4, MaxSize = 1.0, Color = "#800080", Name = "0.40-1.00μm" },
                new ParticleColorMapping { MinSize = 1.0, MaxSize = 999.0, Color = "#FF0000", Name = "≥1.00μm" }
            };
        }

        // 按颗粒大小分组数据
        private List<ParticleGroup> GroupDataBySize(List<PAData> data, List<ParticleColorMapping> colorMappings)
        {
            var groups = new List<ParticleGroup>();
            var processedData = new HashSet<PAData>(); // 跟踪已处理的数据点

            for (int i = 0; i < colorMappings.Count; i++)
            {
                var mapping = colorMappings[i];
                List<PAData> groupData;

                if (i == colorMappings.Count - 1) // 最后一个范围
                {
                    // 最后一个范围包含所有大于等于MinSize的颗粒
                    groupData = data.Where(p => p.DSIZE >= mapping.MinSize && !processedData.Contains(p)).ToList();
                }
                else
                {
                    // 其他范围使用 >= MinSize && < MaxSize
                    groupData = data.Where(p => p.DSIZE >= mapping.MinSize && p.DSIZE < mapping.MaxSize).ToList();
                }

                if (groupData.Count > 0)
                {
                    // 将颜色字符串转换为OxyColor
                    var oxyColor = ConvertToOxyColor(mapping.Color);

                    groups.Add(new ParticleGroup
                    {
                        SizeRange = mapping.Name,
                        Data = groupData,
                        OxyColor = oxyColor
                    });

                    // 标记这些数据点为已处理
                    foreach (var item in groupData)
                    {
                        processedData.Add(item);
                    }

                    System.Diagnostics.Debug.WriteLine($"分组: {mapping.Name}, 颗粒数: {groupData.Count}, 大小范围: {mapping.MinSize}-{mapping.MaxSize}");
                }
            }

            // 检查是否有未分组的数据点
            var ungroupedData = data.Where(p => !processedData.Contains(p)).ToList();
            if (ungroupedData.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"警告: 有 {ungroupedData.Count} 个颗粒未被分组");
                foreach (var item in ungroupedData)
                {
                    System.Diagnostics.Debug.WriteLine($"  未分组颗粒: ID={item.DEFECTID}, Size={item.DSIZE}");
                }

                // 将未分组的数据添加到一个默认组
                if (ungroupedData.Count > 0)
                {
                    groups.Add(new ParticleGroup
                    {
                        SizeRange = "未分组",
                        Data = ungroupedData,
                        OxyColor = OxyColors.Gray
                    });
                }
            }

            var totalGroupedCount = groups.Sum(g => g.Data.Count);
            System.Diagnostics.Debug.WriteLine($"分组完成: 原始数据 {data.Count} 个, 分组后 {totalGroupedCount} 个, 分组数 {groups.Count}");

            return groups;
        }

        // 将颜色字符串转换为OxyColor
        private OxyColor ConvertToOxyColor(string colorString)
        {
            try
            {
                // 移除#号
                var hex = colorString.TrimStart('#');

                // 解析RGB值
                var r = Convert.ToByte(hex.Substring(0, 2), 16);
                var g = Convert.ToByte(hex.Substring(2, 2), 16);
                var b = Convert.ToByte(hex.Substring(4, 2), 16);

                return OxyColor.FromRgb(r, g, b);
            }
            catch
            {
                return OxyColors.Gray; // 默认颜色
            }
        }

        // 颗粒分组类
        private class ParticleGroup
        {
            public string SizeRange { get; set; }
            public List<PAData> Data { get; set; }
            public OxyColor OxyColor { get; set; }
        }

        // 坐标轴范围类
        private class AxisRange
        {
            public double XMin { get; set; }
            public double XMax { get; set; }
            public double YMin { get; set; }
            public double YMax { get; set; }
        }

        // 计算统一的坐标轴范围
        private AxisRange CalculateUnifiedAxisRange()
        {
            var allData = new List<PAData>();

            if (PreData != null) allData.AddRange(PreData);
            if (AfterData != null) allData.AddRange(AfterData);
            if (AddData != null) allData.AddRange(AddData);

            if (allData.Count == 0)
            {
                // 默认范围
                return new AxisRange { XMin = -100, XMax = 100, YMin = -100, YMax = 100 };
            }

            var xMin = allData.Min(d => d.XREL);
            var xMax = allData.Max(d => d.XREL);
            var yMin = allData.Min(d => d.YREL);
            var yMax = allData.Max(d => d.YREL);

            // 添加一些边距（10%）
            var xRange = xMax - xMin;
            var yRange = yMax - yMin;
            var xMargin = Math.Max(xRange * 0.1, 10); // 至少10个单位的边距
            var yMargin = Math.Max(yRange * 0.1, 10);

            xMin -= xMargin;
            xMax += xMargin;
            yMin -= yMargin;
            yMax += yMargin;

            System.Diagnostics.Debug.WriteLine($"统一坐标轴范围: X[{xMin:F1}, {xMax:F1}], Y[{yMin:F1}, {yMax:F1}]");

            return new AxisRange { XMin = xMin, XMax = xMax, YMin = yMin, YMax = yMax };
        }

        // 刷新所有图表的坐标轴范围
        public void RefreshAllPlotModels()
        {
            System.Diagnostics.Debug.WriteLine("RefreshAllPlotModels: 刷新所有图表的坐标轴范围");

            if (PreData != null && PreData.Count > 0)
            {
                UpdatePlotModel(PrePlotModel, PreData, "Pre数据");
            }

            if (AfterData != null && AfterData.Count > 0)
            {
                UpdatePlotModel(AfterPlotModel, AfterData, "After数据");
            }

            if (AddData != null && AddData.Count > 0)
            {
                UpdatePlotModel(AddPlotModel, AddData, "Add数据");
            }
        }
    }
}