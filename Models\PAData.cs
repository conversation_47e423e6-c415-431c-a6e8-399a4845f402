using System;

namespace DatalogDrawing.Models  // 确保与项目命名空间一致
{
    public class PAData
    {
        // 原始数据文件中的字段（按DefectRecordSpec顺序）
        public int DEFECTID { get; set; }
        public double XREL { get; set; }
        public double YREL { get; set; }
        public int XINDEX { get; set; }
        public int YINDEX { get; set; }
        public double XSIZE { get; set; }
        public double YSIZE { get; set; }
        public double DEFECTAREA { get; set; }
        public double DSIZE { get; set; }
        public int CLASSNUMBER { get; set; }
        public int TEST { get; set; }
        public int CLUSTERNUMBER { get; set; }
        public int ROUGHBINNUMBER { get; set; }
        public int FINEBINNUMBER { get; set; }
        public int REVIEWSAMPLE { get; set; }
        public int IMAGECOUNT { get; set; }
        public string IMAGELIST { get; set; }

        // 计算字段和元数据
        public double DeltaDSIZE { get; set; }
        public string BatchId { get; set; }
        public DateTime Timestamp { get; set; }
        public string WaferId { get; set; }
        public string SlotNumber { get; set; }
        public string FilePath { get; set; }
    }
}
