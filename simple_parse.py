#!/usr/bin/env python3
"""
简化的膜厚数据解析器
专门处理实际CSV文件格式
"""

import re
import numpy as np
from typing import List, Dict

def parse_film_thickness_csv(file_path: str) -> Dict[str, List[Dict[str, float]]]:
    """
    解析膜厚CSV文件，返回每个槽位的测量点数据
    
    Returns:
        {slot_number: [{'X': x, 'Y': y, 'Value': thickness}, ...]}
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按空行分割不同的槽位数据
    sections = re.split(r'\n\s*\n\s*\n', content)
    
    slots_data = {}
    
    for section in sections:
        lines = [line.strip() for line in section.split('\n') if line.strip()]
        
        # 查找槽位编号
        slot_number = None
        for line in lines:
            if line.startswith('SLOT,'):
                slot_number = line.split(',')[1].strip()
                break
        
        if not slot_number:
            continue
        
        # 查找测量数据
        site_data_start = -1
        for i, line in enumerate(lines):
            if line.startswith('Site #,'):
                site_data_start = i + 1
                break
        
        if site_data_start == -1:
            continue
        
        # 解析测量点数据
        measurements = []
        for i in range(site_data_start, len(lines)):
            line = lines[i]
            if not line or not line[0].isdigit():
                break
            
            # 分割数据，处理多个空格
            parts = [p.strip() for p in line.split(',')]
            
            try:
                # 处理不同的数据格式
                if len(parts) >= 8:
                    # 格式1: Site#, Value1, Value2, Value3, Value4, Value5, X, Y (如Slot 14, 16)
                    site_num = int(parts[0])
                    thickness = float(parts[1])  # 第一个Value是厚度
                    x = float(parts[6])  # X坐标
                    y = float(parts[7])  # Y坐标

                    measurements.append({
                        'X': x,
                        'Y': y,
                        'Value': thickness
                    })
                elif len(parts) >= 5:
                    # 格式2: Site#, Value1, Value2, X, Y (如Slot 15)
                    site_num = int(parts[0])
                    thickness = float(parts[1])  # 第一个Value是厚度
                    x = float(parts[3])  # X坐标
                    y = float(parts[4])  # Y坐标

                    measurements.append({
                        'X': x,
                        'Y': y,
                        'Value': thickness
                    })
            except (ValueError, IndexError) as e:
                print(f"解析行出错: {line} - {e}")
                continue
        
        if measurements:
            slots_data[slot_number] = measurements
            print(f"槽位 {slot_number}: 解析到 {len(measurements)} 个测量点")
    
    return slots_data

def main():
    """测试解析器"""
    csv_path = r"E:\Code\WPF\DatalogDrawing\bin\Debug\data.csv"
    
    print("解析膜厚数据文件...")
    slots_data = parse_film_thickness_csv(csv_path)
    
    print(f"\n成功解析 {len(slots_data)} 个槽位的数据:")
    
    for slot_num, measurements in slots_data.items():
        if measurements:
            thicknesses = [m['Value'] for m in measurements]
            print(f"\n槽位 {slot_num}:")
            print(f"  测量点数: {len(measurements)}")
            print(f"  厚度范围: {min(thicknesses):.2f} - {max(thicknesses):.2f} nm")
            print(f"  平均厚度: {np.mean(thicknesses):.2f} ± {np.std(thicknesses):.2f} nm")
            
            # 显示前3个测量点
            print("  前3个测量点:")
            for i, m in enumerate(measurements[:3]):
                print(f"    {i+1}: X={m['X']}, Y={m['Y']}, 厚度={m['Value']:.2f}nm")
    
    return slots_data

if __name__ == "__main__":
    slots_data = main()
