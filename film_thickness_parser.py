#!/usr/bin/env python3
"""
膜厚数据解析器
专门解析实际的膜厚测量CSV文件格式
"""

import re
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class SlotData:
    """槽位数据结构"""
    slot_number: str
    wafer_id: str
    lot_id: str
    recipe: str
    material: str
    statistics: Dict[str, List[float]]  # 统计数据
    measurements: List[Dict[str, float]]  # 测量点数据
    
class FilmThicknessParser:
    """膜厚数据解析器"""
    
    def __init__(self):
        self.slots_data = {}
        
    def parse_csv_file(self, file_path: str) -> Dict[str, SlotData]:
        """
        解析CSV文件，提取所有槽位的膜厚数据
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            包含所有槽位数据的字典
        """
        self.slots_data = {}
        
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        # 清理行数据
        lines = [line.strip() for line in lines]
        
        # 查找所有槽位的起始位置
        slot_positions = []
        for i, line in enumerate(lines):
            if line.startswith('WAFER ID,'):
                slot_positions.append(i)
        
        # 解析每个槽位
        for i, start_pos in enumerate(slot_positions):
            end_pos = slot_positions[i + 1] if i + 1 < len(slot_positions) else len(lines)
            slot_lines = lines[start_pos:end_pos]
            
            try:
                slot_data = self._parse_slot_data(slot_lines)
                if slot_data:
                    self.slots_data[slot_data.slot_number] = slot_data
            except Exception as e:
                print(f"解析槽位数据时出错: {e}")
                continue
        
        return self.slots_data
    
    def _parse_slot_data(self, lines: List[str]) -> Optional[SlotData]:
        """解析单个槽位的数据"""
        if not lines:
            return None
            
        # 提取基本信息
        wafer_id = self._extract_value(lines, 'WAFER ID')
        lot_id = self._extract_value(lines, 'LOT ID')
        slot_number = self._extract_value(lines, 'SLOT')
        recipe = self._extract_value(lines, 'RECIPE')
        material = self._extract_value(lines, 'MATERIAL')
        
        if not slot_number:
            return None
        
        # 查找统计数据和测量数据的位置
        result_type_line = -1
        site_data_start = -1
        
        for i, line in enumerate(lines):
            if line.startswith('RESULT TYPE,'):
                result_type_line = i
            elif line.startswith('Site #,'):
                site_data_start = i + 1
                break
        
        if result_type_line == -1 or site_data_start == -1:
            return None
        
        # 解析统计数据
        statistics = self._parse_statistics(lines, result_type_line)
        
        # 解析测量点数据
        measurements = self._parse_measurements(lines, site_data_start)
        
        return SlotData(
            slot_number=slot_number,
            wafer_id=wafer_id,
            lot_id=lot_id,
            recipe=recipe,
            material=material,
            statistics=statistics,
            measurements=measurements
        )
    
    def _extract_value(self, lines: List[str], key: str) -> str:
        """从行中提取指定键的值"""
        for line in lines:
            if line.startswith(f'{key},'):
                parts = line.split(',', 1)
                if len(parts) > 1:
                    return parts[1].strip('"').strip()
        return ""
    
    def _parse_statistics(self, lines: List[str], start_line: int) -> Dict[str, List[float]]:
        """解析统计数据"""
        statistics = {}
        
        # 获取列名
        result_type_line = lines[start_line]
        columns = [col.strip() for col in result_type_line.split(',')[1:] if col.strip()]
        
        # 解析统计行
        stat_keywords = ['MEAN', 'MIN', 'MAX', '% STDDEV', 'STDDEV', 'RANGE']
        
        for i in range(start_line + 1, len(lines)):
            line = lines[i]
            if not line or line.startswith('Site #,'):
                break
                
            for keyword in stat_keywords:
                if line.startswith(f'{keyword},'):
                    values = line.split(',')[1:]
                    try:
                        float_values = []
                        for val in values:
                            val = val.strip()
                            if val and val != '':
                                try:
                                    float_values.append(float(val))
                                except ValueError:
                                    continue
                        if float_values:
                            statistics[keyword] = float_values
                    except Exception as e:
                        print(f"解析统计数据 {keyword} 时出错: {e}")
                    break
        
        return statistics
    
    def _parse_measurements(self, lines: List[str], start_line: int) -> List[Dict[str, float]]:
        """解析测量点数据"""
        measurements = []

        if start_line >= len(lines):
            return measurements

        # 获取列名（从Site #行）
        header_line = lines[start_line - 1] if start_line > 0 else ""
        if not header_line.startswith('Site #,'):
            return measurements

        # 解析列名，处理多个Value列的情况
        header_parts = header_line.split(',')
        columns = []
        value_count = 0

        for part in header_parts:
            part = part.strip()
            if part == 'Value':
                value_count += 1
                columns.append(f'Value_{value_count}')  # 给Value列编号
            else:
                columns.append(part)

        print(f"调试: 列名解析结果: {columns}")

        # 解析数据行
        for i in range(start_line, len(lines)):
            line = lines[i]
            if not line or line.strip() == '':
                break

            # 使用正则表达式分割，处理多个空格的情况
            import re
            values = re.split(r',\s*', line)
            if len(values) < 3:  # 至少需要Site#, 一些数据, X, Y
                continue

            try:
                measurement = {}
                for j, value in enumerate(values):
                    if j < len(columns):
                        col_name = columns[j]
                        value = value.strip()

                        if value and value != '':
                            try:
                                measurement[col_name] = float(value)
                            except ValueError:
                                measurement[col_name] = value

                print(f"调试: 解析的测量点: {measurement}")

                # 确保有X, Y坐标
                if ('X' in measurement and 'Y' in measurement) or (' X ' in measurement and ' Y ' in measurement):
                    # 标准化X, Y列名
                    x_val = measurement.get('X') or measurement.get(' X ')
                    y_val = measurement.get('Y') or measurement.get(' Y ')

                    if x_val is not None and y_val is not None:
                        measurement['X'] = x_val
                        measurement['Y'] = y_val

                        # 查找厚度值 - 第一个Value列通常是厚度
                        thickness_value = None

                        # 1. 查找Value_1（第一个Value列）
                        if 'Value_1' in measurement:
                            val = measurement['Value_1']
                            if isinstance(val, (int, float)) and val > 10:  # 厚度通常大于10nm
                                thickness_value = val

                        # 2. 如果没找到，查找所有数值列中最大的值
                        if thickness_value is None:
                            numeric_values = []
                            for key, val in measurement.items():
                                if (isinstance(val, (int, float)) and
                                    key not in ['Site #', 'X', 'Y', ' X ', ' Y '] and
                                    val > 10):
                                    numeric_values.append((key, val))

                            if numeric_values:
                                thickness_value = max(numeric_values, key=lambda x: x[1])[1]

                        if thickness_value is not None:
                            measurement['Thickness'] = thickness_value
                            measurements.append(measurement)
                            print(f"调试: 添加测量点 - X:{x_val}, Y:{y_val}, Thickness:{thickness_value}")

            except Exception as e:
                print(f"解析测量数据行时出错: {e}")
                print(f"问题行: {line}")
                continue

        print(f"调试: 总共解析到 {len(measurements)} 个测量点")
        return measurements
    
    def get_heatmap_points(self, slot_number: str) -> List[Dict[str, float]]:
        """
        获取指定槽位的热力图数据点
        
        Args:
            slot_number: 槽位编号
            
        Returns:
            热力图数据点列表
        """
        if slot_number not in self.slots_data:
            return []
        
        slot_data = self.slots_data[slot_number]
        points = []
        
        for measurement in slot_data.measurements:
            if 'X' in measurement and 'Y' in measurement and 'Thickness' in measurement:
                points.append({
                    'X': measurement['X'],
                    'Y': measurement['Y'],
                    'Value': measurement['Thickness']
                })
        
        return points
    
    def get_slot_summary(self, slot_number: str) -> Dict:
        """获取槽位摘要信息"""
        if slot_number not in self.slots_data:
            return {}
        
        slot_data = self.slots_data[slot_number]
        points = self.get_heatmap_points(slot_number)
        
        summary = {
            'slot_number': slot_data.slot_number,
            'wafer_id': slot_data.wafer_id,
            'lot_id': slot_data.lot_id,
            'recipe': slot_data.recipe,
            'material': slot_data.material,
            'points_count': len(points),
            'statistics': slot_data.statistics
        }
        
        if points:
            thicknesses = [p['Value'] for p in points]
            summary['thickness_range'] = {
                'min': min(thicknesses),
                'max': max(thicknesses),
                'mean': np.mean(thicknesses),
                'std': np.std(thicknesses)
            }
        
        return summary
    
    def list_available_slots(self) -> List[str]:
        """列出所有可用的槽位编号"""
        return list(self.slots_data.keys())
    
    def export_slot_to_json(self, slot_number: str, output_path: str) -> bool:
        """将槽位数据导出为JSON格式"""
        import json
        
        points = self.get_heatmap_points(slot_number)
        if not points:
            return False
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(points, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出JSON文件失败: {e}")
            return False

def main():
    """主函数 - 演示解析器使用"""
    # 解析CSV文件
    parser = FilmThicknessParser()
    csv_path = r"E:\Code\WPF\DatalogDrawing\bin\Debug\data.csv"

    print("解析膜厚数据文件...")
    slots_data = parser.parse_csv_file(csv_path)

    print(f"成功解析 {len(slots_data)} 个槽位的数据")
    print("\n可用槽位:")

    for slot_num in parser.list_available_slots():
        summary = parser.get_slot_summary(slot_num)
        print(f"  槽位 {slot_num}: {summary['wafer_id']} ({summary['points_count']} 个测量点)")

        # 调试信息：显示原始测量数据
        if summary['points_count'] == 0:
            slot_data = slots_data[slot_num]
            print(f"    调试: 原始测量数据数量: {len(slot_data.measurements)}")
            if slot_data.measurements:
                first_measurement = slot_data.measurements[0]
                print(f"    调试: 第一个测量点的键: {list(first_measurement.keys())}")
                print(f"    调试: 第一个测量点的值: {first_measurement}")

        if 'thickness_range' in summary:
            tr = summary['thickness_range']
            print(f"    厚度范围: {tr['min']:.2f} - {tr['max']:.2f} nm (平均: {tr['mean']:.2f} ± {tr['std']:.2f})")

    return parser

if __name__ == "__main__":
    parser = main()
