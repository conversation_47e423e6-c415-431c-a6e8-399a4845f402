using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using DatalogDrawing.Models;
using DatalogDrawing.Services;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using OxyPlot.Annotations;

namespace DatalogDrawing.ViewModels
{
    /// <summary>
    /// 参数项类，替代元组
    /// </summary>
    public class ParameterItem
    {
        public string Name { get; set; }
        public string Value { get; set; }

        public ParameterItem(string name, string value)
        {
            Name = name;
            Value = value;
        }
    }

    public class RecipeViewModel : ViewModelBase
    {
        private readonly RecipeParserService _parserService;
        private readonly RecipeConfigService _configService;

        // 命令
        public ICommand SelectRecipeFileCommand { get; private set; }
        public ICommand ParseRecipeCommand { get; private set; }
        public ICommand GenerateChartCommand { get; private set; }
        public ICommand ClearDataCommand { get; private set; }
        public ICommand SelectConfigCommand { get; private set; }

        // 测试命令
        public ICommand ResetTestCommand { get; private set; }
        public ICommand QuickTestCommand { get; private set; }

        // 配置管理命令
        public ICommand ReloadConfigCommand { get; private set; }
        public ICommand ExportConfigCommand { get; private set; }
        public ICommand ShowConfigStatsCommand { get; private set; }
        public ICommand DebugBindingCommand { get; private set; }

        // 属性
        private string _selectedRecipeFile;
        public string SelectedRecipeFile
        {
            get => _selectedRecipeFile;
            set
            {
                if (SetProperty(ref _selectedRecipeFile, value, "SelectedRecipeFile"))
                {
                    // 触发命令状态更新
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        private string _selectedConfigFile;
        public string SelectedConfigFile
        {
            get => _selectedConfigFile;
            set => SetProperty(ref _selectedConfigFile, value, "SelectedConfigFile");
        }

        private RecipeType _selectedRecipeType = RecipeType.MainRecipe;
        public RecipeType SelectedRecipeType
        {
            get => _selectedRecipeType;
            set => SetProperty(ref _selectedRecipeType, value, "SelectedRecipeType");
        }

        public int SelectedRecipeTypeIndex
        {
            get => (int)SelectedRecipeType;
            set
            {
                SelectedRecipeType = (RecipeType)value;
                OnPropertyChanged("RecipeTypeDisplay");
            }
        }

        private RecipeData _currentRecipeData;
        public RecipeData CurrentRecipeData
        {
            get => _currentRecipeData;
            set => SetProperty(ref _currentRecipeData, value, "CurrentRecipeData");
        }

        private ObservableCollection<RecipeStep> _recipeSteps = new ObservableCollection<RecipeStep>();
        public ObservableCollection<RecipeStep> RecipeSteps
        {
            get => _recipeSteps;
            set => SetProperty(ref _recipeSteps, value, "RecipeSteps");
        }

        private PlotModel _processTimelineChart;
        public PlotModel ProcessTimelineChart
        {
            get => _processTimelineChart;
            set => SetProperty(ref _processTimelineChart, value, "ProcessTimelineChart");
        }

        /// <summary>
        /// 动态生成DataGrid列的事件
        /// </summary>
        public event Action<DataGrid> GenerateDataGridColumns;

        private string _statusMessage = "请选择Recipe文件";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value, "StatusMessage");
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value, "IsBusy");
        }

        // 统计信息
        public string TotalTimeDisplay => CurrentRecipeData?.TotalTime.ToString(@"hh\:mm\:ss") ?? "00:00:00";
        public int StepCountDisplay => CurrentRecipeData?.StepCount ?? 0;
        public string RecipeTypeDisplay => SelectedRecipeType == RecipeType.MainRecipe ? "主配方" : "子配方";
        public string ConfigStatsDisplay => GetConfigStatistics();

        public RecipeViewModel()
        {
            _configService = new RecipeConfigService();
            _parserService = new RecipeParserService(_configService);
            InitializeCommands();
            InitializeChart();
            
            try
            {
                LoadDefaultConfig();
            }
            catch
            {
                // 配置加载失败，但不阻止ViewModel初始化
                StatusMessage = "配置加载失败，请检查RecipeConfig.json文件";
            }

            // 测试模式：自动加载测试文件
            LoadTestFiles();
        }

        /// <summary>
        /// 根据配置动态生成DataGrid列
        /// </summary>
        /// <param name="dataGrid">目标DataGrid</param>
        public void SetupDataGridColumns(DataGrid dataGrid)
        {
            if (dataGrid == null) return;

            // 清除现有列
            dataGrid.Columns.Clear();

            // 根据配置生成列
            var displayColumns = _configService.CurrentConfig.DisplayConfig.Columns;
            
            foreach (var columnConfig in displayColumns)
            {
                var bindingPath = GetBindingPath(columnConfig);
                
                // 调试输出
                System.Diagnostics.Debug.WriteLine($"列配置: Field={columnConfig.Field}, Header={columnConfig.Header}, Type={columnConfig.Type}, BindingPath={bindingPath}");
                
                var column = new DataGridTextColumn
                {
                    Header = columnConfig.Header,
                    // 使用GetBindingPath处理复杂绑定
                    Binding = new Binding(bindingPath),
                    Width = GetColumnWidth(columnConfig.Header)
                };

                dataGrid.Columns.Add(column);
            }
            
            // 输出总列数
            System.Diagnostics.Debug.WriteLine($"DataGrid列设置完成，共{dataGrid.Columns.Count}列");
        }

        /// <summary>
        /// 获取正确的绑定路径，支持新的配置格式
        /// </summary>
        /// <param name="columnConfig">列配置</param>
        /// <returns>正确的绑定路径</returns>
        private string GetBindingPath(RecipeColumnConfig columnConfig)
        {
            // 根据配置类型返回不同的绑定路径
            switch (columnConfig.Type)
            {
                case "composite":
                    return GetCompositeBindingPath(columnConfig);
                case "conditional":
                    return GetConditionalBindingPath(columnConfig);
                case "selector":
                    return GetSelectorBindingPath(columnConfig);
                default:
                    // 简单字段，需要映射到对应的Display属性
                    var fieldName = columnConfig.Source ?? columnConfig.Field;
                    return GetSimpleFieldBindingPath(fieldName);
            }
        }

        /// <summary>
        /// 获取简单字段的绑定路径
        /// </summary>
        private string GetSimpleFieldBindingPath(string fieldName)
        {
            // 处理包含特殊字符的字段名
            switch (fieldName)
            {
                case "StepNo":
                case "StepName":
                case "StepTime":
                case "StepUpCondition":
                case "StepUpConditionTargetValue":
                case "CommandCode":
                case "ElevatorCommand":
                case "ElevatorSpeed":
                case "BoatRotateCommand":
                case "BoatRotateSpeed":
                case "TempZone1Set":
                case "TempZone2Set":
                case "TempZone3Set":
                case "TempZone4Set":
                case "TempZone5Set":
                case "TempZone6Set":
                case "TempZone7Set":
                case "TempZone8Set":
                case "TempZone1RampSet":
                case "TempZone2RampSet":
                case "TempZone3RampSet":
                case "TempZone4RampSet":
                case "TempZone5RampSet":
                case "TempZone6RampSet":
                case "TempZone7RampSet":
                case "TempZone8RampSet":
                case "TempControlMode":
                case "LoopCount":
                case "APC2ControlMode":
                case "APC2Set":
                case "APC2RampSet":
                case "APC3ControlMode":
                case "APC3Set":
                case "APC3RampSet":
                case "SubRecipeName":
                case "CallCount":
                    return fieldName;
                case "Command":
                    return "CommandDisplay";
                case "Temp Set(°C)":
                    return "TempSetDisplay";
                case "Temp Rate(°C/min)":
                    return "TempRateDisplay";
                case "Temp Mode":
                    return "TempModeDisplay";
                case "Loop":
                    return "LoopDisplay";
                case "APC2 Setting(mTorr)":
                    return "APC2SettingDisplay";
                case "APC3 Setting(mTorr)":
                    return "APC3SettingDisplay";
                default:
                    return fieldName;
            }
        }

        /// <summary>
        /// 获取复合字段的绑定路径
        /// </summary>
        private string GetCompositeBindingPath(RecipeColumnConfig columnConfig)
        {
            // 复合字段需要特殊的显示属性
            switch (columnConfig.Field)
            {
                case "Condition":
                    return "ConditionDisplay";
                case "SubRecipeName":
                    return "SubRecipeNameDisplay";
                default:
                    return columnConfig.Field;
            }
        }

        /// <summary>
        /// 获取条件字段的绑定路径
        /// </summary>
        private string GetConditionalBindingPath(RecipeColumnConfig columnConfig)
        {
            // 条件字段需要特殊的显示属性
            switch (columnConfig.Field)
            {
                case "ElevatorCommand":
                    return "ElevatorCommandDisplay";
                case "BoatRotateCommand":
                    return "BoatRotateCommandDisplay";
                default:
                    return columnConfig.Field;
            }
        }

        /// <summary>
        /// 获取选择器字段的绑定路径
        /// </summary>
        private string GetSelectorBindingPath(RecipeColumnConfig columnConfig)
        {
            // 选择器字段需要特殊的显示属性
            switch (columnConfig.Field)
            {
                case "APC2 Setting(mTorr)":
                    return "APC2SettingDisplay";
                case "APC3 Setting(mTorr)":
                    return "APC3SettingDisplay";
                default:
                    return columnConfig.Field;
            }
        }

        /// <summary>
        /// 根据列名获取合适的列宽度
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>列宽度</returns>
        private DataGridLength GetColumnWidth(string columnName)
        {
            // 根据列名设置不同的宽度
            switch (columnName)
            {
                case "StepNo":
                    return new DataGridLength(50);
                case "StepName":
                    return new DataGridLength(100);
                case "StepTime":
                case "StepUpCondition":
                case "CommandCode":
                case "TempZone1Set":
                case "TempZone1RampSet":
                case "TempControlMode":
                case "LoopCount":
                case "Temp Set(°C)":
                case "Temp Rate(°C/min)":
                case "Temp Mode":
                case "Loop":
                case "Command":
                    return new DataGridLength(80);
                case "ElevatorCommand":
                case "BoatRotateCommand":
                case "APC2ControlMode":
                case "APC3ControlMode":
                case "APC2 Setting(mTorr)":
                case "APC3 Setting(mTorr)":
                    return new DataGridLength(100);
                case "SubRecipeName":
                case "Condition":
                    return new DataGridLength(120);
                default:
                    return new DataGridLength(80);  // 默认宽度
            }
        }

        private void InitializeCommands()
        {
            SelectRecipeFileCommand = new RelayCommand(SelectRecipeFile);
            ParseRecipeCommand = new RelayCommand(ParseRecipe, CanParseRecipe);
            GenerateChartCommand = new RelayCommand(GenerateChart, CanGenerateChart);
            ClearDataCommand = new RelayCommand(ClearData);
            SelectConfigCommand = new RelayCommand(SelectConfig);

            // 测试命令
            ResetTestCommand = new RelayCommand(ResetTestEnvironment);
            QuickTestCommand = new RelayCommand(QuickTest);

            // 配置管理命令
            ReloadConfigCommand = new RelayCommand(ReloadConfigAndUpdateColumns);
            ExportConfigCommand = new RelayCommand(ExportConfig);
            ShowConfigStatsCommand = new RelayCommand(ShowConfigStatistics);
            DebugBindingCommand = new RelayCommand(DebugDataGridBinding);
        }

        private void LoadDefaultConfig()
        {
            try
            {
                _configService.LoadDefaultConfig();
                SelectedConfigFile = "RecipeConfig.json (默认)";
                
                // 验证配置是否正确加载
                var config = _configService.CurrentConfig;
                StatusMessage = $"已加载默认Recipe配置，共{config.DisplayConfig.Columns.Count}列";
                System.Diagnostics.Debug.WriteLine($"已加载默认Recipe配置，共{config.DisplayConfig.Columns.Count}列");
            }
            catch (FileNotFoundException ex)
            {
                StatusMessage = $"配置文件不存在: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"配置文件不存在: {ex.Message}");
                MessageBox.Show($"Recipe配置文件不存在:\n{ex.Message}\n\n请确保RecipeConfig.json文件存在于正确位置。", 
                    "配置文件错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (InvalidOperationException ex)
            {
                StatusMessage = $"配置文件格式错误: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"配置文件格式错误: {ex.Message}");
                MessageBox.Show($"Recipe配置文件格式错误:\n{ex.Message}\n\n请检查RecipeConfig.json文件的格式。", 
                    "配置文件错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                StatusMessage = "加载配置失败: " + ex.Message;
                System.Diagnostics.Debug.WriteLine($"加载配置失败: {ex.Message}");
                MessageBox.Show($"加载Recipe配置时发生未知错误:\n{ex.Message}", 
                    "配置加载错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeChart()
        {
            ProcessTimelineChart = new PlotModel
            {
                Title = "工艺时序图",
                Background = OxyColors.White
            };
        }

        #region 命令实现

        private void SelectRecipeFile()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
                Title = "选择Recipe文件",
                InitialDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug")
            };

            if (openFileDialog.ShowDialog() == true)
            {
                SelectedRecipeFile = openFileDialog.FileName;

                // 根据文件名自动判断Recipe类型
                var fileName = Path.GetFileName(SelectedRecipeFile).ToLower();
                if (fileName.Contains("sub") || fileName.Contains("子"))
                {
                    SelectedRecipeType = RecipeType.SubRecipe;
                }
                else
                {
                    SelectedRecipeType = RecipeType.MainRecipe;
                }

                StatusMessage = "已选择文件: " + Path.GetFileName(SelectedRecipeFile);
                OnPropertyChanged("RecipeTypeDisplay");
            }
        }

        private bool CanParseRecipe()
        {
            return !string.IsNullOrEmpty(SelectedRecipeFile) && File.Exists(SelectedRecipeFile) && !IsBusy;
        }

        private void ParseRecipe()
        {
            if (!CanParseRecipe()) return;

            IsBusy = true;
            StatusMessage = "正在解析Recipe文件...";

            try
            {
                // 直接同步解析，避免async/await复杂性
                CurrentRecipeData = _parserService.ParseRecipeFile(SelectedRecipeFile);

                // 更新步骤集合
                RecipeSteps.Clear();
                foreach (var step in CurrentRecipeData.Steps)
                {
                    RecipeSteps.Add(step);
                }

                // 更新统计信息显示
                OnPropertyChanged("TotalTimeDisplay");
                OnPropertyChanged("StepCountDisplay");

                // 触发DataGrid列重新生成
                GenerateDataGridColumns?.Invoke(null);

                StatusMessage = string.Format("解析完成，共{0}个步骤，总时间{1}", CurrentRecipeData.StepCount, TotalTimeDisplay);
            }
            catch (Exception ex)
            {
                StatusMessage = "解析失败: " + ex.Message;
                MessageBox.Show("解析Recipe文件时出错:\n" + ex.Message, "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private bool CanGenerateChart()
        {
            return CurrentRecipeData != null && CurrentRecipeData.Steps.Count > 0 && !IsBusy;
        }

        private void GenerateChart()
        {
            if (!CanGenerateChart()) return;

            IsBusy = true;
            StatusMessage = "正在生成工艺时序图...";

            try
            {
                ProcessTimelineChart = CreateProcessTimelineChart();
                StatusMessage = "工艺时序图生成完成";
            }
            catch (Exception ex)
            {
                StatusMessage = "生成图表失败: " + ex.Message;
                MessageBox.Show("生成工艺时序图时出错:\n" + ex.Message, "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ClearData()
        {
            CurrentRecipeData = null;
            RecipeSteps.Clear();
            SelectedRecipeFile = "";
            ProcessTimelineChart = new PlotModel { Title = "工艺时序图", Background = OxyColors.White };
            StatusMessage = "数据已清空，请选择Recipe文件";

            OnPropertyChanged("TotalTimeDisplay");
            OnPropertyChanged("StepCountDisplay");
        }

        private void SelectConfig()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "JSON Config Files (*.json)|*.json|All Files (*.*)|*.*",
                Title = "选择Recipe配置文件",
                InitialDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug")
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _configService.LoadConfig(openFileDialog.FileName);
                    SelectedConfigFile = Path.GetFileName(openFileDialog.FileName);
                    StatusMessage = "已加载配置: " + SelectedConfigFile;

                    // 触发重新生成DataGrid列
                    GenerateDataGridColumns?.Invoke(null);
                }
                catch (Exception ex)
                {
                    StatusMessage = "加载配置失败: " + ex.Message;
                    MessageBox.Show("加载配置文件时出错:\n" + ex.Message, "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 重新加载当前配置并更新DataGrid列
        /// </summary>
        public void ReloadConfigAndUpdateColumns()
        {
            try
            {
                // 重新加载配置
                _configService.LoadDefaultConfig();
                
                // 触发重新生成DataGrid列
                GenerateDataGridColumns?.Invoke(null);
                
                StatusMessage = "配置已重新加载并更新列显示";
            }
            catch (Exception ex)
            {
                StatusMessage = "重新加载配置失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 获取当前配置的列信息
        /// </summary>
        /// <returns>列配置列表</returns>
        public List<RecipeColumnConfig> GetCurrentColumnConfigs()
        {
            return _configService.CurrentConfig.DisplayConfig.Columns;
        }

        /// <summary>
        /// 验证配置文件的完整性
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        /// <returns>验证结果</returns>
        public bool ValidateConfig(string configPath)
        {
            try
            {
                if (!File.Exists(configPath))
                    return false;

                var json = File.ReadAllText(configPath);
                var config = Newtonsoft.Json.JsonConvert.DeserializeObject<RecipeConfig>(json);
                
                // 验证基本结构
                if (config?.DisplayConfig?.Columns == null)
                    return false;

                // 验证列配置
                foreach (var column in config.DisplayConfig.Columns)
                {
                    if (string.IsNullOrEmpty(column.Field) || string.IsNullOrEmpty(column.Header))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 保存当前配置到文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        public void SaveConfig(string configPath)
        {
            try
            {
                _configService.SaveConfig(configPath);
                StatusMessage = "配置已保存到: " + Path.GetFileName(configPath);
            }
            catch (Exception ex)
            {
                StatusMessage = "保存配置失败: " + ex.Message;
                MessageBox.Show("保存配置文件时出错:\n" + ex.Message, "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导出当前配置
        /// </summary>
        public void ExportConfig()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "JSON Config Files (*.json)|*.json|All Files (*.*)|*.*",
                Title = "导出Recipe配置文件",
                InitialDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug"),
                FileName = "RecipeConfig_Export.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                SaveConfig(saveFileDialog.FileName);
            }
        }

        /// <summary>
        /// 获取配置文件的统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetConfigStatistics()
        {
            try
            {
                var config = _configService.CurrentConfig;
                var columnCount = config.DisplayConfig.Columns.Count;
                var compositeCount = config.DisplayConfig.Columns.Count(c => c.Type == "composite");
                var conditionalCount = config.DisplayConfig.Columns.Count(c => c.Type == "conditional");
                var selectorCount = config.DisplayConfig.Columns.Count(c => c.Type == "selector");
                var simpleCount = columnCount - compositeCount - conditionalCount - selectorCount;

                return $"总列数: {columnCount}, 复合字段: {compositeCount}, 条件字段: {conditionalCount}, 选择器字段: {selectorCount}, 简单字段: {simpleCount}";
            }
            catch
            {
                return "无法获取配置统计信息";
            }
        }

        /// <summary>
        /// 显示配置统计信息
        /// </summary>
        private void ShowConfigStatistics()
        {
            var stats = GetConfigStatistics();
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug", "RecipeConfig.json");
            var isValid = ValidateConfig(configPath);
            
            var message = $"配置统计信息:\n{stats}\n\n配置文件路径: {configPath}\n配置文件状态: {(isValid ? "有效" : "无效")}";
            
            MessageBox.Show(message, "配置统计信息", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 调试DataGrid绑定问题
        /// </summary>
        public void DebugDataGridBinding()
        {
            try
            {
                var debugInfo = new System.Text.StringBuilder();
                
                // 检查数据
                if (CurrentRecipeData?.Steps == null || CurrentRecipeData.Steps.Count == 0)
                {
                    debugInfo.AppendLine("❌ 没有Recipe数据");
                    MessageBox.Show(debugInfo.ToString(), "DataGrid绑定调试", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                debugInfo.AppendLine("✅ Recipe数据存在");
                debugInfo.AppendLine($"📊 步骤数量: {CurrentRecipeData.Steps.Count}");
                
                var firstStep = CurrentRecipeData.Steps[0];
                debugInfo.AppendLine("\n📋 RecipeStep属性调试信息:");
                debugInfo.AppendLine($"StepNo: {firstStep.StepNo}");
                debugInfo.AppendLine($"StepName: {firstStep.StepName}");
                debugInfo.AppendLine($"CommandCode: {firstStep.CommandCode}");
                debugInfo.AppendLine($"CommandDisplay: {firstStep.CommandDisplay}");
                debugInfo.AppendLine($"TempZone1Set: {firstStep.TempZone1Set}");
                debugInfo.AppendLine($"TempSetDisplay: {firstStep.TempSetDisplay}");
                debugInfo.AppendLine($"TempZone1RampSet: {firstStep.TempZone1RampSet}");
                debugInfo.AppendLine($"TempRateDisplay: {firstStep.TempRateDisplay}");
                debugInfo.AppendLine($"TempControlMode: {firstStep.TempControlMode}");
                debugInfo.AppendLine($"TempModeDisplay: {firstStep.TempModeDisplay}");
                debugInfo.AppendLine($"LoopCount: {firstStep.LoopCount}");
                debugInfo.AppendLine($"LoopDisplay: {firstStep.LoopDisplay}");
                debugInfo.AppendLine($"APC2ControlMode: {firstStep.APC2ControlMode}");
                debugInfo.AppendLine($"APC2SettingDisplay: {firstStep.APC2SettingDisplay}");
                debugInfo.AppendLine($"APC3ControlMode: {firstStep.APC3ControlMode}");
                debugInfo.AppendLine($"APC3SettingDisplay: {firstStep.APC3SettingDisplay}");
                debugInfo.AppendLine($"StepUpCondition: {firstStep.StepUpCondition}");
                debugInfo.AppendLine($"ConditionDisplay: {firstStep.ConditionDisplay}");

                // 检查配置
                debugInfo.AppendLine("\n⚙️ 配置信息:");
                var config = _configService.CurrentConfig;
                debugInfo.AppendLine($"配置是否加载: {(config != null ? "是" : "否")}");
                if (config?.DisplayConfig?.Columns != null)
                {
                    debugInfo.AppendLine($"列配置数量: {config.DisplayConfig.Columns.Count}");
                    foreach (var column in config.DisplayConfig.Columns)
                    {
                        var bindingPath = GetBindingPath(column);
                        debugInfo.AppendLine($"  - {column.Header} (Field: {column.Field}, Type: {column.Type}, Binding: {bindingPath})");
                    }
                }
                else
                {
                    debugInfo.AppendLine("❌ 列配置为空");
                }

                // 检查RecipeSteps集合
                debugInfo.AppendLine($"\n📋 RecipeSteps集合:");
                debugInfo.AppendLine($"集合数量: {RecipeSteps.Count}");
                debugInfo.AppendLine($"集合是否为null: {(RecipeSteps == null ? "是" : "否")}");

                MessageBox.Show(debugInfo.ToString(), "DataGrid绑定调试", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"调试过程中出错: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "调试错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 图表生成

        /// <summary>
        /// 创建工艺流程图（甘特图样式）
        /// </summary>
        private PlotModel CreateProcessTimelineChart()
        {
            var plotModel = new PlotModel
            {
                Title = RecipeTypeDisplay + " - 工艺流程图",
                Background = OxyColors.White,
                PlotAreaBorderColor = OxyColors.Black,
                PlotAreaBorderThickness = new OxyThickness(1),
                PlotMargins = new OxyThickness(150, 40, 40, 60)
            };

            // 添加时间轴（X轴）
            var timeAxis = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Title = "时间 (分钟)",
                MajorGridlineStyle = LineStyle.Solid,
                MajorGridlineColor = OxyColors.LightGray,
                MinorGridlineStyle = LineStyle.Dot,
                MinorGridlineColor = OxyColors.LightGray,
                Minimum = 0,
                Maximum = CurrentRecipeData.TotalTime.TotalMinutes * 1.1
            };
            plotModel.Axes.Add(timeAxis);

            // 添加参数轴（Y轴）
            var parameterAxis = new CategoryAxis
            {
                Position = AxisPosition.Left,
                Title = "工艺参数",
                Key = "ParameterAxis",
                MajorGridlineStyle = LineStyle.Solid,
                MajorGridlineColor = OxyColors.LightGray
            };

            // 从配置中获取表格列名作为Y轴参数
            var columnConfigs = GetCurrentColumnConfigs();
            var parameters = new List<string>();

            // 添加所有有意义的列（排除StepNo和StepName）
            foreach (var column in columnConfigs)
            {
                if (column.Header != "StepNo" && column.Header != "StepName")
                {
                    parameters.Add(column.Header);
                }
            }

            // 如果没有配置，使用默认参数
            if (parameters.Count == 0)
            {
                parameters = new List<string>
                {
                    "Temp Set(°C)",
                    "Condition",
                    "Command",
                    "ElevatorCommand",
                    "BoatRotateCommand",
                    "Temp Rate(°C/min)",
                    "Temp Mode",
                    "Loop",
                    "APC2 Setting(mTorr)",
                    "APC3 Setting(mTorr)",
                    "SubRecipeName"
                };
            }
            else
            {
                // 重新排序，将"Temp Set(°C)"放在列表最后（反转后显示在最上面）
                var tempSetIndex = parameters.IndexOf("Temp Set(°C)");
                if (tempSetIndex >= 0)
                {
                    parameters.RemoveAt(tempSetIndex);
                    parameters.Add("Temp Set(°C)"); // 添加到列表末尾
                }
            }

            foreach (var param in parameters)
            {
                parameterAxis.Labels.Add(param);
            }
            plotModel.Axes.Add(parameterAxis);

            // 为温度数据添加数值Y轴
            var tempValueAxis = new LinearAxis
            {
                Position = AxisPosition.Right,
                Title = "温度 (°C)",
                Key = "TempValueAxis",
                IsAxisVisible = false, // 隐藏轴线，只用于数据映射
                Minimum = 0,
                Maximum = 1200 // 根据实际温度范围调整
            };
            plotModel.Axes.Add(tempValueAxis);

            // 创建甘特图时间条
            CreateProcessFlowNodes(plotModel, parameters);

            return plotModel;
        }

        /// <summary>
        /// 创建甘特图时间条
        /// </summary>
        private void CreateProcessFlowNodes(PlotModel plotModel, List<string> parameters)
        {
            if (CurrentRecipeData?.Steps == null || CurrentRecipeData.Steps.Count == 0)
                return;

            // 时间条颜色
            var barColors = new[]
            {
                OxyColors.LightBlue,
                OxyColors.LightGreen,
                OxyColors.Orange,
                OxyColors.LightCoral,
                OxyColors.LightPink,
                OxyColors.LightYellow,
                OxyColors.LightCyan,
                OxyColors.Wheat,
                OxyColors.LightSalmon
            };

            double currentTime = 0;

            // 获取Temp Set参数在Y轴上的位置
            double tempSetY = GetTempSetParameterYPosition(plotModel);
            
            // 为温度数据创建折线图系列
            var tempSeries = new LineSeries
            {
                Title = "温度变化",
                Color = OxyColors.Red,
                StrokeThickness = 2,
                YAxisKey = "TempValueAxis", // 使用右侧的温度数值轴
                MarkerType = MarkerType.Circle,
                MarkerSize = 3,
                MarkerFill = OxyColors.Red
            };

            // 收集温度数据点
            var tempPoints = new List<DataPoint>();
            var tempBackgrounds = new List<Tuple<double, double>>(); // 存储需要添加背景的时间段

            // 为每个步骤创建时间条
            for (int stepIndex = 0; stepIndex < CurrentRecipeData.Steps.Count; stepIndex++)
            {
                var step = CurrentRecipeData.Steps[stepIndex];
                var stepDuration = step.StepTime.TotalMinutes;
                var color = barColors[stepIndex % barColors.Length];

                // 处理温度数据
                var tempValue = GetParameterDisplayValueByColumn(step, "Temp Set(°C)");
                if (double.TryParse(tempValue.Replace("°C", "").Trim(), out double temperature))
                {
                    // 将温度值映射到Temp Set参数行的Y坐标范围内
                    double mappedY = MapTemperatureToRowY(temperature, tempSetY);
                    
                    // 添加步骤开始点和结束点，Y坐标使用映射后的值
                    tempPoints.Add(new DataPoint(currentTime, mappedY));
                    tempPoints.Add(new DataPoint(currentTime + stepDuration, mappedY));
                    
                    // 检查温度变化，如果变化则记录背景时间段
                    if (stepIndex > 0)
                    {
                        var prevStep = CurrentRecipeData.Steps[stepIndex - 1];
                        var prevTempValue = GetParameterDisplayValueByColumn(prevStep, "Temp Set(°C)");
                        if (double.TryParse(prevTempValue.Replace("°C", "").Trim(), out double prevTemp))
                        {
                            if (Math.Abs(temperature - prevTemp) > 0.1)
                            {
                                tempBackgrounds.Add(new Tuple<double, double>(currentTime, currentTime + stepDuration));
                            }
                        }
                    }
                }

                // 为每个参数行创建时间条（除了Temp Set，因为用折线图显示）
                for (int paramIndex = 0; paramIndex < parameters.Count; paramIndex++)
                {
                    var parameterName = parameters[paramIndex];
                    
                    // 跳过Temp Set参数，因为用折线图显示
                    if (parameterName == "Temp Set(°C)")
                        continue;
                        
                    var displayValue = GetParameterDisplayValueByColumn(step, parameterName);
                    
                    // 检查该步骤是否有该参数的值
                    if (!string.IsNullOrEmpty(displayValue) && displayValue != "0" && displayValue != "None")
                    {
                        // 只添加双箭头，不要矩形时间条
                        CreateDoubleArrow(plotModel, currentTime, currentTime + stepDuration, paramIndex, displayValue, parameters, stepIndex);
                    }
                }

                currentTime += stepDuration;
            }

            // 添加温度数据点到系列
            foreach (var point in tempPoints)
            {
                tempSeries.Points.Add(point);
            }
            plotModel.Series.Add(tempSeries);

            // 添加温度变化背景
            foreach (var background in tempBackgrounds)
            {
                AddTempSetBackground(plotModel, background.Item1, background.Item2);
            }

            // 添加温度变化箭头
            AddTemperatureChangeArrows(plotModel, tempPoints);

            // 添加步骤分隔线
            AddStepSeparators(plotModel);

            // 添加步骤时间和名称标注
            AddStepTimeAndNameLabels(plotModel);
        }

        /// <summary>
        /// 检查步骤是否有指定参数的值
        /// </summary>
        private bool HasParameterValue(RecipeStep step, int paramIndex)
        {
            // 根据参数索引检查对应的字段是否有值
            switch (paramIndex)
            {
                case 0: return !string.IsNullOrEmpty(step.SubRecipeName);
                case 1: return step.APC3Set > 0;
                case 2: return step.APC2Set > 0;
                case 3: return step.TempZone4Set > 0;
                case 4: return step.TempZone3Set > 0;
                case 5: return step.TempZone2Set > 0;
                case 6: return step.TempZone1Set > 0;
                case 7: return step.BoatRotateCommand > 0;
                case 8: return step.ElevatorCommand > 0;
                case 9: return !string.IsNullOrEmpty(step.CommandCode);
                default: return false;
            }
        }

        /// <summary>
        /// 获取参数显示值
        /// </summary>
        private string GetParameterDisplayValue(RecipeStep step, int paramIndex)
        {
            switch (paramIndex)
            {
                case 0: return step.SubRecipeName ?? "";
                case 1: return step.APC3Set.ToString();
                case 2: return step.APC2Set.ToString();
                case 3: return step.TempZone4Set.ToString();
                case 4: return step.TempZone3Set.ToString();
                case 5: return step.TempZone2Set.ToString();
                case 6: return step.TempZone1Set.ToString();
                case 7: return step.BoatRotateCommand.ToString();
                case 8: return step.ElevatorCommand.ToString();
                case 9: return step.CommandCode ?? "";
                default: return "";
            }
        }

        /// <summary>
        /// 创建完整的双箭头（支持直线和波动两种类型）
        /// </summary>
        private void CreateDoubleArrow(PlotModel plotModel, double startTime, double endTime, int paramIndex, string displayValue = "", List<string> parameters = null, int stepIndex = -1)
        {
            // 计算Y轴坐标：反转坐标，让索引0对应最顶部
            double y = parameters != null ? (parameters.Count - 1 - paramIndex) : paramIndex;
            double arrowLength = Math.Min(8, (endTime - startTime) / 6); // 箭头长度
            var lightBlue = OxyColor.FromRgb(173, 216, 230); // 淡蓝色

            // 检查是否为Temp Set参数
            bool isTempSet = parameters != null && IsTempSetParameter(paramIndex, parameters);
            
            // 根据参数类型决定使用折线双箭头还是直线双箭头
            if (isTempSet)
            {
                // 尝试解析温度值
                double currentTemp = 0;
                double prevTemp = 0;
                
                if (stepIndex >= 0 && CurrentRecipeData?.Steps != null && stepIndex < CurrentRecipeData.Steps.Count)
                {
                    // 获取当前步骤温度值
                    if (double.TryParse(displayValue.Replace("°C", "").Trim(), out currentTemp))
                    {
                        // 获取前一个步骤温度值
                        if (stepIndex > 0)
                        {
                            var prevStep = CurrentRecipeData.Steps[stepIndex - 1];
                            var prevValue = GetParameterDisplayValueByColumn(prevStep, "Temp Set(°C)");
                            double.TryParse(prevValue.Replace("°C", "").Trim(), out prevTemp);
                        }
                        else
                        {
                            prevTemp = currentTemp; // 第一个步骤
                        }
                    }
                }
                
                // Temp set数据使用斜着的双箭头（当数据变化时）
                // 获取Temp Set参数行的Y坐标
                double tempSetY = GetTempSetParameterYPosition(plotModel);
                // 将温度值映射到Temp Set参数行的Y坐标范围内
                double mappedStartY = MapTemperatureToRowY(prevTemp, tempSetY);
                double mappedEndY = MapTemperatureToRowY(currentTemp, tempSetY);
                AddSlantedArrow(plotModel, startTime, mappedStartY, endTime, mappedEndY, lightBlue);
            }
            else
            {
                // 其他数据使用直线双箭头
                AddStraightArrow(plotModel, startTime, endTime, y, lightBlue, arrowLength);
            }

            // 在中间添加参数值文本
            if (!string.IsNullOrEmpty(displayValue) && displayValue != "0")
            {
                double centerX = (startTime + endTime) / 2;
                var textAnnotation = new TextAnnotation
                {
                    Text = displayValue,
                    TextPosition = new DataPoint(centerX, y + 0.15), // 稍微向上偏移
                    TextColor = OxyColors.DarkBlue,
                    FontSize = 9,
                    FontWeight = OxyPlot.FontWeights.Bold,
                    TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Center,
                    TextVerticalAlignment = OxyPlot.VerticalAlignment.Middle,
                    Background = OxyColors.White,
                    Padding = new OxyThickness(2)
                };
                plotModel.Annotations.Add(textAnnotation);
            }
        }

        /// <summary>
        /// 根据列名获取参数显示值
        /// </summary>
        private string GetParameterDisplayValueByColumn(RecipeStep step, string columnName)
        {
            // 根据列名映射到对应的显示属性
            switch (columnName)
            {
                case "Condition":
                    return step.ConditionDisplay;
                case "Command":
                    return step.CommandDisplay;
                case "ElevatorCommand":
                    return step.ElevatorCommandDisplay;
                case "BoatRotateCommand":
                    return step.BoatRotateCommandDisplay;
                case "Temp Set(°C)":
                    return step.TempSetDisplay;
                case "Temp Rate(°C/min)":
                    return step.TempRateDisplay;
                case "Temp Mode":
                    return step.TempModeDisplay;
                case "Loop":
                    return step.LoopDisplay;
                case "APC2 Setting(mTorr)":
                    return step.APC2SettingDisplay;
                case "APC3 Setting(mTorr)":
                    return step.APC3SettingDisplay;
                case "SubRecipeName":
                    return step.SubRecipeNameDisplay;
                default:
                    // 如果没有找到对应的显示属性，尝试从原始字段获取
                    return GetParameterDisplayValueByField(step, columnName);
            }
        }

        /// <summary>
        /// 根据字段名获取参数显示值（备用方法）
        /// </summary>
        private string GetParameterDisplayValueByField(RecipeStep step, string fieldName)
        {
            // 根据字段名获取原始值
            switch (fieldName)
            {
                case "SubRecipeName":
                    return step.SubRecipeName ?? "";
                case "APC3Set":
                    return step.APC3Set.ToString();
                case "APC2Set":
                    return step.APC2Set.ToString();
                case "TempZone4Set":
                    return step.TempZone4Set.ToString();
                case "TempZone3Set":
                    return step.TempZone3Set.ToString();
                case "TempZone2Set":
                    return step.TempZone2Set.ToString();
                case "TempZone1Set":
                    return step.TempZone1Set.ToString();
                case "BoatRotateCommand":
                    return step.BoatRotateCommand.ToString();
                case "ElevatorCommand":
                    return step.ElevatorCommand.ToString();
                case "CommandCode":
                    return step.CommandCode ?? "";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 判断是否为Temp set参数（需要折线双箭头）
        /// </summary>
        private bool IsTempSetParameter(int paramIndex, List<string> parameters)
        {
            if (paramIndex >= 0 && paramIndex < parameters.Count)
            {
                var columnName = parameters[paramIndex];
                // 根据列名判断是否为温度设置参数
                return columnName == "Temp Set(°C)" || 
                       columnName == "TempZone1Set" || 
                       columnName == "TempZone2Set" || 
                       columnName == "TempZone3Set" || 
                       columnName == "TempZone4Set";
            }
            return false;
        }

        /// <summary>
        /// 判断是否应该使用波动箭头（只有参数值在时间段内有变化时才波动）
        /// </summary>
        private bool ShouldUseWaveArrow(int paramIndex, string displayValue)
        {
            // 如果参数值为空或为0，不使用波动箭头
            if (string.IsNullOrEmpty(displayValue) || displayValue == "0" || displayValue == "NONE")
                return false;

            // 检查是否是可能变化的参数类型
            bool isVariableParameter = false;

            // 温度相关参数可能波动 (TempZone1Set 到 TempZone4Set)
            if (paramIndex >= 6 && paramIndex <= 9)
                isVariableParameter = true;

            // 压力相关参数可能波动 (APC相关参数)
            if (paramIndex >= 1 && paramIndex <= 2)
                isVariableParameter = true;

            // 流量控制参数可能波动
            if (paramIndex >= 3 && paramIndex <= 5)
                isVariableParameter = true;

            // Command参数可能变化
            if (paramIndex == 10)
                isVariableParameter = true;

            // 只有可变参数且有实际值时才考虑波动
            if (!isVariableParameter)
                return false;

            // 进一步检查：如果是数值参数，值必须大于0才波动
            if (double.TryParse(displayValue, out double value))
            {
                return value > 0; // 只有大于0的数值才波动
            }

            // 文本参数（如Command状态）如果不是NONE就可能波动
            return displayValue != "NONE" && displayValue != "OFF";
        }

        /// <summary>
        /// 添加折线形波动箭头（类似截图中的样式）
        /// </summary>
        private void AddWaveArrow(PlotModel plotModel, double startTime, double endTime, double y, OxyColor color, double arrowLength)
        {
            // 折线波动参数
            double amplitude = 0.1; // 波动幅度
            int segments = Math.Max(3, (int)((endTime - startTime) / 20)); // 折线段数，根据时间长度调整

            // 创建折线波动路径点
            var wavePoints = new List<DataPoint>();
            double segmentWidth = (endTime - startTime - 2 * arrowLength) / segments;

            // 起始点
            wavePoints.Add(new DataPoint(startTime + arrowLength, y));

            // 创建锯齿状折线
            for (int i = 1; i <= segments; i++)
            {
                double x = startTime + arrowLength + i * segmentWidth;

                // 交替上下波动，创建锯齿效果
                double waveY = y + (i % 2 == 1 ? amplitude : -amplitude);
                wavePoints.Add(new DataPoint(x, waveY));
            }

            // 结束点回到基线
            wavePoints.Add(new DataPoint(endTime - arrowLength, y));

            // 创建折线波动线条
            var waveSeries = new LineSeries
            {
                Color = color,
                StrokeThickness = 2,
                LineStyle = LineStyle.Solid
            };

            foreach (var point in wavePoints)
            {
                waveSeries.Points.Add(point);
            }
            plotModel.Series.Add(waveSeries);

            // 添加左箭头
            var leftArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(startTime + arrowLength, y),
                EndPoint = new DataPoint(startTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(leftArrow);

            // 添加右箭头
            var rightArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(endTime - arrowLength, y),
                EndPoint = new DataPoint(endTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(rightArrow);
        }

        /// <summary>
        /// 添加直线箭头
        /// </summary>
        private void AddStraightArrow(PlotModel plotModel, double startTime, double endTime, double y, OxyColor color, double arrowLength)
        {
            // 左箭头 ◄
            var leftArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(startTime + arrowLength, y),
                EndPoint = new DataPoint(startTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(leftArrow);

            // 中间连接线 ─────
            var middleLine = new LineAnnotation
            {
                Type = LineAnnotationType.Horizontal,
                Y = y,
                MinimumX = startTime + arrowLength,
                MaximumX = endTime - arrowLength,
                Color = color,
                StrokeThickness = 2,
                LineStyle = LineStyle.Solid
            };
            plotModel.Annotations.Add(middleLine);

            // 右箭头 ►
            var rightArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(endTime - arrowLength, y),
                EndPoint = new DataPoint(endTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(rightArrow);
        }

        /// <summary>
        /// 添加步骤分隔线
        /// </summary>
        private void AddStepSeparators(PlotModel plotModel)
        {
            double currentTime = 0;

            for (int i = 0; i < CurrentRecipeData.Steps.Count; i++)
            {
                var step = CurrentRecipeData.Steps[i];
                currentTime += step.StepTime.TotalMinutes;

                // 添加垂直分隔线
                var separator = new LineAnnotation
                {
                    Type = LineAnnotationType.Vertical,
                    X = currentTime,
                    Color = OxyColors.Gray,
                    StrokeThickness = 1,
                    LineStyle = LineStyle.Dash
                };
                plotModel.Annotations.Add(separator);
            }
        }

        /// <summary>
        /// 添加步骤时间和名称标注
        /// </summary>
        private void AddStepTimeAndNameLabels(PlotModel plotModel)
        {
            double currentTime = 0;

            for (int i = 0; i < CurrentRecipeData.Steps.Count; i++)
            {
                var step = CurrentRecipeData.Steps[i];
                var stepDuration = step.StepTime.TotalMinutes;
                double centerX = currentTime + stepDuration / 2;

                // 添加步骤名称（在图表上方）
                var stepNameAnnotation = new TextAnnotation
                {
                    Text = step.StepName,
                    TextPosition = new DataPoint(centerX, 10.5), // 在最上方
                    TextColor = OxyColors.DarkBlue,
                    FontSize = 10,
                    FontWeight = OxyPlot.FontWeights.Bold,
                    TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Center,
                    TextVerticalAlignment = OxyPlot.VerticalAlignment.Middle,
                    Background = OxyColors.LightYellow,
                    Padding = new OxyThickness(3)
                };
                plotModel.Annotations.Add(stepNameAnnotation);

                // 添加步骤时间（在图表下方）
                var stepTimeText = string.Format("{0:F1}min", stepDuration);
                var stepTimeAnnotation = new TextAnnotation
                {
                    Text = stepTimeText,
                    TextPosition = new DataPoint(centerX, -1), // 在最下方
                    TextColor = OxyColors.DarkGreen,
                    FontSize = 9,
                    FontWeight = OxyPlot.FontWeights.Bold,
                    TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Center,
                    TextVerticalAlignment = OxyPlot.VerticalAlignment.Middle,
                    Background = OxyColors.LightGreen,
                    Padding = new OxyThickness(2)
                };
                plotModel.Annotations.Add(stepTimeAnnotation);

                currentTime += stepDuration;
            }
        }

        /// <summary>
        /// 为Temp Set参数添加橘红色垂直背景（覆盖整个变化时间段）
        /// </summary>
        private void AddTempSetBackground(PlotModel plotModel, double startTime, double endTime)
        {
            var orangeRed = OxyColor.FromRgb(255, 69, 0); // 橘红色
            
            // 获取Temp Set参数在Y轴上的位置
            double tempSetY = GetTempSetParameterYPosition(plotModel);
            
            var backgroundRect = new RectangleAnnotation
            {
                MinimumX = startTime,
                MaximumX = endTime,
                MinimumY = tempSetY - 0.5,
                MaximumY = tempSetY + 0.5,
                Fill = OxyColor.FromArgb(50, orangeRed.R, orangeRed.G, orangeRed.B), // 半透明
                Stroke = OxyColors.Transparent,
                Layer = AnnotationLayer.BelowSeries
            };
            plotModel.Annotations.Add(backgroundRect);
        }

        /// <summary>
        /// 获取Temp Set参数在Y轴上的位置
        /// </summary>
        private double GetTempSetParameterYPosition(PlotModel plotModel)
        {
            // 查找左侧类别轴
            var categoryAxis = plotModel.Axes.FirstOrDefault(a => a.Position == AxisPosition.Left) as CategoryAxis;
            if (categoryAxis != null)
            {
                // 查找"Temp Set(°C)"在标签中的位置
                for (int i = 0; i < categoryAxis.Labels.Count; i++)
                {
                    if (categoryAxis.Labels[i] == "Temp Set(°C)")
                    {
                        return i;
                    }
                }
            }
            return 0; // 默认返回第一个位置
        }

        /// <summary>
        /// 将温度值映射到Temp Set参数行的Y坐标范围内
        /// </summary>
        /// <param name="temperature">温度值</param>
        /// <param name="rowY">Temp Set参数行的Y坐标</param>
        /// <returns>映射后的Y坐标</returns>
        private double MapTemperatureToRowY(double temperature, double rowY)
        {
            // 将温度值(0-1200°C)映射到Temp Set行的Y坐标范围内(rowY-0.4 到 rowY+0.4)
            double minTemp = 0;
            double maxTemp = 1200;
            double rowHeight = 0.8; // 行高度的一半
            
            // 确保温度值在有效范围内
            temperature = Math.Max(minTemp, Math.Min(maxTemp, temperature));
            
            // 线性映射：温度值映射到行内的相对位置
            double ratio = (temperature - minTemp) / (maxTemp - minTemp);
            double mappedY = rowY - rowHeight/2 + ratio * rowHeight;
            
            return mappedY;
        }

        /// <summary>
        /// 添加斜着的双箭头连接温度变化点
        /// </summary>
        private void AddSlantedArrow(PlotModel plotModel, double startTime, double startY, double endTime, double endY, OxyColor color)
        {
            // 创建第一个箭头（起点到终点）
            var arrow1 = new ArrowAnnotation
            {
                StartPoint = new DataPoint(startTime, startY),
                EndPoint = new DataPoint(endTime, endY),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 8,
                HeadWidth = 5
            };
            
            // 创建第二个箭头（终点到起点）
            var arrow2 = new ArrowAnnotation
            {
                StartPoint = new DataPoint(endTime, endY),
                EndPoint = new DataPoint(startTime, startY),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 8,
                HeadWidth = 5
            };
            
            plotModel.Annotations.Add(arrow1);
            plotModel.Annotations.Add(arrow2);
        }

        /// <summary>
        /// 添加温度变化箭头
        /// </summary>
        private void AddTemperatureChangeArrows(PlotModel plotModel, List<DataPoint> tempPoints)
        {
            for (int i = 1; i < tempPoints.Count; i += 2)
            {
                if (i + 1 < tempPoints.Count)
                {
                    var startPoint = tempPoints[i];     // 当前步骤结束点
                    var endPoint = tempPoints[i + 1];   // 下一步骤开始点
                    
                    // 只在温度变化时添加箭头，Y坐标使用映射后的温度值
                    if (Math.Abs(startPoint.Y - endPoint.Y) > 0.1)
                    {
                        var orangeRed = OxyColor.FromRgb(255, 69, 0); // 橘红色
                        AddSlantedArrow(plotModel, startPoint.X, startPoint.Y, endPoint.X, endPoint.Y, orangeRed);
                    }
                }
            }
        }

        #endregion

        #region 测试功能

        /// <summary>
        /// 加载测试文件（开发测试用）
        /// </summary>
        private void LoadTestFiles()
        {
            try
            {
                // 获取应用程序目录
                string appDir = AppDomain.CurrentDomain.BaseDirectory;

                // 测试CSV文件路径
                string testCsvPath = Path.Combine(appDir, "main_recipe_sample.csv");

                // 测试配置文件路径
                string testConfigPath = Path.Combine(appDir, "RecipeConfig.json");

                // 检查并加载CSV文件
                if (File.Exists(testCsvPath))
                {
                    SelectedRecipeFile = testCsvPath;
                    StatusMessage = "已自动加载测试CSV文件: " + Path.GetFileName(testCsvPath);

                    // 自动解析
                    if (CanParseRecipe())
                    {
                        ParseRecipe();
                        StatusMessage += " | 已自动解析完成";

                        // 自动生成图表
                        if (CanGenerateChart())
                        {
                            GenerateChart();
                            StatusMessage += " | 已自动生成流程图";
                        }
                    }
                }
                else
                {
                    StatusMessage = "测试CSV文件不存在: " + testCsvPath;
                }

                // 检查并加载配置文件
                if (File.Exists(testConfigPath))
                {
                    SelectedConfigFile = "RecipeConfig.json (测试)";
                    StatusMessage += " | 已加载测试配置";
                }
                else
                {
                    StatusMessage += " | 测试配置文件不存在";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "加载测试文件失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 重置测试环境
        /// </summary>
        public void ResetTestEnvironment()
        {
            try
            {
                // 清除当前数据
                ClearData();

                // 重新加载测试文件
                LoadTestFiles();

                StatusMessage = "测试环境已重置";
            }
            catch (Exception ex)
            {
                StatusMessage = "重置测试环境失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 快速测试流程
        /// </summary>
        public void QuickTest()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedRecipeFile))
                {
                    LoadTestFiles();
                    return;
                }

                // 如果已有文件，直接解析和生成图表
                if (CanParseRecipe())
                {
                    ParseRecipe();

                    if (CanGenerateChart())
                    {
                        GenerateChart();
                        StatusMessage = "快速测试完成 - 流程图已生成";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "快速测试失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 判断温度是否发生变化
        /// </summary>
        private bool IsTemperatureChanged(string displayValue, int stepIndex)
        {
            if (string.IsNullOrEmpty(displayValue))
                return false;
                
            // 尝试解析当前步骤的温度值
            if (double.TryParse(displayValue.Replace("°C", "").Trim(), out double currentTemp))
            {
                // 如果有前一个步骤，比较温度变化
                if (stepIndex > 0 && CurrentRecipeData?.Steps != null && stepIndex < CurrentRecipeData.Steps.Count)
                {
                    var previousStep = CurrentRecipeData.Steps[stepIndex - 1];
                    var previousTempValue = GetParameterDisplayValueByColumn(previousStep, "Temp Set(°C)");
                    
                    if (double.TryParse(previousTempValue.Replace("°C", "").Trim(), out double previousTemp))
                    {
                        return Math.Abs(currentTemp - previousTemp) > 0.1; // 温度变化超过0.1度
                    }
                }
                
                // 如果没有前一个步骤，默认认为有变化（第一个步骤）
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// 判断温度是否在上升
        /// </summary>
        private bool IsTemperatureIncreasing(string displayValue, int stepIndex)
        {
            if (string.IsNullOrEmpty(displayValue))
                return false;
                
            // 尝试解析当前步骤的温度值
            if (double.TryParse(displayValue.Replace("°C", "").Trim(), out double currentTemp))
            {
                // 如果有前一个步骤，比较温度变化
                if (stepIndex > 0 && CurrentRecipeData?.Steps != null && stepIndex < CurrentRecipeData.Steps.Count)
                {
                    var previousStep = CurrentRecipeData.Steps[stepIndex - 1];
                    var previousTempValue = GetParameterDisplayValueByColumn(previousStep, "Temp Set(°C)");
                    
                    if (double.TryParse(previousTempValue.Replace("°C", "").Trim(), out double previousTemp))
                    {
                        return currentTemp > previousTemp; // 当前温度高于前一步骤温度，表示上升
                    }
                }
                
                // 如果没有前一个步骤或无法比较，根据温度值判断趋势
                // 如果温度值大于800度，通常表示在加热阶段（上升）
                return currentTemp > 800;
            }
            
            return false;
        }

        #endregion
    }
}
