using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using DatalogDrawing.Models;
using DatalogDrawing.Services;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using OxyPlot.Annotations;

namespace DatalogDrawing.ViewModels
{
    /// <summary>
    /// 参数项类，替代元组
    /// </summary>
    public class ParameterItem
    {
        public string Name { get; set; }
        public string Value { get; set; }

        public ParameterItem(string name, string value)
        {
            Name = name;
            Value = value;
        }
    }

    public class RecipeViewModel : ViewModelBase
    {
        private readonly RecipeParserService _parserService;
        private readonly RecipeConfigService _configService;

        // 命令
        public ICommand SelectRecipeFileCommand { get; private set; }
        public ICommand ParseRecipeCommand { get; private set; }
        public ICommand GenerateChartCommand { get; private set; }
        public ICommand ClearDataCommand { get; private set; }
        public ICommand SelectConfigCommand { get; private set; }

        // 测试命令
        public ICommand ResetTestCommand { get; private set; }
        public ICommand QuickTestCommand { get; private set; }

        // 属性
        private string _selectedRecipeFile;
        public string SelectedRecipeFile
        {
            get => _selectedRecipeFile;
            set
            {
                if (SetProperty(ref _selectedRecipeFile, value, "SelectedRecipeFile"))
                {
                    // 触发命令状态更新
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        private string _selectedConfigFile;
        public string SelectedConfigFile
        {
            get => _selectedConfigFile;
            set => SetProperty(ref _selectedConfigFile, value, "SelectedConfigFile");
        }

        private RecipeType _selectedRecipeType = RecipeType.MainRecipe;
        public RecipeType SelectedRecipeType
        {
            get => _selectedRecipeType;
            set => SetProperty(ref _selectedRecipeType, value, "SelectedRecipeType");
        }

        public int SelectedRecipeTypeIndex
        {
            get => (int)SelectedRecipeType;
            set
            {
                SelectedRecipeType = (RecipeType)value;
                OnPropertyChanged("RecipeTypeDisplay");
            }
        }

        private RecipeData _currentRecipeData;
        public RecipeData CurrentRecipeData
        {
            get => _currentRecipeData;
            set => SetProperty(ref _currentRecipeData, value, "CurrentRecipeData");
        }

        private ObservableCollection<RecipeStep> _recipeSteps = new ObservableCollection<RecipeStep>();
        public ObservableCollection<RecipeStep> RecipeSteps
        {
            get => _recipeSteps;
            set => SetProperty(ref _recipeSteps, value, "RecipeSteps");
        }

        private PlotModel _processTimelineChart;
        public PlotModel ProcessTimelineChart
        {
            get => _processTimelineChart;
            set => SetProperty(ref _processTimelineChart, value, "ProcessTimelineChart");
        }

        private string _statusMessage = "请选择Recipe文件";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value, "StatusMessage");
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value, "IsBusy");
        }

        // 统计信息
        public string TotalTimeDisplay => CurrentRecipeData?.TotalTime.ToString(@"hh\:mm\:ss") ?? "00:00:00";
        public int StepCountDisplay => CurrentRecipeData?.StepCount ?? 0;
        public string RecipeTypeDisplay => SelectedRecipeType == RecipeType.MainRecipe ? "主配方" : "子配方";

        public RecipeViewModel()
        {
            _configService = new RecipeConfigService();
            _parserService = new RecipeParserService(_configService);
            InitializeCommands();
            InitializeChart();
            LoadDefaultConfig();

            // 测试模式：自动加载测试文件
            LoadTestFiles();
        }

        private void InitializeCommands()
        {
            SelectRecipeFileCommand = new RelayCommand(SelectRecipeFile);
            ParseRecipeCommand = new RelayCommand(ParseRecipe, CanParseRecipe);
            GenerateChartCommand = new RelayCommand(GenerateChart, CanGenerateChart);
            ClearDataCommand = new RelayCommand(ClearData);
            SelectConfigCommand = new RelayCommand(SelectConfig);

            // 测试命令
            ResetTestCommand = new RelayCommand(ResetTestEnvironment);
            QuickTestCommand = new RelayCommand(QuickTest);
        }

        private void LoadDefaultConfig()
        {
            try
            {
                _configService.LoadDefaultConfig();
                SelectedConfigFile = "RecipeConfig.json (默认)";
                StatusMessage = "已加载默认Recipe配置";
            }
            catch (Exception ex)
            {
                StatusMessage = "加载配置失败: " + ex.Message;
            }
        }

        private void InitializeChart()
        {
            ProcessTimelineChart = new PlotModel
            {
                Title = "工艺时序图",
                Background = OxyColors.White
            };
        }

        #region 命令实现

        private void SelectRecipeFile()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
                Title = "选择Recipe文件",
                InitialDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug")
            };

            if (openFileDialog.ShowDialog() == true)
            {
                SelectedRecipeFile = openFileDialog.FileName;

                // 根据文件名自动判断Recipe类型
                var fileName = Path.GetFileName(SelectedRecipeFile).ToLower();
                if (fileName.Contains("sub") || fileName.Contains("子"))
                {
                    SelectedRecipeType = RecipeType.SubRecipe;
                }
                else
                {
                    SelectedRecipeType = RecipeType.MainRecipe;
                }

                StatusMessage = "已选择文件: " + Path.GetFileName(SelectedRecipeFile);
                OnPropertyChanged("RecipeTypeDisplay");
            }
        }

        private bool CanParseRecipe()
        {
            return !string.IsNullOrEmpty(SelectedRecipeFile) && File.Exists(SelectedRecipeFile) && !IsBusy;
        }

        private void ParseRecipe()
        {
            if (!CanParseRecipe()) return;

            IsBusy = true;
            StatusMessage = "正在解析Recipe文件...";

            try
            {
                // 直接同步解析，避免async/await复杂性
                CurrentRecipeData = _parserService.ParseRecipeFile(SelectedRecipeFile);

                // 更新步骤集合
                RecipeSteps.Clear();
                foreach (var step in CurrentRecipeData.Steps)
                {
                    RecipeSteps.Add(step);
                }

                // 更新统计信息显示
                OnPropertyChanged("TotalTimeDisplay");
                OnPropertyChanged("StepCountDisplay");

                StatusMessage = string.Format("解析完成，共{0}个步骤，总时间{1}", CurrentRecipeData.StepCount, TotalTimeDisplay);
            }
            catch (Exception ex)
            {
                StatusMessage = "解析失败: " + ex.Message;
                MessageBox.Show("解析Recipe文件时出错:\n" + ex.Message, "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private bool CanGenerateChart()
        {
            return CurrentRecipeData != null && CurrentRecipeData.Steps.Count > 0 && !IsBusy;
        }

        private void GenerateChart()
        {
            if (!CanGenerateChart()) return;

            IsBusy = true;
            StatusMessage = "正在生成工艺时序图...";

            try
            {
                ProcessTimelineChart = CreateProcessTimelineChart();
                StatusMessage = "工艺时序图生成完成";
            }
            catch (Exception ex)
            {
                StatusMessage = "生成图表失败: " + ex.Message;
                MessageBox.Show("生成工艺时序图时出错:\n" + ex.Message, "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ClearData()
        {
            CurrentRecipeData = null;
            RecipeSteps.Clear();
            SelectedRecipeFile = "";
            ProcessTimelineChart = new PlotModel { Title = "工艺时序图", Background = OxyColors.White };
            StatusMessage = "数据已清空，请选择Recipe文件";

            OnPropertyChanged("TotalTimeDisplay");
            OnPropertyChanged("StepCountDisplay");
        }

        private void SelectConfig()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "JSON Config Files (*.json)|*.json|All Files (*.*)|*.*",
                Title = "选择Recipe配置文件",
                InitialDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Debug")
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _configService.LoadConfig(openFileDialog.FileName);
                    SelectedConfigFile = Path.GetFileName(openFileDialog.FileName);
                    StatusMessage = "已加载配置: " + SelectedConfigFile;
                }
                catch (Exception ex)
                {
                    StatusMessage = "加载配置失败: " + ex.Message;
                    MessageBox.Show("加载配置文件时出错:\n" + ex.Message, "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #endregion

        #region 图表生成

        /// <summary>
        /// 创建工艺流程图（甘特图样式）
        /// </summary>
        private PlotModel CreateProcessTimelineChart()
        {
            var plotModel = new PlotModel
            {
                Title = RecipeTypeDisplay + " - 工艺流程图",
                Background = OxyColors.White,
                PlotAreaBorderColor = OxyColors.Black,
                PlotAreaBorderThickness = new OxyThickness(1),
                PlotMargins = new OxyThickness(150, 40, 40, 60)
            };

            // 添加时间轴（X轴）
            var timeAxis = new LinearAxis
            {
                Position = AxisPosition.Bottom,
                Title = "时间 (分钟)",
                MajorGridlineStyle = LineStyle.Solid,
                MajorGridlineColor = OxyColors.LightGray,
                MinorGridlineStyle = LineStyle.Dot,
                MinorGridlineColor = OxyColors.LightGray,
                Minimum = 0,
                Maximum = CurrentRecipeData.TotalTime.TotalMinutes * 1.1
            };
            plotModel.Axes.Add(timeAxis);

            // 添加参数轴（Y轴）
            var parameterAxis = new CategoryAxis
            {
                Position = AxisPosition.Left,
                Title = "工艺参数",
                Key = "ParameterAxis",
                MajorGridlineStyle = LineStyle.Solid,
                MajorGridlineColor = OxyColors.LightGray
            };

            // 按截图顺序添加参数标签（从下到上）
            var parameters = new[]
            {
                "SubRecipeName",
                "APC3Set",
                "APC2Set",
                "TempZone4Set",
                "TempZone3Set",
                "TempZone2Set",
                "TempZone1Set",
                "BoatRotateCommand",
                "ElevatorCommand",
                "Command"
            };

            foreach (var param in parameters)
            {
                parameterAxis.Labels.Add(param);
            }
            plotModel.Axes.Add(parameterAxis);

            // 创建甘特图时间条
            CreateProcessFlowNodes(plotModel);

            return plotModel;
        }

        /// <summary>
        /// 创建甘特图时间条
        /// </summary>
        private void CreateProcessFlowNodes(PlotModel plotModel)
        {
            if (CurrentRecipeData?.Steps == null || CurrentRecipeData.Steps.Count == 0)
                return;

            // 时间条颜色
            var barColors = new[]
            {
                OxyColors.LightBlue,
                OxyColors.LightGreen,
                OxyColors.Orange,
                OxyColors.LightCoral,
                OxyColors.LightPink,
                OxyColors.LightYellow,
                OxyColors.LightCyan,
                OxyColors.Wheat,
                OxyColors.LightSalmon
            };

            double currentTime = 0;

            // 为每个步骤创建时间条
            for (int stepIndex = 0; stepIndex < CurrentRecipeData.Steps.Count; stepIndex++)
            {
                var step = CurrentRecipeData.Steps[stepIndex];
                var stepDuration = step.StepTime.TotalMinutes;
                var color = barColors[stepIndex % barColors.Length];

                // 为每个参数行创建时间条
                for (int paramIndex = 0; paramIndex < 10; paramIndex++) // 10个参数行
                {
                    // 检查该步骤是否有该参数的值
                    if (HasParameterValue(step, paramIndex))
                    {
                        // 只添加双箭头，不要矩形时间条
                        CreateDoubleArrow(plotModel, currentTime, currentTime + stepDuration, paramIndex,
                                        GetParameterDisplayValue(step, paramIndex));
                    }
                }

                currentTime += stepDuration;
            }

            // 添加步骤分隔线
            AddStepSeparators(plotModel);

            // 添加步骤时间和名称标注
            AddStepTimeAndNameLabels(plotModel);
        }

        /// <summary>
        /// 检查步骤是否有指定参数的值
        /// </summary>
        private bool HasParameterValue(RecipeStep step, int paramIndex)
        {
            // 根据参数索引检查对应的字段是否有值
            switch (paramIndex)
            {
                case 0: return !string.IsNullOrEmpty(step.SubRecipeName);
                case 1: return step.APC3Set > 0;
                case 2: return step.APC2Set > 0;
                case 3: return step.TempZone4Set > 0;
                case 4: return step.TempZone3Set > 0;
                case 5: return step.TempZone2Set > 0;
                case 6: return step.TempZone1Set > 0;
                case 7: return step.BoatRotateCommand > 0;
                case 8: return step.ElevatorCommand > 0;
                case 9: return !string.IsNullOrEmpty(step.CommandCode);
                default: return false;
            }
        }

        /// <summary>
        /// 获取参数显示值
        /// </summary>
        private string GetParameterDisplayValue(RecipeStep step, int paramIndex)
        {
            switch (paramIndex)
            {
                case 0: return step.SubRecipeName ?? "";
                case 1: return step.APC3Set.ToString();
                case 2: return step.APC2Set.ToString();
                case 3: return step.TempZone4Set.ToString();
                case 4: return step.TempZone3Set.ToString();
                case 5: return step.TempZone2Set.ToString();
                case 6: return step.TempZone1Set.ToString();
                case 7: return step.BoatRotateCommand.ToString();
                case 8: return step.ElevatorCommand.ToString();
                case 9: return step.CommandCode ?? "";
                default: return "";
            }
        }

        /// <summary>
        /// 创建完整的双箭头（支持直线和波动两种类型）
        /// </summary>
        private void CreateDoubleArrow(PlotModel plotModel, double startTime, double endTime, int paramIndex, string displayValue = "")
        {
            double y = paramIndex;
            double arrowLength = Math.Min(8, (endTime - startTime) / 6); // 箭头长度
            var lightBlue = OxyColor.FromRgb(173, 216, 230); // 淡蓝色

            // 统一使用直线箭头
            AddStraightArrow(plotModel, startTime, endTime, y, lightBlue, arrowLength);

            // 在中间添加参数值文本
            if (!string.IsNullOrEmpty(displayValue) && displayValue != "0")
            {
                double centerX = (startTime + endTime) / 2;
                var textAnnotation = new TextAnnotation
                {
                    Text = displayValue,
                    TextPosition = new DataPoint(centerX, y + 0.15), // 稍微向上偏移
                    TextColor = OxyColors.DarkBlue,
                    FontSize = 9,
                    FontWeight = OxyPlot.FontWeights.Bold,
                    TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Center,
                    TextVerticalAlignment = OxyPlot.VerticalAlignment.Middle,
                    Background = OxyColors.White,
                    Padding = new OxyThickness(2)
                };
                plotModel.Annotations.Add(textAnnotation);
            }
        }

        /// <summary>
        /// 判断是否应该使用波动箭头（只有参数值在时间段内有变化时才波动）
        /// </summary>
        private bool ShouldUseWaveArrow(int paramIndex, string displayValue)
        {
            // 如果参数值为空或为0，不使用波动箭头
            if (string.IsNullOrEmpty(displayValue) || displayValue == "0" || displayValue == "NONE")
                return false;

            // 检查是否是可能变化的参数类型
            bool isVariableParameter = false;

            // 温度相关参数可能波动 (TempZone1Set 到 TempZone4Set)
            if (paramIndex >= 6 && paramIndex <= 9)
                isVariableParameter = true;

            // 压力相关参数可能波动 (APC相关参数)
            if (paramIndex >= 1 && paramIndex <= 2)
                isVariableParameter = true;

            // 流量控制参数可能波动
            if (paramIndex >= 3 && paramIndex <= 5)
                isVariableParameter = true;

            // Command参数可能变化
            if (paramIndex == 10)
                isVariableParameter = true;

            // 只有可变参数且有实际值时才考虑波动
            if (!isVariableParameter)
                return false;

            // 进一步检查：如果是数值参数，值必须大于0才波动
            if (double.TryParse(displayValue, out double value))
            {
                return value > 0; // 只有大于0的数值才波动
            }

            // 文本参数（如Command状态）如果不是NONE就可能波动
            return displayValue != "NONE" && displayValue != "OFF";
        }

        /// <summary>
        /// 添加折线形波动箭头（类似截图中的样式）
        /// </summary>
        private void AddWaveArrow(PlotModel plotModel, double startTime, double endTime, double y, OxyColor color, double arrowLength)
        {
            // 折线波动参数
            double amplitude = 0.1; // 波动幅度
            int segments = Math.Max(3, (int)((endTime - startTime) / 20)); // 折线段数，根据时间长度调整

            // 创建折线波动路径点
            var wavePoints = new List<DataPoint>();
            double segmentWidth = (endTime - startTime - 2 * arrowLength) / segments;

            // 起始点
            wavePoints.Add(new DataPoint(startTime + arrowLength, y));

            // 创建锯齿状折线
            for (int i = 1; i <= segments; i++)
            {
                double x = startTime + arrowLength + i * segmentWidth;

                // 交替上下波动，创建锯齿效果
                double waveY = y + (i % 2 == 1 ? amplitude : -amplitude);
                wavePoints.Add(new DataPoint(x, waveY));
            }

            // 结束点回到基线
            wavePoints.Add(new DataPoint(endTime - arrowLength, y));

            // 创建折线波动线条
            var waveSeries = new LineSeries
            {
                Color = color,
                StrokeThickness = 2,
                LineStyle = LineStyle.Solid
            };

            foreach (var point in wavePoints)
            {
                waveSeries.Points.Add(point);
            }
            plotModel.Series.Add(waveSeries);

            // 添加左箭头
            var leftArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(startTime + arrowLength, y),
                EndPoint = new DataPoint(startTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(leftArrow);

            // 添加右箭头
            var rightArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(endTime - arrowLength, y),
                EndPoint = new DataPoint(endTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(rightArrow);
        }

        /// <summary>
        /// 添加直线箭头
        /// </summary>
        private void AddStraightArrow(PlotModel plotModel, double startTime, double endTime, double y, OxyColor color, double arrowLength)
        {
            // 左箭头 ◄
            var leftArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(startTime + arrowLength, y),
                EndPoint = new DataPoint(startTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(leftArrow);

            // 中间连接线 ─────
            var middleLine = new LineAnnotation
            {
                Type = LineAnnotationType.Horizontal,
                Y = y,
                MinimumX = startTime + arrowLength,
                MaximumX = endTime - arrowLength,
                Color = color,
                StrokeThickness = 2,
                LineStyle = LineStyle.Solid
            };
            plotModel.Annotations.Add(middleLine);

            // 右箭头 ►
            var rightArrow = new ArrowAnnotation
            {
                StartPoint = new DataPoint(endTime - arrowLength, y),
                EndPoint = new DataPoint(endTime, y),
                Color = color,
                StrokeThickness = 2,
                HeadLength = 6,
                HeadWidth = 4
            };
            plotModel.Annotations.Add(rightArrow);
        }

        /// <summary>
        /// 添加步骤分隔线
        /// </summary>
        private void AddStepSeparators(PlotModel plotModel)
        {
            double currentTime = 0;

            for (int i = 0; i < CurrentRecipeData.Steps.Count; i++)
            {
                var step = CurrentRecipeData.Steps[i];
                currentTime += step.StepTime.TotalMinutes;

                // 添加垂直分隔线
                var separator = new LineAnnotation
                {
                    Type = LineAnnotationType.Vertical,
                    X = currentTime,
                    Color = OxyColors.Gray,
                    StrokeThickness = 1,
                    LineStyle = LineStyle.Dash
                };
                plotModel.Annotations.Add(separator);
            }
        }

        /// <summary>
        /// 添加步骤时间和名称标注
        /// </summary>
        private void AddStepTimeAndNameLabels(PlotModel plotModel)
        {
            double currentTime = 0;

            for (int i = 0; i < CurrentRecipeData.Steps.Count; i++)
            {
                var step = CurrentRecipeData.Steps[i];
                var stepDuration = step.StepTime.TotalMinutes;
                double centerX = currentTime + stepDuration / 2;

                // 添加步骤名称（在图表上方）
                var stepNameAnnotation = new TextAnnotation
                {
                    Text = step.StepName,
                    TextPosition = new DataPoint(centerX, 10.5), // 在最上方
                    TextColor = OxyColors.DarkBlue,
                    FontSize = 10,
                    FontWeight = OxyPlot.FontWeights.Bold,
                    TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Center,
                    TextVerticalAlignment = OxyPlot.VerticalAlignment.Middle,
                    Background = OxyColors.LightYellow,
                    Padding = new OxyThickness(3)
                };
                plotModel.Annotations.Add(stepNameAnnotation);

                // 添加步骤时间（在图表下方）
                var stepTimeText = string.Format("{0:F1}min", stepDuration);
                var stepTimeAnnotation = new TextAnnotation
                {
                    Text = stepTimeText,
                    TextPosition = new DataPoint(centerX, -1), // 在最下方
                    TextColor = OxyColors.DarkGreen,
                    FontSize = 9,
                    FontWeight = OxyPlot.FontWeights.Bold,
                    TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Center,
                    TextVerticalAlignment = OxyPlot.VerticalAlignment.Middle,
                    Background = OxyColors.LightGreen,
                    Padding = new OxyThickness(2)
                };
                plotModel.Annotations.Add(stepTimeAnnotation);

                currentTime += stepDuration;
            }
        }





        #endregion

        #region 测试功能

        /// <summary>
        /// 加载测试文件（开发测试用）
        /// </summary>
        private void LoadTestFiles()
        {
            try
            {
                // 获取应用程序目录
                string appDir = AppDomain.CurrentDomain.BaseDirectory;

                // 测试CSV文件路径
                string testCsvPath = Path.Combine(appDir, "main_recipe_sample.csv");

                // 测试配置文件路径
                string testConfigPath = Path.Combine(appDir, "RecipeConfig.json");

                // 检查并加载CSV文件
                if (File.Exists(testCsvPath))
                {
                    SelectedRecipeFile = testCsvPath;
                    StatusMessage = "已自动加载测试CSV文件: " + Path.GetFileName(testCsvPath);

                    // 自动解析
                    if (CanParseRecipe())
                    {
                        ParseRecipe();
                        StatusMessage += " | 已自动解析完成";

                        // 自动生成图表
                        if (CanGenerateChart())
                        {
                            GenerateChart();
                            StatusMessage += " | 已自动生成流程图";
                        }
                    }
                }
                else
                {
                    StatusMessage = "测试CSV文件不存在: " + testCsvPath;
                }

                // 检查并加载配置文件
                if (File.Exists(testConfigPath))
                {
                    SelectedConfigFile = "RecipeConfig.json (测试)";
                    StatusMessage += " | 已加载测试配置";
                }
                else
                {
                    StatusMessage += " | 测试配置文件不存在";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "加载测试文件失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 重置测试环境
        /// </summary>
        public void ResetTestEnvironment()
        {
            try
            {
                // 清除当前数据
                ClearData();

                // 重新加载测试文件
                LoadTestFiles();

                StatusMessage = "测试环境已重置";
            }
            catch (Exception ex)
            {
                StatusMessage = "重置测试环境失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 快速测试流程
        /// </summary>
        public void QuickTest()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedRecipeFile))
                {
                    LoadTestFiles();
                    return;
                }

                // 如果已有文件，直接解析和生成图表
                if (CanParseRecipe())
                {
                    ParseRecipe();

                    if (CanGenerateChart())
                    {
                        GenerateChart();
                        StatusMessage = "快速测试完成 - 流程图已生成";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "快速测试失败: " + ex.Message;
            }
        }

        #endregion
    }
}