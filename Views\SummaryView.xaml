<UserControl x:Class="DatalogDrawing.Views.SummaryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:oxy="http://oxyplot.org/wpf"
             xmlns:vm="clr-namespace:DatalogDrawing.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Content="刷新会话列表" Command="{Binding RefreshCommand}" Margin="0,0,10,0" Padding="5"/>
        </StackPanel>

        <!-- Tab控件 -->
        <TabControl Grid.Row="1">
            <!-- 膜厚数据Tab -->
            <TabItem Header="📊 膜厚数据">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 膜厚数据会话列表 -->
                    <DataGrid Grid.Row="0"
                              ItemsSource="{Binding FilmThicknessSessions}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              SelectionMode="Single"
                              SelectedItem="{Binding SelectedFilmThicknessSession}"
                              Name="FilmThicknessSessionsGrid"
                              Margin="0,0,0,10"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Auto"
                              HeadersVisibility="Column">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Binding="{Binding [Id]}" Width="50"/>
                            <DataGridTextColumn Header="文件路径" Binding="{Binding [ParsedFilePath]}" Width="*"/>
                            <DataGridTextColumn Header="用户输入值" Binding="{Binding [UserInputValue]}" Width="100"/>
                            <DataGridTextColumn Header="解析时间" Binding="{Binding [Timestamp], StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                            <DataGridTemplateColumn Header="操作" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="删除"
                                                    Click="DeleteFilmThicknessButton_Click"
                                                    Tag="{Binding}"
                                                    Width="50"
                                                    Height="25"
                                                    Margin="5"/>
                                            <Button Content="预览"
                                                    Click="PreviewFilmThicknessButton_Click"
                                                    Tag="{Binding}"
                                                    Width="50"
                                                    Height="25"
                                                    Margin="5"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 膜厚数据对比选择 -->
                    <Grid Grid.Row="1" Margin="0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="选择会话1:" Margin="0,0,5,0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="1"
                                  ItemsSource="{Binding FilmThicknessSessions}"
                                  SelectedItem="{Binding SelectedFilmThicknessSession1}"
                                  Margin="0,0,10,0">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <Run Text="{Binding [Id], StringFormat='ID: {0}'}" />
                                        <Run Text=" - " />
                                        <Run Text="{Binding [ParsedFilePath]}" />
                                    </TextBlock>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <TextBlock Grid.Column="2" Text="选择会话2:" Margin="0,0,5,0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="3"
                                  ItemsSource="{Binding FilmThicknessSessions}"
                                  SelectedItem="{Binding SelectedFilmThicknessSession2}"
                                  Margin="0,0,10,0">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <Run Text="{Binding [Id], StringFormat='ID: {0}'}" />
                                        <Run Text=" - " />
                                        <Run Text="{Binding [ParsedFilePath]}" />
                                    </TextBlock>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <Button Grid.Column="4" Content="对比膜厚" Command="{Binding CompareFilmThicknessCommand}" Padding="10,5"/>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- 颗粒数据Tab -->
            <TabItem Header="🔬 颗粒数据">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="200"/>
                    </Grid.RowDefinitions>

                    <!-- 颗粒数据会话列表 -->
                    <DataGrid Grid.Row="0"
                              ItemsSource="{Binding PADataSessions}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              SelectionMode="Single"
                              SelectedItem="{Binding SelectedPADataSession}"
                              Name="PADataSessionsGrid"
                              Margin="0,0,0,10"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Auto"
                              HeadersVisibility="Column">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Binding="{Binding [Id]}" Width="50"/>
                            <DataGridTextColumn Header="文件路径" Binding="{Binding [ParsedFilePath]}" Width="*"/>

                            <!-- 颗粒图显示列 -->
                            <DataGridTemplateColumn Header="颗粒分布图" Width="450">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid Height="120" Margin="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Pre 图表 -->
                                            <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="1"
                                                    Background="Black" CornerRadius="3">
                                                <Grid>
                                                    <Ellipse Fill="Black" Stroke="White" StrokeThickness="1"
                                                             Margin="3" x:Name="PreCircle"/>
                                                    <Canvas x:Name="PreCanvas" Margin="3" Tag="PreCanvas"/>
                                                    <TextBlock Text="Pre" Foreground="White" FontWeight="Bold"
                                                               HorizontalAlignment="Center" VerticalAlignment="Top"
                                                               Margin="0,3,0,0" FontSize="10"/>
                                                </Grid>
                                            </Border>

                                            <!-- After 图表 -->
                                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="1"
                                                    Background="Black" CornerRadius="3">
                                                <Grid>
                                                    <Ellipse Fill="Black" Stroke="White" StrokeThickness="1"
                                                             Margin="3" x:Name="AfterCircle"/>
                                                    <Canvas x:Name="AfterCanvas" Margin="3" Tag="AfterCanvas"/>
                                                    <TextBlock Text="After" Foreground="White" FontWeight="Bold"
                                                               HorizontalAlignment="Center" VerticalAlignment="Top"
                                                               Margin="0,3,0,0" FontSize="10"/>
                                                </Grid>
                                            </Border>

                                            <!-- Add 图表 -->
                                            <Border Grid.Column="2" BorderBrush="Black" BorderThickness="1" Margin="1"
                                                    Background="Black" CornerRadius="3">
                                                <Grid>
                                                    <Ellipse Fill="Black" Stroke="White" StrokeThickness="1"
                                                             Margin="3" x:Name="AddCircle"/>
                                                    <Canvas x:Name="AddCanvas" Margin="3" Tag="AddCanvas"/>
                                                    <TextBlock Text="Add" Foreground="White" FontWeight="Bold"
                                                               HorizontalAlignment="Center" VerticalAlignment="Top"
                                                               Margin="0,3,0,0" FontSize="10"/>
                                                </Grid>
                                            </Border>
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="解析时间" Binding="{Binding [Timestamp], StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                            <DataGridTemplateColumn Header="操作" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="删除"
                                                    Click="DeletePADataButton_Click"
                                                    Tag="{Binding}"
                                                    Width="40"
                                                    Height="25"
                                                    Margin="2"/>
                                            <Button Content="预览"
                                                    Click="PreviewPADataButton_Click"
                                                    Tag="{Binding}"
                                                    Width="40"
                                                    Height="25"
                                                    Margin="2"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 颗粒数据对比选择 -->
                    <Grid Grid.Row="1" Margin="0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="选择会话1:" Margin="0,0,5,0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="1"
                                  ItemsSource="{Binding PADataSessions}"
                                  SelectedItem="{Binding SelectedPADataSession1}"
                                  Margin="0,0,10,0">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <Run Text="{Binding [Id], StringFormat='ID: {0}'}" />
                                        <Run Text=" - " />
                                        <Run Text="{Binding [ParsedFilePath]}" />
                                    </TextBlock>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <TextBlock Grid.Column="2" Text="选择会话2:" Margin="0,0,5,0" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="3"
                                  ItemsSource="{Binding PADataSessions}"
                                  SelectedItem="{Binding SelectedPADataSession2}"
                                  Margin="0,0,10,0">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <Run Text="{Binding [Id], StringFormat='ID: {0}'}" />
                                        <Run Text=" - " />
                                        <Run Text="{Binding [ParsedFilePath]}" />
                                    </TextBlock>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <Button Grid.Column="4" Content="对比颗粒" Command="{Binding ComparePADataCommand}" Padding="10,5"/>
                    </Grid>


                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl> 