using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using DatalogDrawing.Models;
using Newtonsoft.Json;

namespace DatalogDrawing.Services
{
    public static class ParticleDrawingService
    {
        // 统一的颗粒绘制方法，显示所有数据点
        public static void DrawParticlesOnCanvas(Canvas canvas, List<PAData> data)
        {
            if (data == null || data.Count == 0) return;

            // 等待Canvas加载完成
            canvas.Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    canvas.Children.Clear();

                    // 根据Canvas的实际尺寸或使用固定尺寸
                    double centerX, centerY, radius;

                    if (canvas.ActualWidth > 0 && canvas.ActualHeight > 0)
                    {
                        // 使用实际尺寸（主要用于PADataView）
                        centerX = canvas.ActualWidth / 2;
                        centerY = canvas.ActualHeight / 2;
                        radius = Math.Min(centerX, centerY) - 30;
                    }
                    else
                    {
                        // 使用固定尺寸（主要用于DataGrid中的小Canvas）
                        // 根据Canvas的父容器尺寸估算
                        var parent = canvas.Parent as FrameworkElement;
                        if (parent != null && parent.ActualWidth > 0 && parent.ActualHeight > 0)
                        {
                            centerX = parent.ActualWidth / 2;
                            centerY = parent.ActualHeight / 2;
                            radius = Math.Min(centerX, centerY) - 5;
                        }
                        else
                        {
                            // 默认固定尺寸
                            centerX = 60.0;
                            centerY = 40.0;
                            radius = 30.0;
                        }
                    }

                    // 动态计算有效显示区域，实现自适应缩放
                    if (data.Count == 0) return;

                    // 1. 计算所有颗粒点的实际坐标范围
                    var minX = data.Min(p => p.XREL);
                    var maxX = data.Max(p => p.XREL);
                    var minY = data.Min(p => p.YREL);
                    var maxY = data.Max(p => p.YREL);

                    // 2. 计算数据的实际范围
                    var dataRangeX = maxX - minX;
                    var dataRangeY = maxY - minY;
                    var maxDataRange = Math.Max(dataRangeX, dataRangeY);

                    // 3. 如果数据范围太小，设置最小范围避免过度放大
                    if (maxDataRange < 1.0) maxDataRange = 1.0;

                    // 4. 计算数据中心点
                    var dataCenterX = (minX + maxX) / 2.0;
                    var dataCenterY = (minY + maxY) / 2.0;

                    // 5. 设置显示区域的有效半径（增加约3倍以提供更好的视觉间距）
                    var effectiveRadius = radius * 2.7;

                    // 显示所有数据点，使用自适应缩放
                    foreach (var particle in data)
                    {
                        // 6. 将颗粒坐标相对于数据中心进行归一化
                        var relativeX = (particle.XREL - dataCenterX) / maxDataRange;
                        var relativeY = (particle.YREL - dataCenterY) / maxDataRange;

                        // 7. 将归一化坐标映射到圆形显示区域
                        var x = centerX + relativeX * effectiveRadius;
                        var y = centerY + relativeY * effectiveRadius;

                        // 减小点的尺寸，减少拥挤感，同时保持清晰可见
                        var markerSize = Math.Max(3, Math.Min(8, particle.DSIZE * 18));

                        // 根据颗粒大小选择颜色
                        var color = GetParticleColor(particle.DSIZE);

                        var ellipse = new Ellipse
                        {
                            Width = markerSize,
                            Height = markerSize,
                            Fill = color,
                            Stroke = Brushes.Black, // 恢复黑色边框，减少视觉干扰
                            StrokeThickness = 0.5 // 减小边框厚度，让点看起来更精致
                        };

                        Canvas.SetLeft(ellipse, x - markerSize / 2);
                        Canvas.SetTop(ellipse, y - markerSize / 2);

                        canvas.Children.Add(ellipse);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"绘制颗粒图表时出错: {ex.Message}");
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        // 根据颗粒大小获取颜色（从配置文件加载）
        private static Brush GetParticleColor(double size)
        {
            // 从配置文件加载颜色映射
            var colorMappings = LoadParticleColorMappings();

            var mapping = colorMappings.FirstOrDefault(m => size >= m.MinSize && size < m.MaxSize);
            if (mapping != null)
            {
                try
                {
                    var color = (Color)ColorConverter.ConvertFromString(mapping.Color);
                    return new SolidColorBrush(color);
                }
                catch
                {
                    return Brushes.White;
                }
            }

            return Brushes.White;
        }

        // 从PAconfig.json加载颗粒颜色映射
        private static List<ParticleColorMapping> LoadParticleColorMappings()
        {
            try
            {
                // 尝试从PAconfig.json加载
                var configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PAconfig.json");
                if (System.IO.File.Exists(configPath))
                {
                    var json = System.IO.File.ReadAllText(configPath);
                    var config = JsonConvert.DeserializeObject<PAConfig>(json);
                    if (config?.DisplayConfig?.ParticleColorMapping?.Count > 0)
                    {
                        // 转换PAconfig格式到标准格式
                        var mappings = config.DisplayConfig.ParticleColorMapping.Select(m => new ParticleColorMapping
                        {
                            MinSize = m.MinSize,
                            MaxSize = m.MaxSize,
                            Color = m.Color,
                            Name = m.Name
                        }).ToList();

                        return mappings;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ParticleDrawingService加载PAconfig.json失败: {ex.Message}");
            }

            // 如果配置文件不存在或加载失败，使用默认配置
            return GetDefaultColorMappings();
        }

        // 获取默认颜色映射（与PAconfig.json保持一致）
        private static List<ParticleColorMapping> GetDefaultColorMappings()
        {
            return new List<ParticleColorMapping>
            {
                new ParticleColorMapping { MinSize = 0.0, MaxSize = 0.03, Color = "#FFFFFF", Name = "0.00-0.03μm" },
                new ParticleColorMapping { MinSize = 0.03, MaxSize = 0.05, Color = "#ADD8E6", Name = "0.03-0.05μm" },
                new ParticleColorMapping { MinSize = 0.05, MaxSize = 0.08, Color = "#00008B", Name = "0.05-0.08μm" },
                new ParticleColorMapping { MinSize = 0.08, MaxSize = 0.12, Color = "#FFFF00", Name = "0.08-0.12μm" },
                new ParticleColorMapping { MinSize = 0.12, MaxSize = 0.2, Color = "#90EE90", Name = "0.12-0.20μm" },
                new ParticleColorMapping { MinSize = 0.2, MaxSize = 0.4, Color = "#00FFFF", Name = "0.20-0.40μm" },
                new ParticleColorMapping { MinSize = 0.4, MaxSize = 1.0, Color = "#800080", Name = "0.40-1.00μm" },
                new ParticleColorMapping { MinSize = 1.0, MaxSize = 999.0, Color = "#FF0000", Name = "≥1.00μm" }
            };
        }
    }
}
