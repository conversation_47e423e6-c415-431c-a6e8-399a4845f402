using System;
using System.Collections.Generic;

namespace DatalogDrawing.Models
{
    public class SlotInfo
    {
        public int SlotNumber { get; set; }
        public string WaferId { get; set; }
        public string ProductId { get; set; }
        public string RecipeId { get; set; }
        public DateTime ProcessTime { get; set; }
        
        // 添加缺失的属性
        public List<Dictionary<string, object>> SlotTotalInfo { get; set; } = new List<Dictionary<string, object>>();
        public List<Dictionary<string, object>> SlotDetailInfo { get; set; } = new List<Dictionary<string, object>>();
        public List<string> columnStaticNames { get; set; } = new List<string>();
        public List<string> columnDetailNames { get; set; } = new List<string>();
        
        public override string ToString()
        {
            return $"{SlotNumber}: {WaferId} ({ProductId})";
        }
    }
}