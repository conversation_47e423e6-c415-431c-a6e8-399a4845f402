using System;
using System.IO;
using System.Windows;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace DatalogDrawing
{
    public class ConfigService
    {
        private string _configPath;
        private AppConfig _config;

        public AppConfig Config
        {
            get => _config;
            set => _config = value;
        }

        public bool SaveConfig(bool showMessage = false)
        {
            try
            {
                if (string.IsNullOrEmpty(_configPath))
                {
                    if (showMessage)
                    {
                        MessageBox.Show("请先选择或加载配置文件。", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    return false;
                }

                var json = JsonConvert.SerializeObject(_config, Formatting.Indented);
                File.WriteAllText(_configPath, json);
                if (showMessage)
                {
                    MessageBox.Show("配置保存成功。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public void ExportConfig(string exportPath)
        {
            try
            {
                if (_config == null)
                {
                    MessageBox.Show("没有配置可导出。", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var json = JsonConvert.SerializeObject(_config, Formatting.Indented);
                File.WriteAllText(exportPath, json);
                MessageBox.Show("配置导出成功。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void ImportConfig(string importPath)
        {
            try
            {
                if (!File.Exists(importPath))
                {
                    MessageBox.Show("未找到导入文件。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                
                var json = File.ReadAllText(importPath);
                _config = JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
                
                // 设置当前配置路径为导入的文件路径
                _configPath = importPath;
                
                MessageBox.Show("配置导入成功。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public ConfigService()
        {
            // 初始化时不加载任何配置文件
            _configPath = null;
            _config = new AppConfig(); // 创建一个空的配置对象
        }

        public bool SetConfigPath(string configPath)
        {
            if (string.IsNullOrEmpty(configPath))
            {
                MessageBox.Show("配置文件路径不能为空。", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            _configPath = configPath;
            return true;
        }

        public bool LoadConfig()
        {
            try
            {
                if (string.IsNullOrEmpty(_configPath))
                {
                    _config = new AppConfig(); // 如果没有路径，创建默认配置
                    return false;
                }

                if (File.Exists(_configPath))
                {
                    var json = File.ReadAllText(_configPath);
                    _config = JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
                    return true;
                }
                else
                {
                    _config = new AppConfig(); // 文件不存在，创建默认配置
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置文件出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                _config = new AppConfig(); // 发生错误，回退到默认配置
                return false;
            }
        }

    }
}