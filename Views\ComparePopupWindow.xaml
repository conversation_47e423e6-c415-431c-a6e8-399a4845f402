<Window x:Class="DatalogDrawing.Views.ComparePopupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:oxy="http://oxyplot.org/wpf"
        xmlns:views="clr-namespace:DatalogDrawing.Views"
        Title="会话对比" Height="900" Width="1600" WindowStartupLocation="CenterScreen" WindowState="Maximized">

    <Window.Resources>
        <views:FileNameConverter x:Key="FileNameConverter"/>
    </Window.Resources>
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="DarkBlue" Padding="10,5" Margin="0,0,0,5">
            <TextBlock Text="📊 会话数据对比分析" FontSize="18" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- 主要内容区域 -->
        <TabControl Grid.Row="1" Margin="5">
            <!-- 膜厚对比Tab -->
            <TabItem Header="📊 膜厚对比">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="5"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 会话1 -->
                    <Grid Grid.Column="0" Margin="0,0,2,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 会话1标题和控制 -->
                        <Border Grid.Row="0" Background="LightBlue" Padding="8" Margin="0,0,0,3">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行：标题和Slot选择 -->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="120"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="📈 会话1" FontWeight="Bold" FontSize="14" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="2" Text="选择Slot:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <ComboBox Grid.Column="3" ItemsSource="{Binding CompareSlots1}"
                                              SelectedItem="{Binding SelectedCompareSlot1}"
                                              DisplayMemberPath="Key"/>
                                </Grid>

                                <!-- 第二行：文件信息 -->
                                <StackPanel Grid.Row="1" Margin="0,5,0,0">
                                    <TextBlock FontSize="10" Foreground="DarkBlue">
                                        <Run Text="CSV文件: "/>
                                        <Run Text="{Binding CompareFilmThicknessVM1.SelectedCsvFile.FileName, FallbackValue='未知'}"/>
                                    </TextBlock>
                                    <TextBlock FontSize="10" Foreground="DarkBlue">
                                        <Run Text="配置文件: "/>
                                        <Run Text="{Binding CompareFilmThicknessVM1.SelectedConfigFile.FileName, FallbackValue='未知'}"/>
                                    </TextBlock>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- 数据表格区域 -->
                        <Border Grid.Row="2" BorderBrush="Gray" BorderThickness="1" Margin="0,0,0,3">
                            <views:FilmThicknessView DataContext="{Binding CompareFilmThicknessVM1}"/>
                        </Border>

                        <!-- 热力图区域 -->
                        <Border Grid.Row="3" BorderBrush="Gray" BorderThickness="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0" Background="LightGray" Padding="5">
                                    <TextBlock Text="📊 热力图分析" FontWeight="Bold" HorizontalAlignment="Center"/>
                                </Border>
                                <oxy:PlotView Grid.Row="1" x:Name="HeatMapView1" Model="{Binding CompareHeatMapModel1}"
                                              Background="White" Margin="5"/>
                            </Grid>
                        </Border>
                    </Grid>

                    <!-- 分隔线 -->
                    <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="DarkGray" ShowsPreview="True"/>

                    <!-- 会话2 -->
                    <Grid Grid.Column="2" Margin="2,0,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 会话2标题和控制 -->
                        <Border Grid.Row="0" Background="LightGreen" Padding="8" Margin="0,0,0,3">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行：标题和Slot选择 -->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="120"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="📊 会话2" FontWeight="Bold" FontSize="14" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="2" Text="选择Slot:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <ComboBox Grid.Column="3" ItemsSource="{Binding CompareSlots2}"
                                              SelectedItem="{Binding SelectedCompareSlot2}"
                                              DisplayMemberPath="Key"/>
                                </Grid>

                                <!-- 第二行：文件信息 -->
                                <StackPanel Grid.Row="1" Margin="0,5,0,0">
                                    <TextBlock FontSize="10" Foreground="DarkGreen">
                                        <Run Text="CSV文件: "/>
                                        <Run Text="{Binding CompareFilmThicknessVM2.SelectedCsvFile.FileName, FallbackValue='未知'}"/>
                                    </TextBlock>
                                    <TextBlock FontSize="10" Foreground="DarkGreen">
                                        <Run Text="配置文件: "/>
                                        <Run Text="{Binding CompareFilmThicknessVM2.SelectedConfigFile.FileName, FallbackValue='未知'}"/>
                                    </TextBlock>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- 数据表格区域 -->
                        <Border Grid.Row="2" BorderBrush="Gray" BorderThickness="1" Margin="0,0,0,3">
                            <views:FilmThicknessView DataContext="{Binding CompareFilmThicknessVM2}"/>
                        </Border>

                        <!-- 热力图区域 -->
                        <Border Grid.Row="3" BorderBrush="Gray" BorderThickness="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0" Background="LightGray" Padding="5">
                                    <TextBlock Text="📊 热力图分析" FontWeight="Bold" HorizontalAlignment="Center"/>
                                </Border>
                                <oxy:PlotView Grid.Row="1" x:Name="HeatMapView2" Model="{Binding CompareHeatMapModel2}"
                                              Background="White" Margin="5"/>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- 颗粒对比Tab -->
            <TabItem Header="🔴 颗粒对比">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="5"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 会话1颗粒数据 -->
                    <Grid Grid.Column="0" Margin="0,0,2,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="LightBlue" Padding="8" Margin="0,0,0,3">
                            <TextBlock Text="📈 会话1 - 颗粒分布" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        </Border>

                        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
                            <oxy:PlotView x:Name="PADataView1" Model="{Binding ComparePAPlotModel1}"
                                          Background="White" Margin="5"/>
                        </Border>
                    </Grid>

                    <!-- 分隔线 -->
                    <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="DarkGray" ShowsPreview="True"/>

                    <!-- 会话2颗粒数据 -->
                    <Grid Grid.Column="2" Margin="2,0,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="LightGreen" Padding="8" Margin="0,0,0,3">
                            <TextBlock Text="� 会话2 - 颗粒分布" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        </Border>

                        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
                            <oxy:PlotView x:Name="PADataView2" Model="{Binding ComparePAPlotModel2}"
                                          Background="White" Margin="5"/>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Window>