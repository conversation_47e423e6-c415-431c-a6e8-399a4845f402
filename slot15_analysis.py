#!/usr/bin/env python3
"""
Slot 15 专项分析
展示Slot 15的特殊数据格式和分析结果
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from simple_parser import parse_film_thickness_csv
from film_thickness_heatmap import FilmThicknessHeatmap

def analyze_slot15():
    """专门分析Slot 15的数据"""
    
    print("=" * 60)
    print("Slot 15 专项分析 - 高厚度膜层")
    print("=" * 60)
    
    # 解析数据
    csv_path = r"E:\Code\WPF\DatalogDrawing\bin\Debug\data.csv"
    slots_data = parse_film_thickness_csv(csv_path)
    
    if '15' not in slots_data:
        print("错误: 未找到Slot 15数据")
        return
    
    slot15_data = slots_data['15']
    print(f"\n✓ 成功解析Slot 15数据: {len(slot15_data)} 个测量点")
    
    # 数据分析
    thicknesses = [p['Value'] for p in slot15_data]
    x_coords = [p['X'] for p in slot15_data]
    y_coords = [p['Y'] for p in slot15_data]
    
    print(f"\n📊 Slot 15 数据特征:")
    print(f"  厚度范围: {min(thicknesses):.2f} - {max(thicknesses):.2f} nm")
    print(f"  平均厚度: {np.mean(thicknesses):.2f} ± {np.std(thicknesses):.2f} nm")
    print(f"  变异系数: {np.std(thicknesses)/np.mean(thicknesses)*100:.2f}%")
    print(f"  中位数: {np.median(thicknesses):.2f} nm")
    print(f"  测量范围: X({min(x_coords):.1f} to {max(x_coords):.1f}), Y({min(y_coords):.1f} to {max(y_coords):.1f})")
    
    # 与其他槽位对比
    print(f"\n🔍 与其他槽位对比:")
    for slot_num, measurements in slots_data.items():
        if slot_num != '15' and measurements:
            other_thicknesses = [m['Value'] for m in measurements]
            print(f"  Slot {slot_num}: 平均厚度 {np.mean(other_thicknesses):.2f} nm")
    
    # 特殊性分析
    print(f"\n⭐ Slot 15 特殊性:")
    print(f"  1. 厚度水平: ~163nm (比其他槽位高约2倍)")
    print(f"  2. 均匀性: CV={np.std(thicknesses)/np.mean(thicknesses)*100:.2f}% (优秀)")
    print(f"  3. 数据格式: 简化格式 (Site#, Thickness, GOF, X, Y)")
    print(f"  4. 测量点数: {len(slot15_data)} 个 (完整49点测量)")
    
    # 创建输出目录
    output_dir = Path("slot15_analysis")
    output_dir.mkdir(exist_ok=True)
    
    # 生成专门的热力图
    print(f"\n🎨 生成Slot 15专用热力图...")
    
    heatmap_generator = FilmThicknessHeatmap(grid_size=300, contour_levels=12)
    
    title = f"Slot 15 High-Thickness Film Analysis\n" \
            f"Mean: {np.mean(thicknesses):.2f}±{np.std(thicknesses):.2f} nm " \
            f"(CV: {np.std(thicknesses)/np.mean(thicknesses)*100:.2f}%)"
    
    # 2D热力图
    output_2d = output_dir / "slot15_detailed_2d.png"
    fig_2d = heatmap_generator.create_heatmap(
        points=slot15_data,
        title=title,
        save_path=str(output_2d),
        show_plot=False,
        figsize=(12, 10),
        dpi=250,
        use_median_filter=True  # 启用移动中位数滤波优化
    )
    plt.close(fig_2d)
    print(f"  ✓ 2D热力图: {output_2d.name}")
    
    # 3D表面图
    output_3d = output_dir / "slot15_detailed_3d.png"
    fig_3d = heatmap_generator.create_3d_surface(
        points=slot15_data,
        title=title,
        save_path=str(output_3d),
        show_plot=False,
        figsize=(14, 11),
        dpi=250,
        use_median_filter=True  # 启用移动中位数滤波优化
    )
    plt.close(fig_3d)
    print(f"  ✓ 3D表面图: {output_3d.name}")
    
    # 生成厚度分布分析图
    create_thickness_distribution_analysis(slot15_data, slots_data, output_dir)
    
    # 生成径向分析图
    create_radial_analysis(slot15_data, output_dir)
    
    print(f"\n✅ Slot 15 专项分析完成!")
    print(f"📁 输出目录: {output_dir.absolute()}")

def create_thickness_distribution_analysis(slot15_data, all_slots_data, output_dir):
    """创建厚度分布对比分析"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Slot 15 厚度分布专项分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    slot15_thickness = [p['Value'] for p in slot15_data]
    
    # 1. Slot 15厚度分布直方图
    ax1 = axes[0, 0]
    ax1.hist(slot15_thickness, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('厚度 (nm)')
    ax1.set_ylabel('频次')
    ax1.set_title('Slot 15 厚度分布直方图')
    ax1.grid(True, alpha=0.3)
    ax1.axvline(np.mean(slot15_thickness), color='red', linestyle='--', 
                label=f'平均值: {np.mean(slot15_thickness):.2f}nm')
    ax1.legend()
    
    # 2. 与其他槽位的对比
    ax2 = axes[0, 1]
    all_means = []
    all_stds = []
    slot_labels = []
    
    for slot_num, measurements in all_slots_data.items():
        if measurements:
            thicknesses = [m['Value'] for m in measurements]
            all_means.append(np.mean(thicknesses))
            all_stds.append(np.std(thicknesses))
            slot_labels.append(f'Slot {slot_num}')
    
    colors = ['lightcoral' if 'Slot 15' in label else 'lightblue' for label in slot_labels]
    bars = ax2.bar(range(len(slot_labels)), all_means, yerr=all_stds, 
                   capsize=5, alpha=0.7, color=colors)
    ax2.set_xlabel('槽位')
    ax2.set_ylabel('平均厚度 (nm)')
    ax2.set_title('槽位间厚度对比 (Slot 15突出显示)')
    ax2.set_xticks(range(len(slot_labels)))
    ax2.set_xticklabels(slot_labels)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (mean, std) in enumerate(zip(all_means, all_stds)):
        ax2.text(i, mean + std + 1, f'{mean:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. Slot 15厚度空间分布
    ax3 = axes[1, 0]
    x_coords = [p['X'] for p in slot15_data]
    y_coords = [p['Y'] for p in slot15_data]
    scatter = ax3.scatter(x_coords, y_coords, c=slot15_thickness, 
                         cmap='turbo', s=50, alpha=0.8)
    ax3.set_xlabel('X 坐标')
    ax3.set_ylabel('Y 坐标')
    ax3.set_title('Slot 15 厚度空间分布')
    ax3.set_aspect('equal')
    ax3.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax3, label='厚度 (nm)')
    
    # 4. 变异系数对比
    ax4 = axes[1, 1]
    cvs = [std/mean*100 for mean, std in zip(all_means, all_stds)]
    colors = ['lightcoral' if 'Slot 15' in label else 'lightblue' for label in slot_labels]
    bars = ax4.bar(range(len(slot_labels)), cvs, alpha=0.7, color=colors)
    ax4.set_xlabel('槽位')
    ax4.set_ylabel('变异系数 (%)')
    ax4.set_title('厚度均匀性对比 (CV%)')
    ax4.set_xticks(range(len(slot_labels)))
    ax4.set_xticklabels(slot_labels)
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, cv in enumerate(cvs):
        ax4.text(i, cv + 0.05, f'{cv:.2f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    output_path = output_dir / "slot15_distribution_analysis.png"
    plt.savefig(output_path, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"  ✓ 分布分析图: {output_path.name}")

def create_radial_analysis(slot15_data, output_dir):
    """创建径向分析图"""
    
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('Slot 15 径向厚度分析', fontsize=16, fontweight='bold')
    
    # 计算径向距离
    x_coords = np.array([p['X'] for p in slot15_data])
    y_coords = np.array([p['Y'] for p in slot15_data])
    thicknesses = np.array([p['Value'] for p in slot15_data])
    
    radial_distances = np.sqrt(x_coords**2 + y_coords**2)
    
    # 1. 径向厚度分布
    ax1 = axes[0]
    scatter = ax1.scatter(radial_distances, thicknesses, c=thicknesses, 
                         cmap='turbo', s=60, alpha=0.8)
    ax1.set_xlabel('径向距离 (mm)')
    ax1.set_ylabel('厚度 (nm)')
    ax1.set_title('径向厚度分布')
    ax1.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax1, label='厚度 (nm)')
    
    # 添加趋势线
    z = np.polyfit(radial_distances, thicknesses, 2)
    p = np.poly1d(z)
    x_trend = np.linspace(0, max(radial_distances), 100)
    ax1.plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2, label='趋势线')
    ax1.legend()
    
    # 2. 径向区域统计
    ax2 = axes[1]
    
    # 分区域统计
    max_radius = max(radial_distances)
    radius_bins = np.linspace(0, max_radius, 6)
    region_means = []
    region_stds = []
    region_labels = []
    
    for i in range(len(radius_bins)-1):
        mask = (radial_distances >= radius_bins[i]) & (radial_distances < radius_bins[i+1])
        if np.any(mask):
            region_thickness = thicknesses[mask]
            region_means.append(np.mean(region_thickness))
            region_stds.append(np.std(region_thickness))
            region_labels.append(f'{radius_bins[i]:.0f}-{radius_bins[i+1]:.0f}mm')
    
    x_pos = range(len(region_labels))
    bars = ax2.bar(x_pos, region_means, yerr=region_stds, capsize=5, 
                   alpha=0.7, color='lightgreen')
    ax2.set_xlabel('径向区域')
    ax2.set_ylabel('平均厚度 (nm)')
    ax2.set_title('径向区域厚度统计')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(region_labels, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (mean, std) in enumerate(zip(region_means, region_stds)):
        ax2.text(i, mean + std + 0.2, f'{mean:.1f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    output_path = output_dir / "slot15_radial_analysis.png"
    plt.savefig(output_path, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"  ✓ 径向分析图: {output_path.name}")

if __name__ == "__main__":
    try:
        analyze_slot15()
    except Exception as e:
        print(f"分析出错: {e}")
        import traceback
        traceback.print_exc()
