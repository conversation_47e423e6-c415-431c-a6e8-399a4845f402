using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using DatalogDrawing.Models;
using DatalogDrawing.Services;
using DatalogDrawing.Views;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using Newtonsoft.Json.Linq;
using DatalogDrawing.Interfaces;
using DatalogDrawing.Helpers;

namespace DatalogDrawing.ViewModels
{
    public class SummaryViewModel : ViewModelBase
    {
        //private object _selectedResult;
        private List<PAData> _paDataResults;

        // 会话管理
        public ObservableCollection<Dictionary<string, object>> Sessions { get; } = new ObservableCollection<Dictionary<string, object>>();

        // 分离的膜厚和颗粒数据集合
        public ObservableCollection<Dictionary<string, object>> FilmThicknessSessions { get; } = new ObservableCollection<Dictionary<string, object>>();
        public ObservableCollection<Dictionary<string, object>> PADataSessions { get; } = new ObservableCollection<Dictionary<string, object>>();

        private Dictionary<string, object> _selectedSession1;
        public Dictionary<string, object> SelectedSession1
        {
            get => _selectedSession1;
            set
            {
                if (SetProperty(ref _selectedSession1, value))
                {
                    UpdateCompareStatus();
                }
            }
        }

        private Dictionary<string, object> _selectedSession2;
        public Dictionary<string, object> SelectedSession2
        {
            get => _selectedSession2;
            set
            {
                if (SetProperty(ref _selectedSession2, value))
                {
                    UpdateCompareStatus();
                }
            }
        }

        // 膜厚数据选择
        private Dictionary<string, object> _selectedFilmThicknessSession;
        public Dictionary<string, object> SelectedFilmThicknessSession
        {
            get => _selectedFilmThicknessSession;
            set => SetProperty(ref _selectedFilmThicknessSession, value);
        }

        private Dictionary<string, object> _selectedFilmThicknessSession1;
        public Dictionary<string, object> SelectedFilmThicknessSession1
        {
            get => _selectedFilmThicknessSession1;
            set => SetProperty(ref _selectedFilmThicknessSession1, value);
        }

        private Dictionary<string, object> _selectedFilmThicknessSession2;
        public Dictionary<string, object> SelectedFilmThicknessSession2
        {
            get => _selectedFilmThicknessSession2;
            set => SetProperty(ref _selectedFilmThicknessSession2, value);
        }

        // 颗粒数据选择
        private Dictionary<string, object> _selectedPADataSession;
        public Dictionary<string, object> SelectedPADataSession
        {
            get => _selectedPADataSession;
            set
            {
                if (SetProperty(ref _selectedPADataSession, value))
                {
                    // 当选中颗粒数据会话时，自动加载数据
                    if (value != null)
                    {
                        LoadPADataForSession(value);
                    }
                }
            }
        }

        private Dictionary<string, object> _selectedPADataSession1;
        public Dictionary<string, object> SelectedPADataSession1
        {
            get => _selectedPADataSession1;
            set
            {
                if (SetProperty(ref _selectedPADataSession1, value))
                {
                    // 触发对比命令的CanExecute更新
                    (ComparePADataCommand as RelayCommand<object>)?.RaiseCanExecuteChanged();
                }
            }
        }

        private Dictionary<string, object> _selectedPADataSession2;
        public Dictionary<string, object> SelectedPADataSession2
        {
            get => _selectedPADataSession2;
            set
            {
                if (SetProperty(ref _selectedPADataSession2, value))
                {
                    // 触发对比命令的CanExecute更新
                    (ComparePADataCommand as RelayCommand<object>)?.RaiseCanExecuteChanged();
                }
            }
        }

        // 结果和状态
        private string _summaryResult;
        public string SummaryResult
        {
            get => _summaryResult;
            set => SetProperty(ref _summaryResult, value);
        }

        // 预览相关
        private Visibility _previewVisibility = Visibility.Collapsed;
        public Visibility PreviewVisibility
        {
            get => _previewVisibility;
            set => SetProperty(ref _previewVisibility, value);
        }

        private Visibility _compareResultVisibility = Visibility.Collapsed;
        public Visibility CompareResultVisibility
        {
            get => _compareResultVisibility;
            set => SetProperty(ref _compareResultVisibility, value);
        }

        private string _previewTitle;
        public string PreviewTitle
        {
            get => _previewTitle;
            set => SetProperty(ref _previewTitle, value);
        }

        private ObservableCollection<KeyValuePair<string, string>> _previewBasicInfo = new ObservableCollection<KeyValuePair<string, string>>();
        public ObservableCollection<KeyValuePair<string, string>> PreviewBasicInfo
        {
            get => _previewBasicInfo;
            set => SetProperty(ref _previewBasicInfo, value);
        }

        private DataTable _previewStatisticsData = new DataTable();
        public DataTable PreviewStatisticsData
        {
            get => _previewStatisticsData;
            set => SetProperty(ref _previewStatisticsData, value);
        }

        private DataTable _previewDetailData = new DataTable();
        public DataTable PreviewDetailData
        {
            get => _previewDetailData;
            set => SetProperty(ref _previewDetailData, value);
        }

        private ObservableCollection<KeyValuePair<string, object>> _previewSlots = new ObservableCollection<KeyValuePair<string, object>>();
        public ObservableCollection<KeyValuePair<string, object>> PreviewSlots
        {
            get => _previewSlots;
            set => SetProperty(ref _previewSlots, value);
        }

        private KeyValuePair<string, object> _selectedPreviewSlot;
        public KeyValuePair<string, object> SelectedPreviewSlot
        {
            get => _selectedPreviewSlot;
            set
            {
                if (SetProperty(ref _selectedPreviewSlot, value))
                {
                    UpdatePreviewDetailData();
                }
            }
        }

        private KeyValuePair<string, object> _selectedHeatmapSlot;
        public KeyValuePair<string, object> SelectedHeatmapSlot
        {
            get => _selectedHeatmapSlot;
            set
            {
                if (SetProperty(ref _selectedHeatmapSlot, value))
                {
                    UpdateHeatMap();
                }
            }
        }

        // 热力图
        private PlotModel _heatMapModel;
        public PlotModel HeatMapModel
        {
            get => _heatMapModel;
            set => SetProperty(ref _heatMapModel, value);
        }

        // 对比数据表格
        private DataTable _compareStatisticsData1 = new DataTable();
        public DataTable CompareStatisticsData1
        {
            get => _compareStatisticsData1;
            set => SetProperty(ref _compareStatisticsData1, value);
        }

        private DataTable _compareStatisticsData2 = new DataTable();
        public DataTable CompareStatisticsData2
        {
            get => _compareStatisticsData2;
            set => SetProperty(ref _compareStatisticsData2, value);
        }

        private PlotModel _compareHeatMapModel1;
        public PlotModel CompareHeatMapModel1
        {
            get => _compareHeatMapModel1;
            set => SetProperty(ref _compareHeatMapModel1, value);
        }

        private PlotModel _compareHeatMapModel2;
        public PlotModel CompareHeatMapModel2
        {
            get => _compareHeatMapModel2;
            set => SetProperty(ref _compareHeatMapModel2, value);
        }

        // PAData属性
        private List<DatalogDrawing.Models.PAData> _preData = new List<DatalogDrawing.Models.PAData>();
        public List<DatalogDrawing.Models.PAData> PreData
        {
            get => _preData;
            set => SetProperty(ref _preData, value);
        }

        private List<DatalogDrawing.Models.PAData> _afterData = new List<DatalogDrawing.Models.PAData>();
        public List<DatalogDrawing.Models.PAData> AfterData
        {
            get => _afterData;
            set => SetProperty(ref _afterData, value);
        }

        private List<DatalogDrawing.Models.PAData> _addData = new List<DatalogDrawing.Models.PAData>();
        public List<DatalogDrawing.Models.PAData> AddData
        {
            get => _addData;
            set => SetProperty(ref _addData, value);
        }

        public List<PAData> PADataResults
        {
            get => _paDataResults;
            set => SetProperty(ref _paDataResults, value);
        }

        // 对比区数据结构
        private DataTable _compareStatisticsTable1 = new DataTable();
        public DataTable CompareStatisticsTable1
        {
            get => _compareStatisticsTable1;
            set => SetProperty(ref _compareStatisticsTable1, value);
        }
        private DataTable _compareDetailTable1 = new DataTable();
        public DataTable CompareDetailTable1 { get => _compareDetailTable1; set => SetProperty(ref _compareDetailTable1, value); }
        private DataTable _compareStatisticsTable2 = new DataTable();
        public DataTable CompareStatisticsTable2
        {
            get => _compareStatisticsTable2;
            set => SetProperty(ref _compareStatisticsTable2, value);
        }
        private DataTable _compareDetailTable2 = new DataTable();
        public DataTable CompareDetailTable2 { get => _compareDetailTable2; set => SetProperty(ref _compareDetailTable2, value); }

        // 新增：对比膜厚图表数据表格（复用FilmThicknessViewModel的ResultTable格式）
        private DataTable _compareFilmThicknessTable1 = new DataTable();
        public DataTable CompareFilmThicknessTable1
        {
            get => _compareFilmThicknessTable1;
            set => SetProperty(ref _compareFilmThicknessTable1, value);
        }
        private DataTable _compareFilmThicknessTable2 = new DataTable();
        public DataTable CompareFilmThicknessTable2
        {
            get => _compareFilmThicknessTable2;
            set => SetProperty(ref _compareFilmThicknessTable2, value);
        }

        // 新增：对比用的FilmThicknessViewModel实例
        private FilmThicknessViewModel _compareFilmThicknessVM1;
        public FilmThicknessViewModel CompareFilmThicknessVM1
        {
            get => _compareFilmThicknessVM1;
            set => SetProperty(ref _compareFilmThicknessVM1, value);
        }

        private FilmThicknessViewModel _compareFilmThicknessVM2;
        public FilmThicknessViewModel CompareFilmThicknessVM2
        {
            get => _compareFilmThicknessVM2;
            set => SetProperty(ref _compareFilmThicknessVM2, value);
        }

        // 新增：对比用的颗粒数据相关属性
        private List<PAData> _comparePAData1;
        public List<PAData> ComparePAData1
        {
            get => _comparePAData1;
            set => SetProperty(ref _comparePAData1, value);
        }

        private List<PAData> _comparePAData2;
        public List<PAData> ComparePAData2
        {
            get => _comparePAData2;
            set => SetProperty(ref _comparePAData2, value);
        }

        private PlotModel _comparePAPlotModel1;
        public PlotModel ComparePAPlotModel1
        {
            get => _comparePAPlotModel1;
            set => SetProperty(ref _comparePAPlotModel1, value);
        }

        private PlotModel _comparePAPlotModel2;
        public PlotModel ComparePAPlotModel2
        {
            get => _comparePAPlotModel2;
            set => SetProperty(ref _comparePAPlotModel2, value);
        }

        // 对比会话属性
        private Dictionary<string, object> _compareSession1;
        public Dictionary<string, object> CompareSession1
        {
            get => _compareSession1;
            set => SetProperty(ref _compareSession1, value);
        }

        private Dictionary<string, object> _compareSession2;
        public Dictionary<string, object> CompareSession2
        {
            get => _compareSession2;
            set => SetProperty(ref _compareSession2, value);
        }

        // 对比Slot相关属性（恢复）
        private ObservableCollection<KeyValuePair<string, object>> _compareSlots1 = new ObservableCollection<KeyValuePair<string, object>>();
        public ObservableCollection<KeyValuePair<string, object>> CompareSlots1 
        {
            get => _compareSlots1;
            set => SetProperty(ref _compareSlots1, value);
        }
        private ObservableCollection<KeyValuePair<string, object>> _compareSlots2 = new ObservableCollection<KeyValuePair<string, object>>();
        public ObservableCollection<KeyValuePair<string, object>> CompareSlots2
        {
            get => _compareSlots2;
            set => SetProperty(ref _compareSlots2, value);
        }
        private KeyValuePair<string, object> _selectedCompareSlot1;
        public KeyValuePair<string, object> SelectedCompareSlot1
        {
            get => _selectedCompareSlot1;
            set
            {
                if (SetProperty(ref _selectedCompareSlot1, value))
                {
                    UpdateCompareSlotResultTable(1);
                    UpdateCompareHeatMap(1);
                }
            }
        }
        private KeyValuePair<string, object> _selectedCompareSlot2;
        public KeyValuePair<string, object> SelectedCompareSlot2
        {
            get => _selectedCompareSlot2;
            set
            {
                if (SetProperty(ref _selectedCompareSlot2, value))
                {
                    UpdateCompareSlotResultTable(2);
                    UpdateCompareHeatMap(2);
                }
            }
        }

        // 私有字段
        private Dictionary<string, object> _currentPreviewSession;
        private FilmThicknessViewModel _filmThicknessVM;
        private DatabaseService _databaseService = DatabaseService.Instance;

        // 命令
        public ICommand LoadSessionsCommand { get; }
        public ICommand CompareSessionsCommand { get; }
        public ICommand DeleteSessionCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand PreviewSessionCommand { get; }

        // 新的分离命令
        public ICommand CompareFilmThicknessCommand { get; }
        public ICommand ComparePADataCommand { get; }

        // 颗粒图刷新请求事件
        public event EventHandler ParticleChartsRefreshRequested;



        public SummaryViewModel()
        {
            // 初始化命令
            LoadSessionsCommand = new RelayCommand<object>(param => LoadSessions());
            CompareSessionsCommand = new RelayCommand<object>(param => CompareSessions(), param => CanCompare());
            DeleteSessionCommand = new RelayCommand<object>(param => DeleteSession(param as Dictionary<string, object>), param => param != null);
            RefreshCommand = new RelayCommand<object>(param => LoadSessions());
            PreviewSessionCommand = new RelayCommand<object>(param => PreviewSession(param as Dictionary<string, object>), param => param != null);

            // 新的分离命令
            CompareFilmThicknessCommand = new RelayCommand<object>(param => CompareFilmThicknessSessions(), param => CanCompareFilmThickness());
            ComparePADataCommand = new RelayCommand<object>(param => ComparePADataSessions(), param => CanComparePAData());

            // 初始化热力图模型
            InitializeHeatMapModel();

            // 初始化 DatabaseService
            _databaseService = DatabaseService.Instance;
            var db = _databaseService; // 保持兼容性

            // 初始化 FilmThicknessViewModel
            try
            {
                var configService = new ConfigService();
                IDataParser<FilmThicknessData> parser = new Services.FilmThicknessParser();
                var callback = new Action<bool>(b => { });

                _filmThicknessVM = new FilmThicknessViewModel(configService, parser, callback);

                if (_filmThicknessVM == null)
                {
                    throw new InvalidOperationException("FilmThicknessViewModel instance was not created.");
                }
            }
            catch (Exception ex)
            {
                // 记录日志或提示用户
                System.Diagnostics.Debug.WriteLine($"初始化 FilmThicknessViewModel 失败: {ex.Message}");
                _filmThicknessVM = null;
            }

            // 执行数据库迁移
            MigrateDeltaToAddData();

            // 自动加载会话
            LoadSessions();
        }

        // 数据库迁移：将旧的DeltaData键名迁移为AddData
        private void MigrateDeltaToAddData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始执行数据库迁移：DeltaData -> AddData");

                // 使用现有的QueryFullResults方法获取PAData会话
                var sessions = _databaseService.QueryFullResults("PAData");
                int migratedCount = 0;

                foreach (var session in sessions)
                {
                    if (session.ContainsKey("FullResultData"))
                    {
                        var fullResultData = session["FullResultData"] as Dictionary<string, object>;
                        if (fullResultData != null && fullResultData.ContainsKey("DeltaData") && !fullResultData.ContainsKey("AddData"))
                        {
                            // 将DeltaData重命名为AddData
                            fullResultData["AddData"] = fullResultData["DeltaData"];
                            fullResultData.Remove("DeltaData");

                            // 由于DatabaseService没有UpdateSession方法，我们需要重新插入数据
                            // 这里暂时跳过更新，只记录需要迁移的数据
                            migratedCount++;
                            System.Diagnostics.Debug.WriteLine($"发现需要迁移的会话 ID={session["Id"]}: DeltaData -> AddData");

                            // 注意：由于没有UpdateSession方法，这里只是标记，实际迁移需要在数据库层面实现
                            // 或者在加载时动态处理（我们已经在加载代码中实现了向后兼容）
                        }
                    }
                }

                if (migratedCount > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"数据库迁移检查完成：发现 {migratedCount} 个需要迁移的会话");
                    System.Diagnostics.Debug.WriteLine("注意：由于向后兼容机制，这些数据仍可正常使用");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("数据库迁移检查完成：无需迁移的数据");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库迁移检查失败: {ex.Message}");
            }
        }

        // 初始化热力图模型
        private void InitializeHeatMapModel()
        {
            HeatMapModel = new PlotModel { Title = "膜厚热力图" };

            // 添加坐标轴
            HeatMapModel.Axes.Add(new LinearAxis { Position = AxisPosition.Bottom, Title = "X 坐标" });
            HeatMapModel.Axes.Add(new LinearAxis { Position = AxisPosition.Left, Title = "Y 坐标" });
        }

        // 预览会话
        private void PreviewSession(Dictionary<string, object> session)
        {
            if (session == null) return;
            try
            {
                _currentPreviewSession = session;
                var moduleType = session.ContainsKey("ModuleType") ? session["ModuleType"]?.ToString() : null;

                if (moduleType == "FilmThickness")
                {
                    PreviewFilmThicknessSession(session);
                }
                else if (moduleType == "PAData")
                {
                    PreviewPADataSession(session);
                }
                else
                {
                    MessageBoxService.ShowError($"不支持的模块类型: {moduleType}");
                }
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"预览失败: {ex.Message}");
            }
        }

        // 预览膜厚数据会话
        private void PreviewFilmThicknessSession(Dictionary<string, object> session)
        {
            // 清空旧的HeatMapModel
            HeatMapModel = null;

            var filePath = session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"]?.ToString() : null;
            var config = new ConfigService();
            config.LoadConfig();
            IDataParser<FilmThicknessData> parser = new Services.FilmThicknessParser();
            _filmThicknessVM = new FilmThicknessViewModel(config, parser, b => { });
            // 保证所有slotEntry.Value都是SlotInfo类型
            Dictionary<string, object> fullResultData = null;
            if (session.ContainsKey("FullResultData"))
            {
                if (session["FullResultData"] is Dictionary<string, object> dict)
                    fullResultData = dict;
                else if (session["FullResultData"] is JObject jObj)
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();
            }
            // 保证所有slotEntry.Value都是SlotInfo类型
            if (fullResultData != null)
            {
                var keys = fullResultData.Keys.ToList();
                foreach (var key in keys)
                {
                    var val = fullResultData[key];
                    if (val is JObject jobj)
                    {
                        fullResultData[key] = jobj.ToObject<SlotInfo>();
                    }
                }
            }
            if (fullResultData != null)
            {
                var csvFilePath = session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"]?.ToString() : null;
                var configFilePath = session.ContainsKey("ConfigFilePath") ? session["ConfigFilePath"]?.ToString() : null;
                System.Diagnostics.Debug.WriteLine($"PreviewFilmThicknessSession: csvFilePath={csvFilePath}, configFilePath={configFilePath}");
                _filmThicknessVM.SetResultData(fullResultData, true, csvFilePath, configFilePath);
            }
            // 弹出膜厚预览窗口
            var previewWin = new FilmThicknessPreviewWindow(_filmThicknessVM);
            previewWin.Show();

            // 填充基本信息
            FillBasicInfo(session);

            // 自动填充Slot选择器
            PreviewSlots = new ObservableCollection<KeyValuePair<string, object>>(fullResultData.Select(kv => new KeyValuePair<string, object>(kv.Key, kv.Value)));
            if (PreviewSlots.Count > 0)
            {
                SelectedPreviewSlot = PreviewSlots[0];
                SelectedHeatmapSlot = PreviewSlots[0]; // 这会触发UpdateHeatMap()
            }
            // 预览区数据全部用_filmThicknessVM的StatisticsTable、DetailTable、GetHeatMapPointsForSlot等
            PreviewStatisticsData = _filmThicknessVM.StatisticsTable;
            PreviewDetailData = _filmThicknessVM.DetailTable;

            // 不需要手动创建热力图，因为设置SelectedHeatmapSlot已经触发了UpdateHeatMap()

            // 设置标题
            string fileName = System.IO.Path.GetFileName(
                session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"]?.ToString() ?? "未知文件" : "未知文件");
            string sessionId = session.ContainsKey("Id") ? session["Id"].ToString() : "未知";
            PreviewTitle = $"预览: {fileName} (ID: {sessionId})";

            // 先设置数据，再显示预览区域
            PreviewVisibility = Visibility.Visible;

            // 主动触发UI刷新
            OnPropertyChanged(nameof(PreviewTitle));
            OnPropertyChanged(nameof(PreviewBasicInfo));
        }

        // 预览颗粒数据会话
        public void PreviewPADataSession(Dictionary<string, object> session)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"PreviewPADataSession: 开始预览会话 ID: {(session?.ContainsKey("Id") == true ? session["Id"] : "未知")}");

                if (session == null)
                {
                    MessageBoxService.ShowError("会话数据为空");
                    return;
                }

                // 调试：打印session的所有键和值
                System.Diagnostics.Debug.WriteLine($"Session包含的键: {string.Join(", ", session.Keys)}");
                foreach (var kvp in session)
                {
                    var valueType = kvp.Value?.GetType().Name ?? "null";
                    var valuePreview = kvp.Value?.ToString();
                    if (valuePreview != null && valuePreview.Length > 100)
                        valuePreview = valuePreview.Substring(0, 100) + "...";
                    System.Diagnostics.Debug.WriteLine($"  {kvp.Key}: {valueType} = {valuePreview}");
                }

                // 检查是否包含FullResultData
                if (!session.ContainsKey("FullResultData"))
                {
                    MessageBoxService.ShowError("会话中不包含FullResultData");
                    return;
                }

                // 调试：检查FullResultData的类型和内容
                var fullResultDataObj = session["FullResultData"];
                System.Diagnostics.Debug.WriteLine($"FullResultData类型: {fullResultDataObj?.GetType().Name ?? "null"}");

                if (fullResultDataObj == null)
                {
                    MessageBoxService.ShowError("FullResultData为空");
                    return;
                }

                // 如果是JObject，打印其内容
                if (fullResultDataObj is Newtonsoft.Json.Linq.JObject jObj)
                {
                    System.Diagnostics.Debug.WriteLine($"FullResultData (JObject) 包含的键: {string.Join(", ", jObj.Properties().Select(p => p.Name))}");
                }

                // 创建PADataViewModel实例
                var paDataViewModel = new PADataViewModel();

                // 从JSON数据中加载Pre、After、Delta数据
                bool hasData = LoadPADataToViewModel(session, paDataViewModel, "预览");

                if (hasData)
                {
                    // 直接使用PADataView，但设置为预览模式
                    var paDataView = new Views.PADataView { DataContext = paDataViewModel };
                    paDataView.SetPreviewMode(true); // 设置为预览模式，按钮不可用

                    var previewWindow = new Window
                    {
                        Title = $"颗粒数据预览 - ID: {session["Id"]}",
                        Width = 1200,
                        Height = 800,
                        Content = paDataView,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen
                    };

                    // 显示窗口后再次确保可视化更新
                    previewWindow.Show();

                    // 延迟一点时间确保窗口完全加载后再刷新
                    System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
                        new Action(() => {
                            System.Diagnostics.Debug.WriteLine($"手动触发PropertyChanged: Pre={paDataViewModel.PreData?.Count ?? 0}, After={paDataViewModel.AfterData?.Count ?? 0}, Add={paDataViewModel.AddData?.Count ?? 0}");

                            // 通过重新设置数据来触发PropertyChanged
                            var preData = paDataViewModel.PreData;
                            var afterData = paDataViewModel.AfterData;
                            var addData = paDataViewModel.AddData;

                            paDataViewModel.PreData = null;
                            paDataViewModel.AfterData = null;
                            paDataViewModel.AddData = null;

                            paDataViewModel.PreData = preData;
                            paDataViewModel.AfterData = afterData;
                            paDataViewModel.AddData = addData;
                        }),
                        System.Windows.Threading.DispatcherPriority.Loaded
                    );

                    var addCount = paDataViewModel.AddData?.Count ?? 0;
                    System.Diagnostics.Debug.WriteLine($"预览颗粒数据成功: Add数据量={addCount}");
                }
                else
                {
                    MessageBoxService.ShowError("未找到对应的颗粒数据");
                }
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"预览颗粒数据失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"预览颗粒数据异常: {ex}");
            }
        }

        // 删除颗粒数据会话
        public void DeletePADataSession(Dictionary<string, object> session)
        {
            try
            {
                if (session == null)
                {
                    MessageBoxService.ShowError("会话数据为空");
                    return;
                }

                var result = MessageBox.Show(
                    $"确定要删除ID为 {session["Id"]} 的颗粒数据会话吗？",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    // 从数据库删除
                    var sessionId = Convert.ToInt32(session["Id"]);
                    bool deleted = DatabaseService.Instance.DeleteSession(sessionId);

                    if (deleted)
                    {
                        // 从集合中移除
                        PADataSessions.Remove(session);

                        // 刷新颗粒图表
                        OnParticleChartsRefreshRequested();

                        MessageBoxService.ShowMessage("颗粒数据会话已删除");
                        System.Diagnostics.Debug.WriteLine($"删除颗粒数据会话成功: ID={session["Id"]}");
                    }
                    else
                    {
                        MessageBoxService.ShowError("删除颗粒数据会话失败");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除颗粒数据会话异常: {ex}");
                MessageBoxService.ShowError($"删除颗粒数据会话失败: {ex.Message}");
            }
        }

        // 填充基本信息
        private void FillBasicInfo(Dictionary<string, object> session)
        {
            PreviewBasicInfo.Clear();

            // 添加基本字段，安全访问字典键
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("ID",
                session.ContainsKey("Id") ? session["Id"].ToString() : "未知"));
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("模块类型",
                session.ContainsKey("ModuleType") ? session["ModuleType"].ToString() : "未知"));
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("文件路径",
                session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"]?.ToString() ?? "未知" : "未知"));
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("配置文件",
                session.ContainsKey("ConfigFilePath") ? session["ConfigFilePath"]?.ToString() ?? "未知" : "未知"));
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("用户输入值",
                session.ContainsKey("UserInputValue") ? session["UserInputValue"]?.ToString() ?? "未知" : "未知"));
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("解析时间",
                session.ContainsKey("Timestamp") ? FormatTimestamp(session["Timestamp"]) : "未知"));

            // 获取FullResultData，处理可能的JObject类型
            Dictionary<string, object> fullResultData = null;

            if (session.ContainsKey("FullResultData"))
            {
                if (session["FullResultData"] is Dictionary<string, object> dict)
                {
                    fullResultData = dict;
                }
                else if (session["FullResultData"] is JObject jObj)
                {
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();
                }
            }

            if (fullResultData == null)
            {
                PreviewBasicInfo.Add(new KeyValuePair<string, string>("平均膜厚", "无法计算"));
                PreviewBasicInfo.Add(new KeyValuePair<string, string>("Slot 数量", "未知"));
                return;
            }

            // 计算平均厚度
            double meanThickness = CalculateMeanThickness(fullResultData);
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("平均膜厚", $"{meanThickness:F2}Å"));

            // 计算 Slot 数量
            int slotCount = fullResultData.Count;
            PreviewBasicInfo.Add(new KeyValuePair<string, string>("Slot 数量", slotCount.ToString()));
        }

        // 填充统计数据
        private void FillStatisticsData(Dictionary<string, object> session)
        {
            PreviewStatisticsData.Clear();
            PreviewStatisticsData.Columns.Clear();

            // 获取FullResultData，处理可能的JObject类型
            Dictionary<string, object> fullResultData = null;

            if (session.ContainsKey("FullResultData"))
            {
                if (session["FullResultData"] is Dictionary<string, object> dict)
                {
                    fullResultData = dict;
                }
                else if (session["FullResultData"] is JObject jObj)
                {
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();
                }
            }

            if (fullResultData == null)
            {
                return; // 如果FullResultData不存在或不是预期的类型，直接返回
            }

            // 添加列
            PreviewStatisticsData.Columns.Add("Slot", typeof(string));
            PreviewStatisticsData.Columns.Add("Wafer ID", typeof(string));
            PreviewStatisticsData.Columns.Add("结果类型", typeof(string));
            PreviewStatisticsData.Columns.Add("平均厚度", typeof(double));
            PreviewStatisticsData.Columns.Add("标准差", typeof(double));
            PreviewStatisticsData.Columns.Add("最小值", typeof(double));
            PreviewStatisticsData.Columns.Add("最大值", typeof(double));

            // 添加行
            foreach (var slotEntry in fullResultData)
            {
                string slotNumber = slotEntry.Key;
                var slotObj = slotEntry.Value;

                // 处理 JObject 类型
                if (slotObj is JObject jObject)
                {
                    var slotTotalInfo = jObject["SlotTotalInfo"]?.ToObject<List<Dictionary<string, object>>>();
                    string waferId = jObject["WaferId"]?.ToString() ?? "未知";

                    if (slotTotalInfo != null)
                    {
                        foreach (var row in slotTotalInfo)
                        {
                            if (row.ContainsKey("RESULT TYPE"))
                            {
                                var newRow = PreviewStatisticsData.NewRow();
                                newRow["Slot"] = slotNumber;
                                newRow["Wafer ID"] = waferId;
                                newRow["结果类型"] = row["RESULT TYPE"]?.ToString() ?? "";

                                // 尝试获取厚度值
                                if (row.ContainsKey("1st Thickness") && double.TryParse(row["1st Thickness"].ToString(), out double thickness))
                                    newRow["平均厚度"] = thickness;

                                // 尝试获取标准差
                                if (row.ContainsKey("1st Std Dev") && double.TryParse(row["1st Std Dev"].ToString(), out double stdDev))
                                    newRow["标准差"] = stdDev;

                                // 尝试获取最小值
                                if (row.ContainsKey("1st Min") && double.TryParse(row["1st Min"].ToString(), out double min))
                                    newRow["最小值"] = min;

                                // 尝试获取最大值
                                if (row.ContainsKey("1st Max") && double.TryParse(row["1st Max"].ToString(), out double max))
                                    newRow["最大值"] = max;

                                PreviewStatisticsData.Rows.Add(newRow);
                            }
                        }
                    }
                }
            }
        }

        // 预览区热力图优化，复用膜厚slot选择和热力图生成逻辑
        private void FillSlotSelector(Dictionary<string, object> session)
        {
            PreviewSlots = new ObservableCollection<KeyValuePair<string, object>>();
            // 获取FullResultData，处理可能的JObject类型
            Dictionary<string, object> fullResultData = null;
            if (session.ContainsKey("FullResultData"))
            {
                if (session["FullResultData"] is Dictionary<string, object> dict)
                {
                    fullResultData = dict;
                }
                else if (session["FullResultData"] is JObject jObj)
                {
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();
                }
            }

            if (fullResultData == null)
            {
                return; // 如果FullResultData不存在或不是预期的类型，直接返回
            }
            foreach (var slotEntry in fullResultData)
            {
                PreviewSlots.Add(new KeyValuePair<string, object>(slotEntry.Key, slotEntry.Value));
            }
            // 默认选择第一个 Slot
            if (PreviewSlots.Count > 0)
            {
                SelectedPreviewSlot = PreviewSlots[0];
                SelectedHeatmapSlot = PreviewSlots[0];
            }
        }

        // 更新详细数据
        private void UpdatePreviewDetailData()
        {
            PreviewDetailData.Clear();
            PreviewDetailData.Columns.Clear();

            if (SelectedPreviewSlot.Key == null || SelectedPreviewSlot.Value == null) return;

            var slotObj = SelectedPreviewSlot.Value;

            // 处理 JObject 类型
            if (slotObj is JObject jObject)
            {
                var slotDetailInfo = jObject["SlotDetailInfo"]?.ToObject<List<Dictionary<string, object>>>();

                if (slotDetailInfo != null && slotDetailInfo.Count > 0)
                {
                    // 获取第一行的所有列名
                    var firstRow = slotDetailInfo[0];
                    foreach (var col in firstRow.Keys)
                    {
                        PreviewDetailData.Columns.Add(col, typeof(string));
                    }

                    // 添加所有行
                    foreach (var row in slotDetailInfo)
                    {
                        var newRow = PreviewDetailData.NewRow();
                        foreach (var col in row.Keys)
                        {
                            newRow[col] = row[col]?.ToString() ?? "";
                        }
                        PreviewDetailData.Rows.Add(newRow);
                    }
                }
            }
        }

        // 更新热力图
        private void UpdateHeatMap()
        {
            // 清空旧的HeatMapModel
            HeatMapModel = null;

            if (SelectedHeatmapSlot.Key == null || _filmThicknessVM == null) return;
            try
            {
                // 使用_filmThicknessVM的GetHeatMapPointsForSlot方法，避免重复解析
                var slotKey = SelectedHeatmapSlot.Key?.ToString();
                var points = _filmThicknessVM.GetHeatMapPointsForSlot(slotKey);

                if (points != null && points.Count > 0)
                {
                    var filmThicknessView = new FilmThicknessView();
                    var model = filmThicknessView.GetType().GetMethod("CreateHeatMapModel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                        .Invoke(filmThicknessView, new object[] { points, $"预览 - Slot {slotKey}" }) as OxyPlot.PlotModel;

                    if (model != null)
                    {
                        HeatMapModel = model;
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"预览热力图生成异常: {ex.Message}");
            }
            // 兜底：无数据时显示空模型
            HeatMapModel = new OxyPlot.PlotModel { Title = $"Slot {SelectedHeatmapSlot.Key} 膜厚热力图" };
        }

        // 热力图点类
        // public class HeatMapPoint
        // {
        //     public double X { get; set; }
        //     public double Y { get; set; }
        //     public double Value { get; set; }
        // }

        // 获取会话摘要信息（用于DataGrid绑定）
        public string GetSessionSummary
        {
            get
            {
                if (SelectedSession1 != null && SelectedSession1.ContainsKey("FullResultData"))
                {
                    var data = SelectedSession1["FullResultData"] as Dictionary<string, object>;
                    if (data != null)
                    {
                        double thickness = CalculateMeanThickness(data);
                        return $"平均厚度: {thickness:F2}Å";
                    }
                }
                return "无数据";
            }
        }

        // 加载所有会话
        private void LoadSessions()
        {
            Sessions.Clear();
            FilmThicknessSessions.Clear();
            PADataSessions.Clear();

            // 加载膜厚数据会话
            var filmThicknessSessions = DatabaseService.Instance.QueryFullResults("FilmThickness");
            System.Diagnostics.Debug.WriteLine($"LoadSessions: 查询到 {filmThicknessSessions?.Count ?? 0} 个FilmThickness会话");

            // 加载颗粒数据会话
            var paDataSessions = DatabaseService.Instance.QueryFullResults("PAData");
            System.Diagnostics.Debug.WriteLine($"LoadSessions: 查询到 {paDataSessions?.Count ?? 0} 个PAData会话");

            // 填充分离的集合
            var orderedFilmThicknessSessions = filmThicknessSessions.OrderByDescending(s => s["Timestamp"]);
            foreach (var session in orderedFilmThicknessSessions)
            {
                System.Diagnostics.Debug.WriteLine($"加载FilmThickness会话: ID={session["Id"]}, ParsedFilePath={(session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"] : "无")}, ConfigFilePath={(session.ContainsKey("ConfigFilePath") ? session["ConfigFilePath"] : "无")}");
                FilmThicknessSessions.Add(session);
            }

            var orderedPADataSessions = paDataSessions.OrderByDescending(s => s["Timestamp"]);
            foreach (var session in orderedPADataSessions)
            {
                System.Diagnostics.Debug.WriteLine($"加载PAData会话: ID={session["Id"]}, FullResultData类型={session["FullResultData"]?.GetType().Name ?? "null"}");
                if (session["FullResultData"] is Newtonsoft.Json.Linq.JObject jObj)
                {
                    System.Diagnostics.Debug.WriteLine($"  JObject包含的键: {string.Join(", ", jObj.Properties().Select(p => p.Name))}");
                }
                PADataSessions.Add(session);
            }

            System.Diagnostics.Debug.WriteLine($"LoadSessions: 加载了 {PADataSessions.Count} 个颗粒数据会话");

            // 触发颗粒图刷新事件
            OnParticleChartsRefreshRequested();

            // 延迟再次刷新，确保DataGrid完全加载后再绘制
            System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
                new Action(() => {
                    System.Diagnostics.Debug.WriteLine("LoadSessions: 延迟刷新颗粒图表");
                    OnParticleChartsRefreshRequested();
                }),
                System.Windows.Threading.DispatcherPriority.Background
            );

            // 合并所有会话到主集合（保持向后兼容）
            var allSessions = new List<Dictionary<string, object>>();
            allSessions.AddRange(filmThicknessSessions);
            allSessions.AddRange(paDataSessions);

            // 按时间倒序排序，最新的在前面
            var orderedSessions = allSessions.OrderByDescending(s => s["Timestamp"]);

            foreach (var session in orderedSessions)
            {
                Sessions.Add(session);
            }

            var filmThicknessCount = filmThicknessSessions.Count;
            var paDataCount = paDataSessions.Count;
            SummaryResult = $"已加载 {Sessions.Count} 个会话（膜厚: {filmThicknessCount}, 颗粒: {paDataCount}）。";
            CompareResultVisibility = Visibility.Visible;

            // 重置选择
            SelectedSession1 = Sessions.FirstOrDefault();
            SelectedSession2 = Sessions.Count > 1 ? Sessions[1] : null;

            // 重置分离的选择
            SelectedFilmThicknessSession1 = FilmThicknessSessions.FirstOrDefault();
            SelectedFilmThicknessSession2 = FilmThicknessSessions.Count > 1 ? FilmThicknessSessions[1] : null;

            SelectedPADataSession1 = PADataSessions.FirstOrDefault();
            SelectedPADataSession2 = PADataSessions.Count > 1 ? PADataSessions[1] : null;

            // 隐藏预览区域
            PreviewVisibility = Visibility.Collapsed;
        }

        // 触发颗粒图刷新事件
        protected virtual void OnParticleChartsRefreshRequested()
        {
            ParticleChartsRefreshRequested?.Invoke(this, EventArgs.Empty);
        }

        // 强制刷新颗粒图表
        public void ForceRefreshParticleCharts()
        {
            System.Diagnostics.Debug.WriteLine("ForceRefreshParticleCharts: 强制刷新颗粒图表");

            // 延迟执行以确保UI完全加载
            System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
                new Action(() => {
                    OnParticleChartsRefreshRequested();
                }),
                System.Windows.Threading.DispatcherPriority.Background
            );
        }

        // 删除会话
        private void DeleteSession(Dictionary<string, object> session)
        {
            if (session == null) return;

            try
            {
                // 从数据库中删除会话
                if (session["Id"] != null && int.TryParse(session["Id"].ToString(), out int sessionId))
                {
                    bool success = DatabaseService.Instance.DeleteSession(sessionId);

                    if (success)
                    {
                        // 从列表中移除
                        Sessions.Remove(session);
                        
                        // 如果删除的是选中的会话，重置选择
                        if (SelectedSession1 == session) SelectedSession1 = Sessions.FirstOrDefault();
                        if (SelectedSession2 == session) SelectedSession2 = Sessions.Count > 1 ? Sessions[1] : null;

                        // 如果删除的是当前预览的会话，隐藏预览区域
                        if (_currentPreviewSession == session)
                        {
                            PreviewVisibility = Visibility.Collapsed;
                            _currentPreviewSession = null;
                        }

                        SummaryResult = "会话已删除。";
                    }
                    else
                    {
                        SummaryResult = "删除失败：未找到指定会话。";
                    }
                }
                else
                {
                    SummaryResult = "删除失败：会话ID无效。";
                }
            }
            catch (Exception ex)
            {
                SummaryResult = $"删除失败: {ex.Message}";
            }
        }

        // 检查是否可以比较
        private bool CanCompare()
        {
            return SelectedSession1 != null && SelectedSession2 != null && SelectedSession1 != SelectedSession2;
        }

        // 更新比较状态
        private void UpdateCompareStatus()
        {
            if (CanCompare())
            {
                SummaryResult = "可以进行对比分析，请点击\"对比\"按钮。";
                CompareResultVisibility = Visibility.Visible;
            }
            else if (SelectedSession1 == SelectedSession2 && SelectedSession1 != null)
            {
                SummaryResult = "请选择两个不同的会话进行对比。";
                CompareResultVisibility = Visibility.Visible;
            }
            else
            {
                SummaryResult = "请选择两个会话进行对比。";
                CompareResultVisibility = Visibility.Visible;
            }
        }

        // 对比会话
        private void CompareSessions()
        {
            if (!CanCompare()) return;
            try
            {
                // 先清空旧的 PlotModel，防止复用
                CompareHeatMapModel1 = null;
                CompareHeatMapModel2 = null;

                // 强制垃圾回收，确保旧的PlotModel被释放
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // 初始化对比用的FilmThicknessViewModel实例
                var config = new ConfigService();
                config.LoadConfig();
                IDataParser<FilmThicknessData> parser1 = new Services.FilmThicknessParser();
                IDataParser<FilmThicknessData> parser2 = new Services.FilmThicknessParser();
                _compareFilmThicknessVM1 = new FilmThicknessViewModel(config, parser1, b => { });
                _compareFilmThicknessVM2 = new FilmThicknessViewModel(config, parser2, b => { });

                // 设置对比会话数据
                CompareSession1 = SelectedSession1;
                CompareSession2 = SelectedSession2;

                // 处理会话1数据
                ProcessCompareSessionData(SelectedSession1, 1);
                // 处理会话2数据
                ProcessCompareSessionData(SelectedSession2, 2);

                // 弹出对比窗口
                var popup = new ComparePopupWindow();
                popup.DataContext = this;

                // 窗口关闭时，释放 PlotModel
                popup.Closed += (s, e) =>
                {
                    CompareHeatMapModel1 = null;
                    CompareHeatMapModel2 = null;
                };

                popup.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"对比过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 处理对比会话数据
        private void ProcessCompareSessionData(Dictionary<string, object> session, int sessionIndex)
        {
            if (session == null) return;

            try
            {
                // 获取FullResultData
                Dictionary<string, object> fullResultData = null;
                if (session["FullResultData"] is Dictionary<string, object> dict)
                    fullResultData = dict;
                else if (session["FullResultData"] is JObject jObj)
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();

                if (fullResultData != null)
                {
                    // 确保所有slotEntry.Value都是SlotInfo类型
                    var keys = fullResultData.Keys.ToList();
                    foreach (var key in keys)
                    {
                        var val = fullResultData[key];
                        if (val is JObject jobj)
                        {
                            fullResultData[key] = jobj.ToObject<SlotInfo>();
                        }
                    }

                    // 设置数据到对应的FilmThicknessViewModel
                    var targetVM = sessionIndex == 1 ? _compareFilmThicknessVM1 : _compareFilmThicknessVM2;

                    // 获取文件路径并设置到FilmThicknessViewModel
                    var csvFilePath = session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"]?.ToString() : null;
                    var configFilePath = session.ContainsKey("ConfigFilePath") ? session["ConfigFilePath"]?.ToString() : null;
                    System.Diagnostics.Debug.WriteLine($"ProcessCompareSessionData 会话{sessionIndex}: csvFilePath={csvFilePath}, configFilePath={configFilePath}");
                    targetVM.SetResultData(fullResultData, true, csvFilePath, configFilePath);

                    // 配置对比模式下的显示设置
                    targetVM.LeftPanelVisibility = Visibility.Collapsed; // 隐藏左侧文件列表
                    targetVM.LeftPanelWidth = new GridLength(0); // 设置左侧面板宽度为0
                    targetVM.SelectedTabIndex = 1; // 设置为数据表格选项卡，更适合对比

                    // 加载对应的颗粒数据
                    LoadComparePAData(session, sessionIndex);

                    // 设置对应的对比表格和Slot列表
                    if (sessionIndex == 1)
                    {
                        CompareStatisticsTable1 = BuildStatisticsDataTable(session);
                        CompareSlots1 = new ObservableCollection<KeyValuePair<string, object>>(GetSlotList(session));
                        SelectedCompareSlot1 = CompareSlots1.FirstOrDefault();
                        // 触发属性变更通知
                        OnPropertyChanged(nameof(CompareFilmThicknessVM1));
                        // 初始化ResultTable和热力图
                        UpdateCompareSlotResultTable(1);
                        UpdateCompareHeatMap(1);
                    }
                    else
                    {
                        CompareStatisticsTable2 = BuildStatisticsDataTable(session);
                        CompareSlots2 = new ObservableCollection<KeyValuePair<string, object>>(GetSlotList(session));
                        SelectedCompareSlot2 = CompareSlots2.FirstOrDefault();
                        // 触发属性变更通知
                        OnPropertyChanged(nameof(CompareFilmThicknessVM2));
                        // 初始化ResultTable和热力图
                        UpdateCompareSlotResultTable(2);
                        UpdateCompareHeatMap(2);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理对比会话{sessionIndex}数据失败: {ex.Message}");
            }
        }
        private List<KeyValuePair<string, object>> GetSlotList(Dictionary<string, object> session)
        {
            var slots = new List<KeyValuePair<string, object>>();
            if (session == null) return slots;
            if (session.ContainsKey("FullResultData"))
            {
                Dictionary<string, object> fullResultData = null;
                if (session["FullResultData"] is Dictionary<string, object> dict)
                    fullResultData = dict;
                else if (session["FullResultData"] is JObject jObj)
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();
                if (fullResultData != null)
                {
                    foreach (var slotEntry in fullResultData)
                        slots.Add(new KeyValuePair<string, object>(slotEntry.Key, slotEntry.Value));
                }
            }
            return slots;
        }
        // 更新对比槽位结果表
        private void UpdateCompareSlotResultTable(int sessionIndex)
        {
            try
            {
                var selectedSlot = sessionIndex == 1 ? SelectedCompareSlot1 : SelectedCompareSlot2;
                var targetVM = sessionIndex == 1 ? _compareFilmThicknessVM1 : _compareFilmThicknessVM2;

                if (targetVM != null && selectedSlot.Key != null && selectedSlot.Value != null)
                {
                    // 确保selectedSlot.Value是SlotInfo类型
                    SlotInfo slotInfo = null;
                    if (selectedSlot.Value is SlotInfo slot)
                    {
                        slotInfo = slot;
                    }
                    else if (selectedSlot.Value is JObject jobj)
                    {
                        slotInfo = jobj.ToObject<SlotInfo>();
                    }

                    if (slotInfo != null)
                    {
                        // 直接使用选中的slot数据，复用膜厚的逻辑
                        var selectedSlotData = new Dictionary<string, object> { { selectedSlot.Key, slotInfo } };
                        targetVM.SetResultData(selectedSlotData, true);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新对比槽位结果表{sessionIndex}失败: {ex.Message}");
            }
        }

        // 更新对比热力图
        private void UpdateCompareHeatMap(int sessionIndex)
        {
            try
            {
                // 先清空对应的PlotModel
                if (sessionIndex == 1)
                {
                    CompareHeatMapModel1 = null;
                }
                else
                {
                    CompareHeatMapModel2 = null;
                }

                var targetVM = sessionIndex == 1 ? _compareFilmThicknessVM1 : _compareFilmThicknessVM2;
                var selectedSlot = sessionIndex == 1 ? SelectedCompareSlot1 : SelectedCompareSlot2;

                if (targetVM != null && selectedSlot.Key != null)
                {
                    var slotKey = selectedSlot.Key.ToString();
                    var points = targetVM.GetHeatMapPointsForSlot(slotKey);

                    if (points != null && points.Count > 0)
                    {
                        // 为每个会话创建完全独立的FilmThicknessView实例
                        var filmThicknessView = new FilmThicknessView();

                        // 创建独立的标题，确保不会重复
                        var title = $"会话{sessionIndex} - Slot {slotKey} - {DateTime.Now.Ticks}";

                        var model = filmThicknessView.GetType().GetMethod("CreateHeatMapModel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                            .Invoke(filmThicknessView, new object[] { points, title }) as OxyPlot.PlotModel;

                        if (model != null)
                        {
                            if (sessionIndex == 1)
                            {
                                CompareHeatMapModel1 = model;
                            }
                            else
                            {
                                CompareHeatMapModel2 = model;
                            }
                            return;
                        }
                    }
                }

                // 如果没有数据或出现问题，设置空的热力图模型
                var emptyModel = new OxyPlot.PlotModel { Title = $"会话{sessionIndex} - 无数据" };
                if (sessionIndex == 1)
                {
                    CompareHeatMapModel1 = emptyModel;
                }
                else
                {
                    CompareHeatMapModel2 = emptyModel;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新对比热力图{sessionIndex}失败: {ex.Message}");
                // 设置空的热力图模型
                var emptyModel = new OxyPlot.PlotModel { Title = $"会话{sessionIndex} - 错误" };
                if (sessionIndex == 1)
                {
                    CompareHeatMapModel1 = emptyModel;
                }
                else
                {
                    CompareHeatMapModel2 = emptyModel;
                }
            }
        }

        // 格式化时间戳
        private string FormatTimestamp(object timestamp)
        {
            if (timestamp == null) return "未知时间";

            if (DateTime.TryParse(timestamp.ToString(), out DateTime dt))
            {
                return dt.ToString("yyyy-MM-dd HH:mm:ss");
            }

            return timestamp.ToString();
        }

        // 计算平均厚度
        private double CalculateMeanThickness(Dictionary<string, object> data)
        {
            double sum = 0;
            int count = 0;

            try
            {
                foreach (var slotEntry in data)
                {
                    var slotObj = slotEntry.Value;

                    // 处理 JObject 类型（从 JSON 反序列化）
                    if (slotObj is Newtonsoft.Json.Linq.JObject jObject)
                    {
                        // 尝试将 JObject 转换为 SlotInfo 属性
                        var slotTotalInfo = jObject["SlotTotalInfo"]?.ToObject<List<Dictionary<string, object>>>();

                        if (slotTotalInfo != null)
                        {
                            foreach (var row in slotTotalInfo)
                            {
                                if (row.ContainsKey("RESULT TYPE") &&
                                    row["RESULT TYPE"].ToString() == "MEAN" &&
                                    row.ContainsKey("1st Thickness"))
                                {
                                    if (double.TryParse(row["1st Thickness"].ToString(), out double thickness))
                                    {
                                        sum += thickness;
                                        count++;
                                    }
                                }
                            }
                        }
                    }
                    // 处理原生 SlotInfo 类型
                    else if (slotObj is SlotInfo slot)
                    {
                        foreach (var row in slot.SlotTotalInfo)
                        {
                            if (row.ContainsKey("RESULT TYPE") &&
                                row["RESULT TYPE"].ToString() == "MEAN" &&
                                row.ContainsKey("1st Thickness"))
                            {
                                if (double.TryParse(row["1st Thickness"].ToString(), out double thickness))
                                {
                                    sum += thickness;
                                    count++;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算平均厚度失败: {ex.Message}");
            }

            return count > 0 ? sum / count : 0;
        }

        // 构建统计信息DataTable
        private DataTable BuildStatisticsDataTable(Dictionary<string, object> session)
        {
            var dt = new DataTable();
            // 复用FillStatisticsData逻辑
            if (session == null) return dt;
            try
            {
                // 获取FullResultData，处理可能的JObject类型
                Dictionary<string, object> fullResultData;
                if (session["FullResultData"] is Dictionary<string, object> dict)
                {
                    fullResultData = dict;
                }
                else if (session["FullResultData"] is JObject jObj)
                {
                    fullResultData = jObj.ToObject<Dictionary<string, object>>();
                }
                else
                {
                    return dt;
                }
                // 添加列
                dt.Columns.Add("Slot", typeof(string));
                dt.Columns.Add("Wafer ID", typeof(string));
                dt.Columns.Add("结果类型", typeof(string));
                dt.Columns.Add("平均厚度", typeof(double));
                dt.Columns.Add("标准差", typeof(double));
                dt.Columns.Add("最小值", typeof(double));
                dt.Columns.Add("最大值", typeof(double));
                // 添加行
                foreach (var slotEntry in fullResultData)
                {
                    string slotNumber = slotEntry.Key;
                    var slotObj = slotEntry.Value;
                    if (slotObj is JObject jObject)
                    {
                        var slotTotalInfo = jObject["SlotTotalInfo"]?.ToObject<List<Dictionary<string, object>>>();
                        string waferId = jObject["WaferId"]?.ToString() ?? "未知";
                        if (slotTotalInfo != null)
                        {
                            foreach (var row in slotTotalInfo)
                            {
                                if (row.ContainsKey("RESULT TYPE"))
                                {
                                    var newRow = dt.NewRow();
                                    newRow["Slot"] = slotNumber;
                                    newRow["Wafer ID"] = waferId;
                                    newRow["结果类型"] = row["RESULT TYPE"]?.ToString() ?? "";
                                    if (row.ContainsKey("1st Thickness") && double.TryParse(row["1st Thickness"].ToString(), out double thickness))
                                        newRow["平均厚度"] = thickness;
                                    if (row.ContainsKey("1st Std Dev") && double.TryParse(row["1st Std Dev"].ToString(), out double stdDev))
                                        newRow["标准差"] = stdDev;
                                    if (row.ContainsKey("1st Min") && double.TryParse(row["1st Min"].ToString(), out double min))
                                        newRow["最小值"] = min;
                                    if (row.ContainsKey("1st Max") && double.TryParse(row["1st Max"].ToString(), out double max))
                                        newRow["最大值"] = max;
                                    dt.Rows.Add(newRow);
                                }
                            }
                        }
                    }
                }
            }
            catch { }
            return dt;
        }

        private void LoadPADataResults()
        {
            try
            {
                // 从PAData表中获取最新的会话数据
                var paDataSessions = DatabaseService.Instance.QueryFullResults("PAData");
                if (paDataSessions == null || paDataSessions.Count == 0)
                {
                    PADataResults = new List<PAData>();
                    return;
                }

                // 获取最新的会话
                var latestSession = paDataSessions.FirstOrDefault();
                if (latestSession != null)
                {
                    var fullResultData = latestSession["FullResultData"] as Dictionary<string, object>;
                    // 优先尝试新的AddData键名，如果不存在则尝试旧的DeltaData键名（向后兼容）
                    if (fullResultData != null && (fullResultData.ContainsKey("AddData") || fullResultData.ContainsKey("DeltaData")))
                    {
                        try
                        {
                            var addDataObj = fullResultData.ContainsKey("AddData") ? fullResultData["AddData"] : fullResultData["DeltaData"];
                            List<PAData> addData = null;

                            if (addDataObj is Newtonsoft.Json.Linq.JArray addArray)
                            {
                                addData = addArray.ToObject<List<PAData>>();
                            }
                            else if (addDataObj is List<PAData> addList)
                            {
                                addData = addList;
                            }

                            PADataResults = addData ?? new List<PAData>();

                            // 更新图表
                            if (PADataResults.Count > 0)
                            {
                                UpdatePADataPlot(PADataResults);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"解析Delta数据失败: {ex.Message}");
                            PADataResults = new List<PAData>();
                        }
                    }
                    else
                    {
                        PADataResults = new List<PAData>();
                    }
                }
                else
                {
                    PADataResults = new List<PAData>();
                }
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"加载PA数据失败: {ex.Message}");
                PADataResults = new List<PAData>();
            }
        }

        private void UpdatePADataPlot(List<PAData> data)
        {
            // 实现PA数据图表更新逻辑
            // 可以复用FilmThicknessView的图表样式
        }

        public void LoadPAData(string sessionId)
        {
            try
            {
                // 从PAData表中查询指定会话的数据
                var paDataSessions = DatabaseService.Instance.QueryFullResults("PAData");
                var targetSession = paDataSessions?.FirstOrDefault(s => s["Id"].ToString() == sessionId);

                if (targetSession != null)
                {
                    var fullResultData = targetSession["FullResultData"] as Dictionary<string, object>;
                    if (fullResultData != null)
                    {
                        // 加载Pre数据
                        if (fullResultData.ContainsKey("PreData"))
                        {
                            var preDataObj = fullResultData["PreData"];
                            if (preDataObj is Newtonsoft.Json.Linq.JArray preArray)
                            {
                                PreData = preArray.ToObject<List<PAData>>();
                            }
                            else if (preDataObj is List<PAData> preList)
                            {
                                PreData = preList;
                            }
                        }

                        // 加载After数据
                        if (fullResultData.ContainsKey("AfterData"))
                        {
                            var afterDataObj = fullResultData["AfterData"];
                            if (afterDataObj is Newtonsoft.Json.Linq.JArray afterArray)
                            {
                                AfterData = afterArray.ToObject<List<PAData>>();
                            }
                            else if (afterDataObj is List<PAData> afterList)
                            {
                                AfterData = afterList;
                            }
                        }

                        // 加载Add数据（支持新旧键名）
                        if (fullResultData.ContainsKey("AddData") || fullResultData.ContainsKey("DeltaData"))
                        {
                            var addDataObj = fullResultData.ContainsKey("AddData") ? fullResultData["AddData"] : fullResultData["DeltaData"];
                            if (addDataObj is Newtonsoft.Json.Linq.JArray addArray)
                            {
                                AddData = addArray.ToObject<List<PAData>>();
                            }
                            else if (addDataObj is List<PAData> addList)
                            {
                                AddData = addList;
                            }
                        }

                        // 通知属性变更
                        OnPropertyChanged(nameof(PreData));
                        OnPropertyChanged(nameof(AfterData));
                        OnPropertyChanged(nameof(AddData));

                        // 触发图表更新
                        UpdatePAChart();

                        System.Diagnostics.Debug.WriteLine($"从JSON加载PA数据成功: Pre={PreData?.Count ?? 0}, After={AfterData?.Count ?? 0}, Add={AddData?.Count ?? 0}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到会话ID: {sessionId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载PA数据失败: {ex.Message}");
            }
        }

        private void UpdatePAChart()
        {
            // 实现PA数据图表更新逻辑
            // 示例：更新散点图
            if (PreData != null && PreData.Count > 0)
            {
                // 创建新的图表模型
                var plotModel = new PlotModel { Title = "PA数据分布" };
                var scatterSeries = new ScatterSeries();
                
                foreach (var data in PreData)
                {
                    // 添加散点数据点
                    scatterSeries.Points.Add(new ScatterPoint(data.XREL, data.YREL, 1, data.DSIZE));
                }
                
                plotModel.Series.Add(scatterSeries);
                // 更新绑定的PlotModel
                HeatMapModel = plotModel; // 或使用独立的PlotModel
            }
        }

        // 辅助方法：获取最新批次ID
        private string GetLatestBatchId(string tableName)
        {
            // 实现获取最新批次ID的逻辑
            // 可以查询数据库获取最新批次
            return Guid.NewGuid().ToString(); // 示例返回
        }

        // 加载对比颗粒数据
        private void LoadComparePAData(Dictionary<string, object> session, int sessionIndex)
        {
            try
            {
                // 根据会话的时间戳或文件路径查找对应的颗粒数据
                var filePath = session["ParsedFilePath"]?.ToString();
                var timestamp = session["Timestamp"];

                if (!string.IsNullOrEmpty(filePath))
                {
                    // 尝试根据文件路径和时间戳查找对应的颗粒数据
                    var paData = FindPADataBySession(filePath, timestamp);

                    System.Diagnostics.Debug.WriteLine($"会话{sessionIndex} - 找到颗粒数据: {paData?.Count ?? 0} 条");

                    if (sessionIndex == 1)
                    {
                        ComparePAData1 = paData;
                        ComparePAPlotModel1 = CreatePAPlotModel(paData, "会话1 - 颗粒分布");
                    }
                    else
                    {
                        ComparePAData2 = paData;
                        ComparePAPlotModel2 = CreatePAPlotModel(paData, "会话2 - 颗粒分布");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载对比颗粒数据失败: {ex.Message}");
            }
        }

        // 根据会话信息查找对应的颗粒数据
        private List<PAData> FindPADataBySession(string filePath, object timestamp)
        {
            try
            {
                // 从PAData表中查询所有会话
                var paDataSessions = DatabaseService.Instance.QueryFullResults("PAData");

                if (paDataSessions != null && paDataSessions.Count > 0)
                {
                    Dictionary<string, object> targetSession = null;

                    if (timestamp != null && DateTime.TryParse(timestamp.ToString(), out var sessionTime))
                    {
                        // 根据时间戳查找最接近的会话
                        targetSession = paDataSessions
                            .Where(s => DateTime.TryParse(s["Timestamp"].ToString(), out var t))
                            .OrderBy(s => Math.Abs((DateTime.Parse(s["Timestamp"].ToString()) - sessionTime).TotalMinutes))
                            .FirstOrDefault();
                    }
                    else
                    {
                        // 如果没有时间戳，使用最新的会话
                        targetSession = paDataSessions.FirstOrDefault();
                    }

                    if (targetSession != null)
                    {
                        var fullResultData = targetSession["FullResultData"] as Dictionary<string, object>;
                        if (fullResultData != null && (fullResultData.ContainsKey("AddData") || fullResultData.ContainsKey("DeltaData")))
                        {
                            try
                            {
                                var addDataObj = fullResultData.ContainsKey("AddData") ? fullResultData["AddData"] : fullResultData["DeltaData"];
                                List<PAData> addData = null;

                                if (addDataObj is Newtonsoft.Json.Linq.JArray addArray)
                                {
                                    addData = addArray.ToObject<List<PAData>>();
                                }
                                else if (addDataObj is List<PAData> addList)
                                {
                                    addData = addList;
                                }

                                if (addData != null && addData.Count > 0)
                                {
                                    System.Diagnostics.Debug.WriteLine($"从JSON找到颗粒数据: 数据量={addData.Count}");
                                    return addData;
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"解析颗粒数据失败: {ex.Message}");
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("未找到颗粒数据");
                return new List<PAData>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找颗粒数据失败: {ex.Message}");
                return new List<PAData>();
            }
        }

        // 获取最新的颗粒数据会话ID
        private string GetLatestPASessionId()
        {
            try
            {
                var paDataSessions = DatabaseService.Instance.QueryFullResults("PAData");
                var latestSession = paDataSessions?.FirstOrDefault();
                return latestSession?["Id"]?.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取最新颗粒数据会话ID失败: {ex.Message}");
                return null;
            }
        }



        // 创建颗粒数据的散点图模型
        private PlotModel CreatePAPlotModel(List<PAData> paData, string title)
        {
            var plotModel = new PlotModel { Title = title };

            if (paData == null || paData.Count == 0)
            {
                // 添加一个空的系列，显示"无数据"
                plotModel.Subtitle = "无颗粒数据";
                return plotModel;
            }

            // 创建散点图系列
            var scatterSeries = new ScatterSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = 4,
                MarkerFill = OxyColors.Red,
                MarkerStroke = OxyColors.DarkRed,
                Title = $"颗粒数据 ({paData.Count} 个颗粒)"
            };

            // 添加数据点
            foreach (var data in paData)
            {
                // 使用XREL和YREL作为坐标，DSIZE影响点的大小
                var markerSize = Math.Max(2, Math.Min(10, data.DSIZE * 3));
                scatterSeries.Points.Add(new ScatterPoint(data.XREL, data.YREL, markerSize));
            }

            plotModel.Series.Add(scatterSeries);

            // 设置坐标轴
            plotModel.Axes.Add(new LinearAxis { Position = AxisPosition.Bottom, Title = "X坐标 (XREL)" });
            plotModel.Axes.Add(new LinearAxis { Position = AxisPosition.Left, Title = "Y坐标 (YREL)" });

            return plotModel;
        }

        // 新的分离对比方法
        private bool CanCompareFilmThickness()
        {
            return SelectedFilmThicknessSession1 != null && SelectedFilmThicknessSession2 != null &&
                   SelectedFilmThicknessSession1 != SelectedFilmThicknessSession2;
        }

        private bool CanComparePAData()
        {
            return SelectedPADataSession1 != null && SelectedPADataSession2 != null &&
                   SelectedPADataSession1 != SelectedPADataSession2;
        }

        private void CompareFilmThicknessSessions()
        {
            if (!CanCompareFilmThickness()) return;

            try
            {
                // 设置选中的会话为对比会话
                SelectedSession1 = SelectedFilmThicknessSession1;
                SelectedSession2 = SelectedFilmThicknessSession2;

                // 调用原有的对比方法
                CompareSessions();
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"膜厚数据对比失败: {ex.Message}");
            }
        }

        private void ComparePADataSessions()
        {
            if (!CanComparePAData()) return;

            try
            {
                // 创建两个PADataViewModel实例
                var paDataViewModel1 = new PADataViewModel();
                var paDataViewModel2 = new PADataViewModel();

                // 加载第一个会话的数据
                var success1 = LoadPADataToViewModel(SelectedPADataSession1, paDataViewModel1, "会话1");

                // 加载第二个会话的数据
                var success2 = LoadPADataToViewModel(SelectedPADataSession2, paDataViewModel2, "会话2");

                if (success1 && success2)
                {
                    // 创建对比窗口
                    var compareWindow = new Window
                    {
                        Title = $"颗粒数据对比 - ID: {SelectedPADataSession1["Id"]} vs {SelectedPADataSession2["Id"]}",
                        Width = 1200,
                        Height = 800,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen
                    };

                    // 创建对比界面
                    var grid = new Grid();
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(5) });
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

                    // 左侧视图
                    var leftView = new Views.PADataView { DataContext = paDataViewModel1 };
                    leftView.SetPreviewMode(true); // 设置为预览模式，禁用操作按钮
                    Grid.SetColumn(leftView, 0);
                    grid.Children.Add(leftView);

                    // 分隔线
                    var splitter = new GridSplitter
                    {
                        Width = 5,
                        HorizontalAlignment = System.Windows.HorizontalAlignment.Stretch,
                        VerticalAlignment = System.Windows.VerticalAlignment.Stretch,
                        Background = System.Windows.Media.Brushes.Gray
                    };
                    Grid.SetColumn(splitter, 1);
                    grid.Children.Add(splitter);

                    // 右侧视图
                    var rightView = new Views.PADataView { DataContext = paDataViewModel2 };
                    rightView.SetPreviewMode(true); // 设置为预览模式，禁用操作按钮
                    Grid.SetColumn(rightView, 2);
                    grid.Children.Add(rightView);

                    compareWindow.Content = grid;

                    // 显示窗口
                    compareWindow.Show();

                    // 延迟触发颗粒可视化更新，确保窗口完全加载
                    System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
                        new Action(() => {
                            System.Diagnostics.Debug.WriteLine("对比窗口：手动触发颗粒可视化更新");

                            // 手动触发左侧视图的PropertyChanged
                            paDataViewModel1.PreData = paDataViewModel1.PreData;
                            paDataViewModel1.AfterData = paDataViewModel1.AfterData;
                            paDataViewModel1.AddData = paDataViewModel1.AddData;

                            // 手动触发右侧视图的PropertyChanged
                            paDataViewModel2.PreData = paDataViewModel2.PreData;
                            paDataViewModel2.AfterData = paDataViewModel2.AfterData;
                            paDataViewModel2.AddData = paDataViewModel2.AddData;
                        }),
                        System.Windows.Threading.DispatcherPriority.Loaded
                    );
                }
                else
                {
                    MessageBoxService.ShowError("加载颗粒数据失败，无法进行对比");
                }
            }
            catch (Exception ex)
            {
                MessageBoxService.ShowError($"颗粒数据对比失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"颗粒数据对比异常: {ex}");
            }
        }

        public bool LoadPADataToViewModel(Dictionary<string, object> session, PADataViewModel viewModel, string sessionName)
        {
            if (session == null || viewModel == null) return false;

            try
            {
                // 处理FullResultData的类型转换问题
                Dictionary<string, object> fullResultData = null;
                var fullResultDataObj = session["FullResultData"];

                if (fullResultDataObj is Dictionary<string, object> dict)
                {
                    fullResultData = dict;
                }
                else if (fullResultDataObj is Newtonsoft.Json.Linq.JObject jObject)
                {
                    fullResultData = jObject.ToObject<Dictionary<string, object>>();
                }
                else if (fullResultDataObj != null)
                {
                    try
                    {
                        var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(fullResultDataObj);
                        fullResultData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"FullResultData JSON转换失败: {ex.Message}");
                    }
                }

                if (fullResultData == null)
                {
                    System.Diagnostics.Debug.WriteLine($"加载颗粒数据到{sessionName}失败: FullResultData转换失败");
                    return false;
                }

                bool hasData = false;

                // 加载Pre数据
                if (fullResultData.ContainsKey("PreData"))
                {
                    try
                    {
                        var preDataObj = fullResultData["PreData"];
                        List<PAData> preData = null;

                        if (preDataObj is Newtonsoft.Json.Linq.JArray preArray)
                        {
                            preData = preArray.ToObject<List<PAData>>();
                        }
                        else if (preDataObj is List<PAData> preList)
                        {
                            preData = preList;
                        }

                        if (preData != null && preData.Count > 0)
                        {
                            viewModel.PreData = preData;
                            hasData = true;

                            // 更新Pre数据的PlotModel
                            viewModel.UpdatePlotModel(viewModel.PrePlotModel, preData, $"{sessionName} - Pre数据 (ID: {session["Id"]})");

                            System.Diagnostics.Debug.WriteLine($"从JSON加载Pre数据到{sessionName}: 数据量={preData.Count}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"解析Pre数据失败: {ex.Message}");
                    }
                }

                // 加载After数据
                if (fullResultData.ContainsKey("AfterData"))
                {
                    try
                    {
                        var afterDataObj = fullResultData["AfterData"];
                        List<PAData> afterData = null;

                        if (afterDataObj is Newtonsoft.Json.Linq.JArray afterArray)
                        {
                            afterData = afterArray.ToObject<List<PAData>>();
                        }
                        else if (afterDataObj is List<PAData> afterList)
                        {
                            afterData = afterList;
                        }

                        if (afterData != null && afterData.Count > 0)
                        {
                            viewModel.AfterData = afterData;
                            hasData = true;

                            // 更新After数据的PlotModel
                            viewModel.UpdatePlotModel(viewModel.AfterPlotModel, afterData, $"{sessionName} - After数据 (ID: {session["Id"]})");

                            System.Diagnostics.Debug.WriteLine($"从JSON加载After数据到{sessionName}: 数据量={afterData.Count}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"解析After数据失败: {ex.Message}");
                    }
                }

                // 加载Add数据（支持新旧键名）
                if (fullResultData.ContainsKey("AddData") || fullResultData.ContainsKey("DeltaData"))
                {
                    try
                    {
                        var addDataObj = fullResultData.ContainsKey("AddData") ? fullResultData["AddData"] : fullResultData["DeltaData"];
                        List<PAData> addData = null;

                        if (addDataObj is Newtonsoft.Json.Linq.JArray addArray)
                        {
                            addData = addArray.ToObject<List<PAData>>();
                        }
                        else if (addDataObj is List<PAData> addList)
                        {
                            addData = addList;
                        }

                        if (addData != null && addData.Count > 0)
                        {
                            viewModel.AddData = addData;
                            hasData = true;

                            // 更新Add数据的PlotModel
                            viewModel.UpdatePlotModel(viewModel.AddPlotModel, addData, $"{sessionName} - Add数据 (ID: {session["Id"]})");

                            System.Diagnostics.Debug.WriteLine($"从JSON加载Add数据到{sessionName}: 数据量={addData.Count}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"解析Add数据失败: {ex.Message}");
                    }
                }

                // 设置文件路径信息
                if (fullResultData.ContainsKey("PreFilePath"))
                {
                    viewModel.PreFilePath = fullResultData["PreFilePath"]?.ToString() ?? "";
                }
                if (fullResultData.ContainsKey("AfterFilePath"))
                {
                    viewModel.AfterFilePath = fullResultData["AfterFilePath"]?.ToString() ?? "";
                }
                if (fullResultData.ContainsKey("ConfigFilePath"))
                {
                    viewModel.ConfigFilePath = fullResultData["ConfigFilePath"]?.ToString() ?? "";
                }



                if (hasData)
                {
                    // 更新统计信息
                    viewModel.UpdateStatistics();

                    // 刷新所有图表的坐标轴范围，确保统一
                    viewModel.RefreshAllPlotModels();

                    System.Diagnostics.Debug.WriteLine($"成功加载颗粒数据到{sessionName}: Pre={viewModel.PreFilePath}, After={viewModel.AfterFilePath}, Config={viewModel.ConfigFilePath}");
                    System.Diagnostics.Debug.WriteLine($"数据统计: Pre={viewModel.PreData?.Count ?? 0}, After={viewModel.AfterData?.Count ?? 0}, Add={viewModel.AddData?.Count ?? 0}");
                    return true;
                }

                System.Diagnostics.Debug.WriteLine($"加载颗粒数据到{sessionName}失败: 无数据");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载颗粒数据到{sessionName}失败: {ex.Message}");
                return false;
            }
        }

        private void LoadPADataForSession(Dictionary<string, object> session)
        {
            try
            {
                if (session == null) return;

                var filePath = session.ContainsKey("ParsedFilePath") ? session["ParsedFilePath"]?.ToString() : null;
                var timestamp = session.ContainsKey("Timestamp") ? session["Timestamp"] : null;

                if (string.IsNullOrEmpty(filePath)) return;

                // 查找对应的颗粒数据
                var paDataList = FindPADataBySession(filePath, timestamp);
                
                if (paDataList != null && paDataList.Count > 0)
                {
                    // 分离Pre、After、Add数据（支持新旧BatchId）
                    PreData = paDataList.Where(p => p.BatchId.Contains("Pre")).ToList();
                    AfterData = paDataList.Where(p => p.BatchId.Contains("After")).ToList();
                    AddData = paDataList.Where(p => p.BatchId.Contains("Add") || p.BatchId.Contains("Delta")).ToList();

                    System.Diagnostics.Debug.WriteLine($"加载颗粒数据成功: Pre={PreData.Count}, After={AfterData.Count}, Add={AddData.Count}");
                }
                else
                {
                    // 清空数据
                    PreData.Clear();
                    AfterData.Clear();
                    AddData.Clear();
                    System.Diagnostics.Debug.WriteLine("未找到对应的颗粒数据");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载颗粒数据失败: {ex.Message}");
                PreData.Clear();
                AfterData.Clear();
                AddData.Clear();
            }
        }
    }

}


