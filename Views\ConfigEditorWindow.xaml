<Window x:Class="DatalogDrawing.ConfigEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="CSV解析配置编辑器"
        Width="1000" Height="700"
        WindowStartupLocation="CenterOwner">
    
    <Window.Resources>
        <x:Array x:Key="DataTypeValues" Type="sys:String">
            <sys:String>string</sys:String>
            <sys:String>int</sys:String>
            <sys:String>double</sys:String>
            <sys:String>datetime</sys:String>
            <sys:String>bool</sys:String>
        </x:Array>
    </Window.Resources>
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TabControl Grid.Row="1" Margin="0,10,0,10">
            <!-- CSV解析配置选项卡 -->
            <TabItem Header="CSV解析配置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 基本设置 -->
                        <GroupBox Header="基本设置" Margin="0,0,0,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Label Grid.Row="0" Grid.Column="0" Content="分隔符:" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Config.CsvParserConfig.Delimiter, UpdateSourceTrigger=PropertyChanged}" Margin="0,2"/>

                                <Label Grid.Row="1" Grid.Column="0" Content="编码:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Row="1" Grid.Column="1" SelectedItem="{Binding Config.CsvParserConfig.Encoding}" Margin="0,2">
                                    <ComboBoxItem Content="UTF-8"/>
                                    <ComboBoxItem Content="GBK"/>
                                    <ComboBoxItem Content="ASCII"/>
                                </ComboBox>
                            </Grid>
                        </GroupBox>

                        <!-- 行过滤器 -->
                        <GroupBox Header="行过滤器" Margin="0,0,0,10">
                            <StackPanel>
                                <DataGrid ItemsSource="{Binding Config.CsvParserConfig.RowFilters}" AutoGenerateColumns="False" Height="200">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="120"/>
                                        <DataGridCheckBoxColumn Header="启用" Binding="{Binding Enabled}" Width="60"/>
                                        <DataGridComboBoxColumn Header="过滤类型" SelectedItemBinding="{Binding FilterType}" Width="100">
                                            <DataGridComboBoxColumn.ItemsSource>
                                                <x:Array Type="sys:String">
                                                    <sys:String>RowIndex</sys:String>
                                                    <sys:String>ColumnValue</sys:String>
                                                    <sys:String>RowInterval</sys:String>
                                                </x:Array>
                                            </DataGridComboBoxColumn.ItemsSource>
                                        </DataGridComboBoxColumn>
                                        <DataGridTextColumn Header="列索引" Binding="{Binding ColumnIndex}" Width="80"/>
                                        <DataGridTextColumn Header="列名" Binding="{Binding ColumnName}" Width="100"/>
                                        <DataGridComboBoxColumn Header="条件" SelectedItemBinding="{Binding Condition}" Width="100">
                                            <DataGridComboBoxColumn.ItemsSource>
                                                <x:Array Type="sys:String">
                                                    <sys:String>Equals</sys:String>
                                                    <sys:String>Contains</sys:String>
                                                    <sys:String>StartsWith</sys:String>
                                                    <sys:String>EndsWith</sys:String>
                                                    <sys:String>GreaterThan</sys:String>
                                                    <sys:String>LessThan</sys:String>
                                                    <sys:String>NotEquals</sys:String>
                                                </x:Array>
                                            </DataGridComboBoxColumn.ItemsSource>
                                        </DataGridComboBoxColumn>
                                        <DataGridTextColumn Header="值" Binding="{Binding Value}" Width="100"/>
                                        <DataGridTextColumn Header="开始行" Binding="{Binding StartRow}" Width="80"/>
                                        <DataGridTextColumn Header="结束行" Binding="{Binding EndRow}" Width="80"/>
                                        <DataGridTextColumn Header="行间隔" Binding="{Binding RowInterval}" Width="80"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <Button Content="添加过滤器" Command="{Binding AddRowFilterCommand}" Width="100" Margin="0,0,5,0"/>
                                    <Button Content="删除过滤器" Command="{Binding RemoveRowFilterCommand}" Width="100"/>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 显示配置选项卡 -->
            <TabItem Header="显示配置">
                <StackPanel Margin="10">
                    <GroupBox Header="表格列显示">
                        <StackPanel>
                            <ListBox ItemsSource="{Binding Config.DisplayConfig.TableColumns}" Height="150">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBox Text="{Binding Path=., UpdateSourceTrigger=PropertyChanged}" Width="150"/>
                                            <Button Content="删除" Margin="5,0,0,0" 
                                                    Command="{Binding DataContext.RemoveColumnCommand, RelativeSource={RelativeSource AncestorType=Window}}" 
                                                    CommandParameter="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            <Button Content="添加列" Margin="0,5,0,0" HorizontalAlignment="Left" 
                                    Command="{Binding AddColumnCommand}"/>
                        </StackPanel>
                    </GroupBox>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Content="图表X轴:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Config.DisplayConfig.ChartXAxis, UpdateSourceTrigger=PropertyChanged}"/>

                        <Label Grid.Row="1" Grid.Column="0" Content="图表Y轴:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Config.DisplayConfig.ChartYAxis, UpdateSourceTrigger=PropertyChanged}"/>

                        <Label Grid.Row="2" Grid.Column="0" Content="最大显示行数:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Config.DisplayConfig.MaxRowsDisplay, UpdateSourceTrigger=PropertyChanged}"/>

                        <Label Grid.Row="3" Grid.Column="0" Content="自动刷新:" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" IsChecked="{Binding Config.DisplayConfig.AutoRefresh}" VerticalAlignment="Center"/>
                        
                        <Label Grid.Row="4" Grid.Column="0" Content="用户输入值:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Config.DisplayConfig.UserInputValue, UpdateSourceTrigger=PropertyChanged}"/>
                        
                        <TextBlock Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" Text="注：用户输入值用于计算GPC[A/C]，默认为1.0" Margin="5" Foreground="Gray"/>
                    </Grid>
                </StackPanel>
            </TabItem>
        </TabControl>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="导出配置" Command="{Binding ExportConfigCommand}" Width="80"/>
            <Button Content="导入配置" Command="{Binding ImportConfigCommand}" Width="80"/>
            <Button Content="保存" Command="{Binding SaveConfigCommand}" Width="80" IsDefault="True"/>
            <Button Content="取消" Command="{Binding CancelCommand}" Width="80" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>