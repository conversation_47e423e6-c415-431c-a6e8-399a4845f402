using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using DatalogDrawing.Models;
using DatalogDrawing.Interfaces;
using DatalogDrawing.Services;
using DatalogDrawing.Helpers;  // 添加Helpers引用
using System.Threading.Tasks;  // 添加Task所需命名空间
using Newtonsoft.Json;  // 添加JSON序列化支持

namespace DatalogDrawing.Services
{
    public class PAParserService : BaseParser, IDataParser<PAData>
    {
        public override Dictionary<string, object> Parse(string filePath, AppConfig config)
        {
            // 将AppConfig转换为Dictionary<string, object>格式
            var configDict = new Dictionary<string, object>();
            // 添加必要的配置项到字典中
            if (config?.CsvParserConfig != null)
            {
                configDict["delimiter"] = config.CsvParserConfig.Delimiter;
                // 可以根据需要添加更多配置项
            }
            
            var rawData = ReadFileContent(filePath);
            var lines = rawData.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            var filteredLines = ApplyFilters(lines, configDict);
            // 这里应该实现具体的解析逻辑
            return new Dictionary<string, object>();
        }

        public Dictionary<string, object> ParseDualFiles(string preFilePath, string afterFilePath, string configPath)
        {
            var config = LoadConfig(configPath);
            var preData = ParseFile(preFilePath, config);
            var afterData = ParseFile(afterFilePath, config);
            var results = CalculateDeltas(preData, afterData, config);

            return new Dictionary<string, object>
            {
                { "PreData", preData },
                { "AfterData", afterData },
                { "DeltaData", results }
            };
        }

        private List<PAData> ParseFile(string filePath, AppConfig config)
        {
            var rawData = ReadFileContent(filePath);
            var lines = rawData.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            var configDict = ConvertAppConfigToDictionary(config);
            var filteredLines = ApplyFilters(lines, configDict);
            return MapToPAData(filteredLines, filePath, config);
        }

        private Dictionary<string, object> ConvertAppConfigToDictionary(AppConfig config)
        {
            var configDict = new Dictionary<string, object>();
            if (config?.CsvParserConfig != null)
            {
                configDict["delimiter"] = config.CsvParserConfig.Delimiter;
                // Add other configuration items as needed
            }
            return configDict;
        }

        private List<PAData> MapToPAData(List<string> lines, string filePath, AppConfig config)
        {
            var result = new List<PAData>();

            // 检查文件格式
            if (filePath.EndsWith(".001") || filePath.EndsWith(".001", StringComparison.OrdinalIgnoreCase))
            {
                return Parse001Format(lines, filePath);
            }
            else
            {
                // CSV格式解析（保留原有逻辑）
                return ParseCsvFormat(lines, filePath, config);
            }
        }

        private List<PAData> Parse001Format(List<string> lines, string filePath)
        {
            var result = new List<PAData>();
            bool inDefectList = false;

            foreach (var line in lines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                // 检查是否开始DefectList段
                if (line.Trim().StartsWith("DefectList"))
                {
                    inDefectList = true;
                    continue;
                }

                // 检查是否结束DefectList段
                if (line.Trim().EndsWith(";") && inDefectList)
                {
                    // 如果这行包含数据，先处理这行
                    if (!line.Trim().Equals(";"))
                    {
                        var data = ParseDefectLine(line, filePath);
                        if (data != null) result.Add(data);
                    }
                    break;
                }

                // 解析DefectList中的数据行
                if (inDefectList && !line.Trim().StartsWith("DefectList"))
                {
                    var data = ParseDefectLine(line, filePath);
                    if (data != null) result.Add(data);
                }
            }

            return result;
        }

        private PAData ParseDefectLine(string line, string filePath)
        {
            try
            {
                // 移除行末的分号
                line = line.TrimEnd(';').Trim();
                if (string.IsNullOrEmpty(line)) return null;

                // 按空格分割数据
                var values = line.Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                if (values.Length < 12) return null; // 至少需要12个字段

                // 根据DefectRecordSpec格式解析所有字段：
                // DEFECTID XREL YREL XINDEX YINDEX XSIZE YSIZE DEFECTAREA DSIZE CLASSNUMBER TEST CLUSTERNUMBER ROUGHBINNUMBER FINEBINNUMBER REVIEWSAMPLE IMAGECOUNT IMAGELIST
                return new PAData
                {
                    DEFECTID = int.TryParse(values[0], out var defectId) ? defectId : 0,
                    XREL = double.TryParse(values[1], out var x) ? x : 0.0,
                    YREL = double.TryParse(values[2], out var y) ? y : 0.0,
                    XINDEX = int.TryParse(values[3], out var xIndex) ? xIndex : 0,
                    YINDEX = int.TryParse(values[4], out var yIndex) ? yIndex : 0,
                    XSIZE = double.TryParse(values[5], out var xSize) ? xSize : 0.0,
                    YSIZE = double.TryParse(values[6], out var ySize) ? ySize : 0.0,
                    DEFECTAREA = double.TryParse(values[7], out var defectArea) ? defectArea : 0.0,
                    DSIZE = double.TryParse(values[8], out var d) ? d : 0.0,
                    CLASSNUMBER = int.TryParse(values[9], out var classNumber) ? classNumber : 0,
                    TEST = int.TryParse(values[10], out var test) ? test : 0,
                    CLUSTERNUMBER = int.TryParse(values[11], out var clusterNumber) ? clusterNumber : 0,
                    ROUGHBINNUMBER = values.Length > 12 ? (int.TryParse(values[12], out var roughBin) ? roughBin : 0) : 0,
                    FINEBINNUMBER = values.Length > 13 ? (int.TryParse(values[13], out var fineBin) ? fineBin : 0) : 0,
                    REVIEWSAMPLE = values.Length > 14 ? (int.TryParse(values[14], out var reviewSample) ? reviewSample : 0) : 0,
                    IMAGECOUNT = values.Length > 15 ? (int.TryParse(values[15], out var imageCount) ? imageCount : 0) : 0,
                    IMAGELIST = values.Length > 16 ? values[16] : "0",
                    FilePath = filePath,
                    Timestamp = DateTime.Now,
                    WaferId = ExtractWaferId(filePath),
                    SlotNumber = ExtractSlotNumber(filePath)
                };
            }
            catch
            {
                return null;
            }
        }

        private List<PAData> ParseCsvFormat(List<string> lines, string filePath, AppConfig config)
        {
            var result = new List<PAData>();
            var delimiter = config?.CsvParserConfig?.Delimiter ?? ",";

            // 跳过第一行（标题行）
            var dataLines = lines.Skip(1);

            foreach (var line in dataLines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                var values = ParseCsvLine(line, delimiter);
                if (values.Count < 4) continue; // Skip invalid lines

                result.Add(new PAData
                {
                    DEFECTID = result.Count + 1, // 自动生成ID
                    XREL = double.TryParse(values[0], out var x) ? x : 0.0,
                    YREL = double.TryParse(values[1], out var y) ? y : 0.0,
                    XINDEX = 0,
                    YINDEX = 0,
                    XSIZE = 0.0,
                    YSIZE = 0.0,
                    DEFECTAREA = 0.0,
                    DSIZE = double.TryParse(values[2], out var d) ? d : 0.0,
                    CLASSNUMBER = 0,
                    TEST = int.TryParse(values[3], out var c) ? c : 0, // 使用第4列作为TEST字段
                    CLUSTERNUMBER = 0,
                    ROUGHBINNUMBER = 0,
                    FINEBINNUMBER = 0,
                    REVIEWSAMPLE = 0,
                    IMAGECOUNT = 0,
                    IMAGELIST = "0",
                    FilePath = filePath,
                    Timestamp = DateTime.Now
                });
            }
            return result;
        }

        private string ExtractWaferId(string filePath)
        {
            try
            {
                var fileName = System.IO.Path.GetFileName(filePath);
                // 从文件名中提取WaferID，例如：1-FUR-BARE_0.025_Pre_WNONE_s16_2025-04-03-15-43-31.001
                var parts = fileName.Split('_');
                foreach (var part in parts)
                {
                    if (part.StartsWith("s") && int.TryParse(part.Substring(1), out _))
                    {
                        return part; // 返回如 "s16"
                    }
                }
                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        private string ExtractSlotNumber(string filePath)
        {
            try
            {
                var fileName = System.IO.Path.GetFileName(filePath);
                var parts = fileName.Split('_');
                foreach (var part in parts)
                {
                    if (part.StartsWith("s") && int.TryParse(part.Substring(1), out var slot))
                    {
                        return slot.ToString();
                    }
                }
                return "0";
            }
            catch
            {
                return "0";
            }
        }

        private List<PAData> CalculateDeltas(List<PAData> preData, List<PAData> afterData, AppConfig config)
        {
            var result = new List<PAData>();

            // 从AppConfig中获取距离阈值
            var distanceTolerance = config?.DisplayConfig?.ParticleSpacingDistance ?? 130.0;

            System.Diagnostics.Debug.WriteLine($"CalculateAdd: Pre数据 {preData.Count} 个点, After数据 {afterData.Count} 个点");

            // 只添加After中新增的颗粒（不在Pre中的颗粒）
            foreach (var after in afterData)
            {
                // 计算与Pre中所有点的距离，找到最近的点
                var matchInPre = preData.FirstOrDefault(p =>
                {
                    var distance = Math.Sqrt(Math.Pow(p.XREL - after.XREL, 2) + Math.Pow(p.YREL - after.YREL, 2));
                    return distance < distanceTolerance;
                });

                if (matchInPre == null)
                {
                    // 这是一个新增的颗粒（在After中存在但在Pre中不存在）
                    var addData = new PAData
                    {
                        DEFECTID = after.DEFECTID,
                        XREL = after.XREL,
                        YREL = after.YREL,
                        XINDEX = after.XINDEX,
                        YINDEX = after.YINDEX,
                        XSIZE = after.XSIZE,
                        YSIZE = after.YSIZE,
                        DEFECTAREA = after.DEFECTAREA,
                        DSIZE = after.DSIZE,
                        CLASSNUMBER = after.CLASSNUMBER,
                        TEST = after.TEST,
                        CLUSTERNUMBER = after.CLUSTERNUMBER,
                        ROUGHBINNUMBER = after.ROUGHBINNUMBER,
                        FINEBINNUMBER = after.FINEBINNUMBER,
                        REVIEWSAMPLE = after.REVIEWSAMPLE,
                        IMAGECOUNT = after.IMAGECOUNT,
                        IMAGELIST = after.IMAGELIST,
                        DeltaDSIZE = after.DSIZE, // 新增颗粒的大小
                        FilePath = after.FilePath,
                        Timestamp = after.Timestamp,
                        WaferId = after.WaferId,
                        SlotNumber = after.SlotNumber
                    };
                    result.Add(addData);
                    System.Diagnostics.Debug.WriteLine($"新增颗粒: ID={after.DEFECTID}, X={after.XREL}, Y={after.YREL}, Size={after.DSIZE}");
                }
                else
                {
                    var distance = Math.Sqrt(Math.Pow(matchInPre.XREL - after.XREL, 2) + Math.Pow(matchInPre.YREL - after.YREL, 2));
                    System.Diagnostics.Debug.WriteLine($"重复颗粒(已排除): After ID={after.DEFECTID} 匹配 Pre ID={matchInPre.DEFECTID}, 距离={distance:F1}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"CalculateAdd: 找到 {result.Count} 个新增颗粒");
            return result;
        }

        protected override List<string> ApplyFilters(List<string> data, Dictionary<string, object> config)
        {
            // 实现过滤逻辑
            if (config.ContainsKey("ignoreHeader") && (bool)config["ignoreHeader"])
            {
                data = data.Skip(1).ToList();
            }

            return base.ApplyFilters(data, config);
        }

        protected override List<string> ParseCsvLine(string line, string delimiter)
        {
            return base.ParseCsvLine(line, delimiter);
        }
        
        private AppConfig LoadConfig(string configPath)
        {
            // 实现配置文件加载逻辑
            if (string.IsNullOrEmpty(configPath) || !System.IO.File.Exists(configPath))
            {
                // 返回默认配置
                return new AppConfig
                {
                    CsvParserConfig = new CsvParserConfig
                    {
                        Delimiter = ",",
                        Encoding = "UTF-8"
                    }
                };
            }

            try
            {
                var json = System.IO.File.ReadAllText(configPath);
                var config = Newtonsoft.Json.JsonConvert.DeserializeObject<AppConfig>(json);
                return config ?? new AppConfig
                {
                    CsvParserConfig = new CsvParserConfig
                    {
                        Delimiter = ",",
                        Encoding = "UTF-8"
                    }
                };
            }
            catch
            {
                // 如果加载失败，返回默认配置
                return new AppConfig
                {
                    CsvParserConfig = new CsvParserConfig
                    {
                        Delimiter = ",",
                        Encoding = "UTF-8"
                    }
                };
            }
        }

        // 实现IDataParser<PAData>接口
        public PAData ParseData(string filePath, AppConfig config)
        {
            var content = ReadFileContent(filePath);
            var lines = content.Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            // 添加具体解析逻辑
            if (lines.Length < 1) return new PAData();

            var delimiter = config?.CsvParserConfig?.Delimiter ?? ",";
            var values = ParseCsvLine(lines[0], delimiter);
            if (values.Count < 4) return new PAData(); // Skip invalid line

            return new PAData
            {
                DEFECTID = 1,
                XREL = double.TryParse(values[0], out var x) ? x : 0.0,
                YREL = double.TryParse(values[1], out var y) ? y : 0.0,
                XINDEX = 0,
                YINDEX = 0,
                XSIZE = 0.0,
                YSIZE = 0.0,
                DEFECTAREA = 0.0,
                DSIZE = double.TryParse(values[2], out var d) ? d : 0.0,
                CLASSNUMBER = 0,
                TEST = int.TryParse(values[3], out var c) ? c : 0,
                CLUSTERNUMBER = 0,
                ROUGHBINNUMBER = 0,
                FINEBINNUMBER = 0,
                REVIEWSAMPLE = 0,
                IMAGECOUNT = 0,
                IMAGELIST = "0",
                FilePath = filePath
            };
        }
        
        public Task<Dictionary<string, object>> ParseAsync(string filePath, AppConfig config)
        {
            return Task.FromResult(Parse(filePath, config));
        }
    }
}