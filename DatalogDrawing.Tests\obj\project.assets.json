{"version": 3, "targets": {".NETFramework,Version=v4.5.1": {"coverlet.collector/3.2.0": {"type": "package", "build": {"build/netstandard1.0/coverlet.collector.targets": {}}}, "Microsoft.NET.Test.Sdk/17.8.0": {"type": "package", "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "MSTest.TestAdapter/2.2.10": {"type": "package", "build": {"build/net45/MSTest.TestAdapter.props": {}, "build/net45/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/2.2.10": {"type": "package", "compile": {"lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"related": ".XML"}, "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".Extensions.XML;.XML"}}, "runtime": {"lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"related": ".XML"}, "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".Extensions.XML;.XML"}}}, "DatalogDrawing/1.0.0": {"type": "project", "compile": {"bin/placeholder/DatalogDrawing.dll": {}}, "runtime": {"bin/placeholder/DatalogDrawing.dll": {}}}}}, "libraries": {"coverlet.collector/3.2.0": {"sha512": "xjY8xBigSeWIYs4I7DgUHqSNoGqnHi7Fv7/7RZD02rvZyG3hlsjnQKiVKVWKgr9kRKgmV+dEfu8KScvysiC0Wg==", "type": "package", "path": "coverlet.collector/3.2.0", "files": [".nupkg.metadata", ".signature.p7s", "build/netstandard1.0/Microsoft.Bcl.AsyncInterfaces.dll", "build/netstandard1.0/Microsoft.CSharp.dll", "build/netstandard1.0/Microsoft.DotNet.PlatformAbstractions.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyInjection.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyModel.dll", "build/netstandard1.0/Microsoft.Extensions.FileSystemGlobbing.dll", "build/netstandard1.0/Microsoft.TestPlatform.CoreUtilities.dll", "build/netstandard1.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "build/netstandard1.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "build/netstandard1.0/Mono.Cecil.Mdb.dll", "build/netstandard1.0/Mono.Cecil.Pdb.dll", "build/netstandard1.0/Mono.Cecil.Rocks.dll", "build/netstandard1.0/Mono.Cecil.dll", "build/netstandard1.0/Newtonsoft.Json.dll", "build/netstandard1.0/NuGet.Frameworks.dll", "build/netstandard1.0/System.AppContext.dll", "build/netstandard1.0/System.Collections.Immutable.dll", "build/netstandard1.0/System.Dynamic.Runtime.dll", "build/netstandard1.0/System.IO.FileSystem.Primitives.dll", "build/netstandard1.0/System.Linq.Expressions.dll", "build/netstandard1.0/System.Linq.dll", "build/netstandard1.0/System.ObjectModel.dll", "build/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "build/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "build/netstandard1.0/System.Reflection.Emit.dll", "build/netstandard1.0/System.Reflection.Metadata.dll", "build/netstandard1.0/System.Reflection.TypeExtensions.dll", "build/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "build/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "build/netstandard1.0/System.Text.RegularExpressions.dll", "build/netstandard1.0/System.Threading.Tasks.Extensions.dll", "build/netstandard1.0/System.Threading.dll", "build/netstandard1.0/System.Xml.ReaderWriter.dll", "build/netstandard1.0/System.Xml.XDocument.dll", "build/netstandard1.0/coverlet.collector.deps.json", "build/netstandard1.0/coverlet.collector.dll", "build/netstandard1.0/coverlet.collector.pdb", "build/netstandard1.0/coverlet.collector.targets", "build/netstandard1.0/coverlet.core.dll", "build/netstandard1.0/coverlet.core.pdb", "coverlet-icon.png", "coverlet.collector.3.2.0.nupkg.sha512", "coverlet.collector.nuspec"]}, "Microsoft.NET.Test.Sdk/17.8.0": {"sha512": "BmTYGbD/YuDHmApIENdoyN1jCk0Rj1fJB0+B/fVekyTdVidr91IlzhqzytiUgaEAzL1ZJcYCme0MeBMYvJVzvw==", "type": "package", "path": "microsoft.net.test.sdk/17.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_MIT.txt", "build/net462/Microsoft.NET.Test.Sdk.props", "build/net462/Microsoft.NET.Test.Sdk.targets", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.fs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.vb", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "lib/net462/_._", "lib/netcoreapp3.1/_._", "microsoft.net.test.sdk.17.8.0.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "MSTest.TestAdapter/2.2.10": {"sha512": "KOc7XVNM0Q5GrTAx4RhxTgwdt9O5gOqSzmLpUMyl9ywa6vvUNFVQ9nCjtEE7qDQW54MZdc82e287PzZDc7yQtA==", "type": "package", "path": "mstest.testadapter/2.2.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/_common/Microsoft.TestPlatform.AdapterUtilities.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.Interface.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/_common/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_common/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/net45/MSTest.TestAdapter.props", "build/net45/MSTest.TestAdapter.targets", "build/net45/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net45/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net45/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/MSTest.TestAdapter.props", "build/net46/MSTest.TestAdapter.targets", "build/net46/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net46/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net46/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net46/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/MSTest.TestAdapter.props", "build/net5.0/MSTest.TestAdapter.targets", "build/net5.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net5.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net5.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/winui/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net5.0/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net5.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/MSTest.TestAdapter.props", "build/netcoreapp1.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netcoreapp1.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netcoreapp1.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp1.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/MSTest.TestAdapter.props", "build/netstandard1.5/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netstandard1.5/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netstandard1.5/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard1.5/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "build/uap10.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/uap10.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "mstest.testadapter.2.2.10.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/2.2.10": {"sha512": "JZRVXKq19uRhkj8MuzsU8zJhPV2JV3ZToFPAIg+BU53L1L9mNDfm9jXerdRfbrE4HBcf2M54Ij80zPOdlha3+Q==", "type": "package", "path": "mstest.testframework/2.2.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net5.0/MSTest.TestFramework.targets", "build/net5.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "build/net5.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net5.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "build/net5.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net5.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net5.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net45/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/net5.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net5.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net5.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard1.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "mstest.testframework.2.2.10.nupkg.sha512", "mstest.testframework.nuspec"]}, "DatalogDrawing/1.0.0": {"type": "project", "path": "../DatalogDrawing.csproj", "msbuildProject": "../DatalogDrawing.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.5.1": ["DatalogDrawing >= 1.0.0", "MSTest.TestAdapter >= 2.2.10", "MSTest.TestFramework >= 2.2.10", "Microsoft.NET.Test.Sdk >= 17.8.0", "coverlet.collector >= 3.2.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Software\\Vs2022Share\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\DatalogDrawing.Tests.csproj", "projectName": "DatalogDrawing.Tests", "projectPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\DatalogDrawing.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Software\\Vs2022Share\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net451"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net451": {"targetAlias": "net451", "projectReferences": {"E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj": {"projectPath": "E:\\Code\\WPF\\DatalogDrawing\\DatalogDrawing.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net451": {"targetAlias": "net451", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[2.2.10, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[2.2.10, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[3.2.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.200\\RuntimeIdentifierGraph.json"}}}}