# CSV数据解析工具

## 概述
这是一个基于WPF的CSV数据解析和可视化工具，专门用于处理包含SLOT信息的复杂CSV文件。

## 主要功能

### 1. 智能SLOT数据解析
- **自动SLOT检测**：自动识别文件中的SLOT块
- **完整数据解析**：自动解析所有统计信息和测量点数据
- **数据关联**：自动关联SLOT信息（编号、晶圆ID、采集日期）

### 2. 灵活显示模式
- **全部数据**：显示所有解析的数据（统计信息 + 测量数据）
- **仅统计信息**：只显示MEAN、MIN、MAX、STDDEV等统计值
- **仅测量数据**：只显示Site #1-49的详细测量数据

### 3. 灵活配置
- **可视化配置编辑器**：图形化编辑解析规则
- **智能配置管理**：自动加载合适的配置文件
- **实时预览**：配置修改后实时预览效果

### 4. 数据可视化
- **表格视图**：清晰展示解析结果
- **图表视图**：支持折线图、柱状图、散点图
- **数据对比**：支持历史数据对比分析

### 5. 汇总对比功能（最新更新）
- **完整膜厚图表对比**：直接复用FilmThicknessView组件，确保100%一致的展示效果
- **12行完整数据**：包含Measuring Equipment、Position、THK[A]、Range[A]、STDDEV[A]、Unif.[+/-%]、RI、GOF、WTW Unif、GPC[A/C]、α/x[%]、THK MAP等所有数据
- **热力图对比**：保留并优化热力图对比功能
- **配置一致性**：使用相同的配置选项（小数位数、设备名称等）
- **代码复用**：直接复用FilmThicknessViewModel和FilmThicknessView，完全避免重复造轮子
- **界面一致性**：通过直接复用组件确保界面完全一致

## 文件结构

### 配置文件
- `slot_complete_config.json` - 完整解析配置（自动加载）

### 核心文件
- `MainWindow.xaml` - 主界面
- `CsvParserService.cs` - CSV解析服务
- `ConfigService.cs` - 配置管理服务
- `ConfigEditorWindow.xaml` - 配置编辑器

### 汇总对比相关文件
- `ViewModels/SummaryViewModel.cs` - 汇总视图模型（已更新对比功能）
- `Views/ComparePopupWindow.xaml` - 对比弹窗界面（已更新为直接复用FilmThicknessView）
- `Views/SummaryView.xaml` - 汇总视图界面

## 使用方法

### 1. 导入CSV文件
- 点击"导入CSV"按钮
- 选择包含SLOT数据的CSV文件

### 2. 解析数据
- 点击"解析"按钮
- 系统自动解析所有SLOT数据（统计信息 + 测量数据）

### 3. 选择显示模式
在工具栏的"显示模式"下拉框中选择：
- **全部数据**：查看所有解析的数据
- **仅统计信息**：只查看统计汇总数据
- **仅测量数据**：只查看详细测量数据

### 4. 查看结果
- **表格视图**：显示当前模式下的数据
- **图表视图**：可视化数据分布
- **对比视图**：与历史数据对比

### 5. 汇总对比功能
- **选择会话**：在汇总页面选择两个不同的会话
- **点击对比**：系统将弹出对比窗口
- **查看膜厚图表**：直接查看完整的12行膜厚数据对比（复用FilmThicknessView组件）
- **热力图对比**：选择不同的Slot查看对应的热力图对比

## 技术实现

### 汇总对比功能实现要点：
1. **组件复用**：直接复用FilmThicknessView组件，确保界面完全一致
2. **实例管理**：为每个对比会话创建独立的FilmThicknessViewModel实例
3. **配置同步**：确保对比功能使用与膜厚模块相同的配置选项
4. **显示优化**：在对比模式下隐藏左侧文件列表，直接显示表格数据

### 数据一致性保证：
- 通过直接复用FilmThicknessView组件确保100%一致的展示效果
- 包含所有12行数据：设备信息、位置信息、厚度数据、统计指标等
- 支持相同的配置选项和格式化规则