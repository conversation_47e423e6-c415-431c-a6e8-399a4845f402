 using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using DatalogDrawing.Models;
using DatalogDrawing.Services;

namespace DatalogDrawing.Views
{
    public partial class SlotSelectionWindow : Window
    {
        public ObservableCollection<SlotViewModel> Slots { get; } = new ObservableCollection<SlotViewModel>();
        
        public List<string> SelectedSlotNumbers { get; private set; } = new List<string>();
        public Dictionary<string, string> SlotDisplayNames { get; private set; } = new Dictionary<string, string>();

        public SlotSelectionWindow(Dictionary<string, SlotInfo> slotInfos)
        {
            InitializeComponent();
            
            foreach (var slot in slotInfos)
            {
                Slots.Add(new SlotViewModel
                {
                    SlotNumber = slot.Value.SlotNumber.ToString(),
                    WaferId = slot.Value.WaferId,
                    DetailCount = slot.Value.SlotDetailInfo.Count,
                    TotalCount = slot.Value.SlotTotalInfo.Count,
                    IsSelected = true
                });
            }
            
            SlotsDataGrid.ItemsSource = Slots;
        }

        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var slot in Slots)
            {
                slot.IsSelected = true;
            }
        }

        private void UnselectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var slot in Slots)
            {
                slot.IsSelected = false;
            }
        }

        private void Confirm_Click(object sender, RoutedEventArgs e)
        {
            SelectedSlotNumbers = new List<string>();
            
            foreach (var slot in Slots)
            {
                if (slot.IsSelected)
                {
                    SelectedSlotNumbers.Add(slot.SlotNumber);
                }
            }
            
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class SlotViewModel : INotifyPropertyChanged
    {
        private bool _isSelected;

        public string SlotNumber { get; set; }
        public string WaferId { get; set; }
        public int DetailCount { get; set; }
        public int TotalCount { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}