using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using DatalogDrawing.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace DatalogDrawing.Services
{
    public class DatabaseService
    {
        private static DatabaseService _instance;
        private static readonly object _lock = new object();
        private readonly string _dbPath;
        private readonly string _connectionString;

        // 静态构造函数，确保 SQLite 提供程序在任何实例创建前初始化
        static DatabaseService()
        {
            // 使用 System.Data.SQLite，不需要初始化
        }

        private DatabaseService()
        {
            try
            {
                // 修改数据库路径到执行文件目录
                var exeDirectory = System.AppDomain.CurrentDomain.BaseDirectory;
                _dbPath = Path.Combine(exeDirectory, "app_data.db");
                _connectionString = $"Data Source={_dbPath};Version=3;";
                InitializeDatabase();

                System.Diagnostics.Debug.WriteLine($"数据库路径: {_dbPath}");
            }
            catch (Exception ex)
            {
                // 记录错误日志
                File.WriteAllText(Path.Combine(Path.GetTempPath(), "db_init_error.log"),
                    $"数据库初始化失败: {ex.Message}\n{ex.StackTrace}");
                // 抛出异常让调用者处理
                throw new Exception("数据库初始化失败", ex);
            }
        }

        public static DatabaseService Instance
        {
            get
            {
                lock (_lock)
                {
                    return _instance ?? (_instance = new DatabaseService());
                }
            }
        }

        // 辅助方法：安全获取字典值，如果不存在则返回默认值
        private static T GetValueOrDefault<T>(Dictionary<string, object> dict, string key, T defaultValue)
        {
            if (dict == null) return defaultValue;
            
            if (dict.TryGetValue(key, out object value) && value != null)
            {
                if (value is T typedValue)
                    return typedValue;
                
                try
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            
            return defaultValue;
        }

        public void InitializeDatabase()
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                connection.Open();
                
                // 创建PAData表（用于保存颗粒数据会话）
                var createPADataTable = $@"CREATE TABLE IF NOT EXISTS PAData ("
                    + "Id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + "ModuleType TEXT NOT NULL,"
                    + "ParsedFilePath TEXT,"
                    + "FullResultData TEXT,"
                    + "Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP"
                    + ")";
                using (var command = new SQLiteCommand(createPADataTable, connection))
                {
                    command.ExecuteNonQuery();
                }

                // 创建FilmThickness表（用于保存膜厚数据会话）
                var createFilmThicknessTable = $@"CREATE TABLE IF NOT EXISTS FilmThickness ("
                    + "Id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + "ModuleType TEXT NOT NULL,"
                    + "ParsedFilePath TEXT,"
                    + "ConfigFilePath TEXT,"
                    + "UserInputValue REAL,"
                    + "FullResultData TEXT,"
                    + "HeatMapData TEXT,"
                    + "Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP"
                    + ")";
                using (var command = new SQLiteCommand(createFilmThicknessTable, connection))
                {
                    command.ExecuteNonQuery();
                }
                
                // 执行数据库迁移
                MigrateDatabase(connection);

                System.Diagnostics.Debug.WriteLine("数据库表创建完成");
                LogDatabaseStructure();
            }
        }

        // 数据库迁移
        private void MigrateDatabase(SQLiteConnection connection)
        {
            try
            {
                // 检查FilmThickness表是否有ConfigFilePath字段
                var checkColumnQuery = "PRAGMA table_info(FilmThickness)";
                bool hasConfigFilePathColumn = false;

                using (var command = new SQLiteCommand(checkColumnQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var columnName = reader["name"].ToString();
                        if (columnName == "ConfigFilePath")
                        {
                            hasConfigFilePathColumn = true;
                            break;
                        }
                    }
                }

                // 如果没有ConfigFilePath字段，添加它
                if (!hasConfigFilePathColumn)
                {
                    var addColumnQuery = "ALTER TABLE FilmThickness ADD COLUMN ConfigFilePath TEXT";
                    using (var command = new SQLiteCommand(addColumnQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                    System.Diagnostics.Debug.WriteLine("数据库迁移: 已添加ConfigFilePath字段到FilmThickness表");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库迁移失败: {ex.Message}");
            }
        }

        // 记录数据库表结构信息
        private void LogDatabaseStructure()
        {
            System.Diagnostics.Debug.WriteLine("=== 数据库表结构信息 ===");
            System.Diagnostics.Debug.WriteLine("数据库文件路径: " + _dbPath);
            System.Diagnostics.Debug.WriteLine("");

            System.Diagnostics.Debug.WriteLine("表1: PAData (颗粒数据会话表)");
            System.Diagnostics.Debug.WriteLine("  - Id: INTEGER PRIMARY KEY AUTOINCREMENT");
            System.Diagnostics.Debug.WriteLine("  - ModuleType: TEXT NOT NULL (固定值: 'PAData')");
            System.Diagnostics.Debug.WriteLine("  - ParsedFilePath: TEXT (解析的文件路径，Pre & After文件)");
            System.Diagnostics.Debug.WriteLine("  - FullResultData: TEXT (JSON格式，包含完整的Pre/After/Delta颗粒数据，用于散点图绘制)");
            System.Diagnostics.Debug.WriteLine("  - Timestamp: DATETIME (创建时间)");
            System.Diagnostics.Debug.WriteLine("");

            System.Diagnostics.Debug.WriteLine("表2: FilmThickness (膜厚数据会话表)");
            System.Diagnostics.Debug.WriteLine("  - Id: INTEGER PRIMARY KEY AUTOINCREMENT");
            System.Diagnostics.Debug.WriteLine("  - ModuleType: TEXT NOT NULL (固定值: 'FilmThickness')");
            System.Diagnostics.Debug.WriteLine("  - ParsedFilePath: TEXT (解析的文件路径)");
            System.Diagnostics.Debug.WriteLine("  - ConfigFilePath: TEXT (配置文件路径)");
            System.Diagnostics.Debug.WriteLine("  - UserInputValue: REAL (用户输入值)");
            System.Diagnostics.Debug.WriteLine("  - FullResultData: TEXT (JSON格式，膜厚数据)");
            System.Diagnostics.Debug.WriteLine("  - HeatMapData: TEXT (JSON格式，热图数据)");
            System.Diagnostics.Debug.WriteLine("  - Timestamp: DATETIME (创建时间)");
            System.Diagnostics.Debug.WriteLine("");

            System.Diagnostics.Debug.WriteLine("数据保存说明:");
            System.Diagnostics.Debug.WriteLine("  - 颗粒数据: Pre/After/Delta完整数据以JSON格式保存在PAData表的FullResultData字段中");
            System.Diagnostics.Debug.WriteLine("    * 包含所有颗粒的坐标、尺寸等完整信息，用于散点图绘制和数据预览");
            System.Diagnostics.Debug.WriteLine("    * 支持在汇总表格中直接显示小型散点图");
            System.Diagnostics.Debug.WriteLine("    * 预览时可以加载完整数据到PADataView窗口");
            System.Diagnostics.Debug.WriteLine("  - 膜厚数据: 膜厚测量数据保存在FilmThickness表中，包含热图数据");
            System.Diagnostics.Debug.WriteLine("  - 不再使用单独的PAData详情表，所有数据都以JSON格式保存，提高查询效率");
            System.Diagnostics.Debug.WriteLine("========================");
        }

        // 已废弃：颗粒数据现在保存在PAData表的JSON字段中
        [Obsolete("颗粒数据现在保存在PAData表的JSON字段中，不再使用此方法")]
        public void SavePAData(List<PAData> records, string batchId)
        {
            System.Diagnostics.Debug.WriteLine("警告：SavePAData方法已废弃，颗粒数据现在保存在PAData表的JSON字段中");
            // 方法体已移除，因为不再需要保存到PAData表
        }

        // 已废弃：颗粒数据现在保存在PAData表的JSON字段中
        [Obsolete("颗粒数据现在保存在PAData表的JSON字段中，不再使用此方法")]
        public List<PAData> GetPAData(string batchId)
        {
            System.Diagnostics.Debug.WriteLine("警告：GetPAData方法已废弃，颗粒数据现在保存在PAData表的JSON字段中");
            return new List<PAData>(); // 返回空列表
        }

        // 已废弃：颗粒数据现在保存在PAData表的JSON字段中
        [Obsolete("颗粒数据现在保存在PAData表的JSON字段中，不再使用此方法")]
        public List<string> GetAllPABatchIds()
        {
            System.Diagnostics.Debug.WriteLine("警告：GetAllPABatchIds方法已废弃，颗粒数据现在保存在PAData表的JSON字段中");
            return new List<string>(); // 返回空列表
        }

        // 已废弃：颗粒数据现在保存在PAData表的JSON字段中
        [Obsolete("颗粒数据现在保存在PAData表的JSON字段中，不再使用此方法")]
        public List<PAData> GetPADataByTimeRange(DateTime startTime, DateTime endTime)
        {
            System.Diagnostics.Debug.WriteLine("警告：GetPADataByTimeRange方法已废弃，颗粒数据现在保存在PAData表的JSON字段中");
            return new List<PAData>(); // 返回空列表
        }

        public void InsertFullResult(string moduleType, Dictionary<string, object> fullData)
        {
            using (var connection = new SQLiteConnection($"Data Source={_dbPath};Version=3;"))
            {
                connection.Open();

                var fullResultJson = JsonConvert.SerializeObject(fullData["FullResultData"]);

                // 根据模块类型选择对应的表和字段
                string tableName;
                string insertSql;

                if (moduleType == "PAData")
                {
                    tableName = "PAData";
                    insertSql = @"
                        INSERT INTO PAData (
                            ModuleType, ParsedFilePath, FullResultData
                        ) VALUES (
                            @ModuleType, @ParsedFilePath, @FullResultData
                        )";
                }
                else
                {
                    tableName = "FilmThickness";
                    var heatMapJson = JsonConvert.SerializeObject(fullData["HeatMapData"]);
                    insertSql = @"
                        INSERT INTO FilmThickness (
                            ModuleType, ParsedFilePath, ConfigFilePath, UserInputValue, FullResultData, HeatMapData
                        ) VALUES (
                            @ModuleType, @ParsedFilePath, @ConfigFilePath, @UserInputValue, @FullResultData, @HeatMapData
                        )";
                }

                var insertCmd = connection.CreateCommand();
                insertCmd.CommandText = insertSql;
                insertCmd.Parameters.AddWithValue("@ModuleType", moduleType);
                insertCmd.Parameters.AddWithValue("@ParsedFilePath", GetValueOrDefault(fullData, "ParsedFilePath", (string)null));
                insertCmd.Parameters.AddWithValue("@FullResultData", fullResultJson);

                // 只有FilmThickness需要这些字段
                if (moduleType != "PAData")
                {
                    insertCmd.Parameters.AddWithValue("@ConfigFilePath", GetValueOrDefault(fullData, "ConfigFilePath", (string)null));
                    insertCmd.Parameters.AddWithValue("@UserInputValue", GetValueOrDefault(fullData, "UserInputValue", 1.0));
                    var heatMapJson = JsonConvert.SerializeObject(fullData["HeatMapData"]);
                    insertCmd.Parameters.AddWithValue("@HeatMapData", heatMapJson);
                }

                insertCmd.ExecuteNonQuery();

                System.Diagnostics.Debug.WriteLine($"数据已保存到表: {tableName}, 模块类型: {moduleType}");
            }
        }

        public List<Dictionary<string, object>> QueryFullResults(string moduleType = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            var results = new List<Dictionary<string, object>>();

            using (var connection = new SQLiteConnection($"Data Source={_dbPath};Version=3;"))
            {
                connection.Open();

                // 根据模块类型选择对应的表
                string tableName = moduleType == "PAData" ? "PAData" :
                                  moduleType == "FilmThickness" ? "FilmThickness" :
                                  "PAData"; // 默认查询PAData表

                var queryCmd = connection.CreateCommand();
                queryCmd.CommandText = $"SELECT * FROM {tableName} WHERE 1=1";
                if (!string.IsNullOrEmpty(moduleType))
                {
                    queryCmd.CommandText += " AND ModuleType = @ModuleType";
                    queryCmd.Parameters.AddWithValue("@ModuleType", moduleType);
                }
                if (startTime.HasValue)
                {
                    queryCmd.CommandText += " AND Timestamp >= @StartTime";
                    queryCmd.Parameters.AddWithValue("@StartTime", startTime.Value);
                }
                if (endTime.HasValue)
                {
                    queryCmd.CommandText += " AND Timestamp <= @EndTime";
                    queryCmd.Parameters.AddWithValue("@EndTime", endTime.Value);
                }

                queryCmd.CommandText += " ORDER BY Timestamp DESC";
                System.Diagnostics.Debug.WriteLine($"查询表: {tableName}, 模块类型: {moduleType}");

                using (var reader = queryCmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        try
                        {
                            var result = new Dictionary<string, object>
                            {
                                ["Id"] = reader["Id"],
                                ["ModuleType"] = reader["ModuleType"],
                                ["ParsedFilePath"] = reader["ParsedFilePath"],
                                ["Timestamp"] = reader["Timestamp"]
                            };

                            // 只有FilmThickness表才有UserInputValue和ConfigFilePath字段
                            if (tableName == "FilmThickness")
                            {
                                result["UserInputValue"] = reader["UserInputValue"];
                                // 安全地读取ConfigFilePath字段（可能不存在于旧记录中）
                                try
                                {
                                    result["ConfigFilePath"] = reader["ConfigFilePath"];
                                    System.Diagnostics.Debug.WriteLine($"读取ConfigFilePath: ID={reader["Id"]}, ConfigFilePath={reader["ConfigFilePath"]}");
                                }
                                catch (Exception ex)
                                {
                                    result["ConfigFilePath"] = null;
                                    System.Diagnostics.Debug.WriteLine($"读取ConfigFilePath失败: ID={reader["Id"]}, Error={ex.Message}");
                                }
                            }

                            // 安全地反序列化FullResultData
                            var fullResultDataStr = reader["FullResultData"]?.ToString();
                            if (!string.IsNullOrEmpty(fullResultDataStr))
                            {
                                try
                                {
                                    result["FullResultData"] = JsonConvert.DeserializeObject(fullResultDataStr);
                                    System.Diagnostics.Debug.WriteLine($"成功反序列化FullResultData，会话ID={reader["Id"]}");
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"反序列化FullResultData失败，会话ID={reader["Id"]}: {ex.Message}");
                                    result["FullResultData"] = null;
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"FullResultData为空，会话ID={reader["Id"]}");
                                result["FullResultData"] = null;
                            }

                            // 只有FilmThickness表才有HeatMapData字段
                            if (tableName == "FilmThickness")
                            {
                                var heatMapDataStr = reader["HeatMapData"]?.ToString();
                                if (!string.IsNullOrEmpty(heatMapDataStr))
                                {
                                    try
                                    {
                                        result["HeatMapData"] = JsonConvert.DeserializeObject(heatMapDataStr);
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"反序列化HeatMapData失败，会话ID={reader["Id"]}: {ex.Message}");
                                        result["HeatMapData"] = null;
                                    }
                                }
                                else
                                {
                                    result["HeatMapData"] = null;
                                }
                            }

                            results.Add(result);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"处理会话记录失败: {ex.Message}");
                        }
                    }
                }
                return results;
            }
        }
        
        public bool DeleteSession(int sessionId)
        {
            using (var connection = new SQLiteConnection($"Data Source={_dbPath};Version=3;"))
            {
                connection.Open();

                // 先查询会话的模块类型以确定删除哪个表
                var queryCmd = connection.CreateCommand();
                queryCmd.CommandText = "SELECT ModuleType FROM PAData WHERE Id = @Id UNION SELECT ModuleType FROM FilmThickness WHERE Id = @Id";
                queryCmd.Parameters.AddWithValue("@Id", sessionId);

                string moduleType = null;
                using (var reader = queryCmd.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        moduleType = reader["ModuleType"].ToString();
                    }
                }

                if (string.IsNullOrEmpty(moduleType))
                {
                    System.Diagnostics.Debug.WriteLine($"未找到会话ID={sessionId}");
                    return false;
                }

                // 根据模块类型选择对应的表
                string tableName = moduleType == "PAData" ? "PAData" : "FilmThickness";

                var deleteCmd = connection.CreateCommand();
                deleteCmd.CommandText = $"DELETE FROM {tableName} WHERE Id = @Id";
                deleteCmd.Parameters.AddWithValue("@Id", sessionId);

                int rowsAffected = deleteCmd.ExecuteNonQuery();
                System.Diagnostics.Debug.WriteLine($"从表{tableName}删除会话ID={sessionId}，影响行数={rowsAffected}");
                return rowsAffected > 0;
            }
        }
    }
}
